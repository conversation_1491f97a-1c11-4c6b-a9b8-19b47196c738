import { ISignupPayload } from '../features/Authentication/types'

export class NullishUser implements ISignupPayload {
  firstName: string
  lastName: string
  email: string
  phoneNumber: string
  terms: boolean
  captcha: string
  title: string

  constructor() {
    this.firstName = ''
    this.lastName = ''
    this.email = ''
    this.phoneNumber = ''
    this.captcha = ''
    this.terms = true
    this.title = ''
  }

  get getData(): NullishUser {
    return this
  }
}
