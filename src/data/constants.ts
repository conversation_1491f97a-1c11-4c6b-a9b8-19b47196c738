export const GeneralNavbarActiveLink =
  'text-[16px] flex gap-x-2 items-center text-primary whitespace-nowrap active leading-8 font-[500]';
export const GeneralNavbarNormalLink = `flex items-center gap-x-2 whitespace-nowrap text-[16px] text-white hover:text-primary font-[500] leading-8 cursor-pointer 
transition duration-500 ease-in-out`;
export const MobileGeneralNavbarNormalLink = `flex items-center gap-x-2 whitespace-nowrap text-[16px] text-black hover:text-primary font-[500] leading-8 cursor-pointer 
transition duration-500 ease-in-out`;

export const userRole = {
  student: 'STUDENT',
  faculty: 'FACULTY',
  user: 'USER',
  practitioner: 'PRACTITIONER',
  benefactor: 'BENEFACTOR',
  venturec_staff: 'VENTUREC_STAFF',
  venturec_admin: 'VENTUREC_ADMIN',
} as const;
export const projectLevelUserRole = {
  projectAdmin: 'PROJECT_ADMIN',
  projectMember: 'PROJECT_MEMBER',
} as const;
export const teamLevelUserRole = {
  teamLead: 'TEAM_LEAD',
  teamMember: 'TEAM_MEMBER',
} as const;

export const problemCategory = {
  education: 'Education And Skills',
  healthcare: 'Healthcare',
  foodSupplyChain: 'Food Security',
  crossBorderTrade: 'Cross-Border Trade',
} as const;

export const categoryByStatus = {
  all: 'ALL',
  mostPopular: 'MOST_POPULAR',
  recentlyAdded: 'RECENTLY_ADDED',
} as const;
export const group = {
  course: 'COURSE',
  sdg: 'SDG',
  iac: 'UIAC24',
  industry: 'INDUSTRY',
  skill: 'SKILL',
  faculty: 'FACULTY',
} as const;

export const courseCategory = {
  backend: 'Backend Software Engineering',
  frontend: 'Frontend Software Engineering',
  mobile: 'Mobile App Development',
  ux: 'User Experience Design',
  leadership: 'Product Leadership',
  dataScience: 'Data Science',
  qa: 'IT Quality Assurance',
  devopsAndCybersecurity: 'IT DevOps & CyberSecurity',
} as const;

export const title = {
  Mr: 'Mr',
  Ms: 'Ms',
  Mrs: 'Mrs',
  Prof: 'Prof',
  Dr: 'Dr',
} as const;

export const forbiddenEmails = {
  gmail: 'gmail.com',
  yahoo: 'yahoo.com',
  hotmail: 'hotmail.com',
  aol: 'aol.com',
} as const;

export const subscription = {
  basic: 'BASIC',
  innovator: 'INNOVATOR',
  accelerator: 'ACCELERATOR',
  institutional: 'INSTITUTION',
} as const;
export const subscriptionStatus = {
  active: 'ACTIVE',
  inactive: 'INACTIVE',
  expired: 'EXPIRED',
  cancelled: 'CANCELLED',
} as const;

export const serviceType = {
  canonin: 'CANONIN',
  peswana: 'PESWANA',
  upivotal: 'UPIVOTAL',
} as const;

export const notificationRoute = {
  teamMemberInviteRequest: 'team-member-invite-request',
  joinTeamRequest: 'join-team-request',
  externalTeamProjectInvite: 'external-team-project-invite',
  joinProjectRequest: 'join-project-request',
  subscriptionSponsorshipRequest: 'subscription-sponsorship-request',
  joinChannelRequest: 'join-channel-request',
  inviteOthersChannelRequest: 'invite-others-channel-request',
} as const;
export const taskProgressStatus = {
  To_Do: 'TO_DO',
  In_Progress: 'IN_PROGRESS',
  Done: 'DONE',
  Approved: 'APPROVED',
  Cancelled: 'CANCELLED',
  Revised: 'REVISED',
} as const;

export const projectProgressStatus = {
  To_Do: 'TO_DO',
  Approved: 'APPROVED',
  Approved_With_Funding: 'APPROVED_WITH_FUNDING',
  In_Progress: 'IN_PROGRESS',
  Done: 'DONE',
  Paused: 'PAUSED',
  Cancelled: 'CANCELLED',
  An_Idea: 'AN_IDEA',
  Requested: 'REQUESTED',
  Assessed: 'ASSESSED',
  Pending_Approval: 'PENDING_APPROVAL',
} as const;

export const subscriptionLevels = {
  BASIC: 1,
  INNOVATOR: 2,
  ACCELERATOR: 3,
  INSTITUTION: 4,
};

export const grantApplicationStatus = {
  PENDING: 'PENDING',
  SAVED: 'SAVED',
  DECLINED: 'DECLINED',
  ISSUED: 'ISSUED',
} as const;

export const grantStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
} as const;

export const grantFundingStatus = {
  FUNDED: 'FUNDED',
  UNFUNDED: 'UNFUNDED',
} as const;
export const goalTrafficLight = {
  GREEN: 'GREEN',
  YELLOW: 'YELLOW',
  RED: 'RED',
} as const;

export const projectTag = {
  ADMITTED: 'ADMITTED',
  UN_ADMITTED: 'UN_ADMITTED',
} as const;
export const visibility = {
  PRIVATE: 'PRIVATE',
  PUBLIC: 'PUBLIC',
} as const;
export const conversationVisibility = {
  PRIVATE: 'PRIVATE',
  PUBLIC: 'PUBLIC',
  WORKPLACE: 'WORKPLACE',
} as const;
export const visibilityLabels = {
  PUBLIC: 'Public',
  PRIVATE: 'Private (My Institution Only)',
} as const;

export const categoryType = {
  sdg: 'SDG',
  course: 'COURSE',
  industry: 'INDUSTRY',
  skill: 'SKILL',
  uiac4: 'UIAC24',
} as const;

export const universityAdminLevels = {
  UNIVERSITY: 'UNIVERSITY',
  FACULTY: 'FACULTY',
  DEPARTMENT: 'DEPARTMENT',
} as const;
