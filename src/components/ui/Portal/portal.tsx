import React, { ReactNode } from 'react';
import ReactDOM from 'react-dom';

interface PortalProps {
  children: ReactNode;
}

const Portal: React.FC<PortalProps> = ({ children }) => {
  const portalRoot = document.getElementById('portal-root');
  if (!portalRoot) {
    console.error(
      'The portal root element (#portal-root) is not found in the DOM.',
    );
    return null;
  }
  return ReactDOM.createPortal(children, portalRoot);
};

export default Portal;
