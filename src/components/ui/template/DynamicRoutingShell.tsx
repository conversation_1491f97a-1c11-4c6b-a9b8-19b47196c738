import { ReactNode, useEffect } from 'react';
import { Navigate, useParams } from 'react-router-dom';

import { useUserContext } from '../../../context/user/UserContext';
import { userRole } from '../../../data/constants';
import WelcomeToDashboard from '../CommonWidget/WelcomeToDashboard';
import { useAppContext } from '@/context/event/AppEventContext';

type CurrentComponentProps = {
  student?: ReactNode;
  faculty?: ReactNode;
  shared?: ReactNode;
  practitioner?: ReactNode;
  benefactor?: ReactNode;
};
export default function DynamicRoutingShell({
  faculty,
  student,
  shared,
  practitioner,
  benefactor,
}: CurrentComponentProps) {
  const { accountType } = useParams();
  const {
    hasFacultyRole,
    hasStudentRole,
    hasPractitionerRole,
    hasBenefactorRole,
    hasAtleastOneAccountType,
  } = useUserContext();
  const { setShowModalHandler } = useAppContext();

  useEffect(() => {
    if (!hasAtleastOneAccountType) {
      setShowModalHandler('ProceedToOnboardModal');
    }
  }, [hasAtleastOneAccountType]);

  if (!hasAtleastOneAccountType) {
    return <Navigate to={'/conversations/feed'} />;
  }

  if (
    accountType?.toLowerCase() === userRole.student.toLowerCase() &&
    hasStudentRole
  ) {
    return student || shared || <WelcomeToDashboard />;
  }
  if (
    accountType?.toLowerCase() === userRole.faculty.toLowerCase() &&
    hasFacultyRole
  ) {
    return faculty || shared || <WelcomeToDashboard />;
  }
  if (
    accountType?.toLowerCase() === userRole.practitioner.toLowerCase() &&
    hasPractitionerRole
  ) {
    return practitioner || shared || <WelcomeToDashboard />;
  }
  if (
    accountType?.toLowerCase() === userRole.benefactor.toLowerCase() &&
    hasBenefactorRole
  ) {
    return benefactor || shared || <WelcomeToDashboard />;
  }
  return <WelcomeToDashboard />;
}
