import React from 'react';
import { Outlet } from 'react-router-dom';

import Error from '../CommonWidget/Error';
import AppShell from './AppShell';

export class AppOutlet extends React.Component<
  {
    header?: React.ComponentType;
    footer?: React.ComponentType;
    sidebar?: React.ComponentType;
  },
  { error: null | undefined | boolean | any; errorInfo: any }
> {
  constructor(props: {
    header?: React.ComponentType;
    footer?: React.ComponentType;
    sidebar?: React.ComponentType;
  }) {
    super(props);
    this.state = { error: '', errorInfo: '' };
  }

  componentDidCatch(error: any, errorInfo: any) {
    console.log({ error, errorInfo });
    this.setState({
      error: error,
      errorInfo: errorInfo,
    });
  }

  render() {
    const { header: Header, footer: Footer, sidebar: Sidebar } = this.props;

    return this.state.error ? (
      <Error />
    ) : (
      <AppShell header={Header} footer={Footer} sidebar={Sidebar}>
        <Outlet />
      </AppShell>
    );
  }
}
