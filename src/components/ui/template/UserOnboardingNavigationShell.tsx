import { useKeycloak } from '@react-keycloak/web';
import { useEffect } from 'react';
import { useUserContext } from '../../../context/user/UserContext';
import { Helper } from '../../../utils/helpers';
import { AppOutlet } from './AppOutlet';
import { isDevEnvironment } from '@/features/UserOnboarding/utils/helper';

export default function UserOnboardingNavigationShell() {
  const { keycloak } = useKeycloak();
  const { isLoggedIn } = useUserContext();
  useEffect(() => {
    if (!isLoggedIn && !isDevEnvironment()) {
      Helper.setLocalUserOnboardingLevel(1);
      keycloak.login({ redirectUri: window.location.href });
    }
  }, [isLoggedIn]);
  return <AppOutlet />;
}
