import ReactPlayer from 'react-player/youtube';

import { playIcon } from '../../../features/Home/assets/icons';
import { useState } from 'react';
export default function Player({ url }: { url: string }) {
  const [isPlaying, setIsPlaying] = useState(false);
  return (
    <ReactPlayer
      className="absolute left-0 top-0 rounded-[10px]"
      url={url || ''}
      controls
      playsinline
      height={'100%'}
      width={'100%'}
      light={!isPlaying}
      playing={isPlaying}
      playIcon={
        <div className="flex w-full max-w-[138px] flex-col rounded-3xl border-[1.447px] border-solid border-[color:var(--light-orange-medium,#FFECE3)] bg-white p-2">
          <button
            className="flex items-stretch gap-2"
            onClick={() => setIsPlaying(true)}
          >
            <img
              loading="lazy"
              src={playIcon}
              className="aspect-[1.04] w-6 max-w-full shrink-0 overflow-hidden object-contain object-center"
            />
            <p className="my-auto self-center whitespace-nowrap text-center text-sm leading-5 text-blackFive">
              Play clip
            </p>
          </button>
        </div>
      }
    />
  );
}
