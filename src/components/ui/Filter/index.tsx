import Select from 'react-select'

import { useCustomMedia } from '../../../hooks/useCustomMedia'
import {
  selectStyles,
  smallMediaSelectStyles,
} from '../../../lib/reactSelect/react-select-style'
import {
  CategoryByStatusType,
  ProblemStatementCategoryNameType,
} from '../../../types'

export default function Filter<
  T extends ProblemStatementCategoryNameType | CategoryByStatusType,
>({
  options,
  onChange,
  label = 'Sort by:',
}: {
  options: { label: string; value: T }[]
  onChange?: (value: T) => void
  label?: string
}) {
  const { screenSize } = useCustomMedia()
  return (
    <div className="w-full max-w-[457px] sm:max-w-[300px] hidden">
      <div className="flex items-center gap-x-3 ">
        <p className="whitespace-nowrap text-grayTen hidden sm:block">
          {label}
        </p>
        <Select
          className="w-full rounded-none"
          styles={screenSize > 768 ? selectStyles : smallMediaSelectStyles}
          placeholder="Sort By..."
          options={options}
          onChange={(option) => {
            option && onChange && onChange(option.value)
          }}
          isClearable
          menuPortalTarget={document.body}
          menuPosition={'absolute'}
          menuPlacement={'auto'}
          menuShouldScrollIntoView={false}
        />
      </div>
    </div>
  )
}
