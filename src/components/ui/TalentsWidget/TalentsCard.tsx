import { useNavigate } from 'react-router-dom';
import { createContext, forwardRef, ReactNode, Ref, useContext } from 'react';

import { profilePlaceholder } from '../../../assets/images';

import Button from '../ButtonComponent';
import { useAppContext } from '../../../context/event/AppEventContext';
import { Talent } from '@/types';
import { Helper } from '@/utils/helpers';

interface Props {
  children?: ReactNode;
  talent?: Partial<Talent>;
}

const Context = createContext({} as Partial<Talent>);

export default forwardRef(function TalentsCard(
  { children, talent }: Props,
  ref: Ref<HTMLDivElement>,
) {
  const { user, universityDTO, skills, country } = talent || {};

  return (
    <Context.Provider value={{ user, universityDTO, country, skills }}>
      <div className="relative flex w-[19%] min-w-[244px] items-stretch">
        <div className="flex w-full flex-col self-stretch rounded-[4px] border-[2.2px] border-grayNineTeen bg-white p-2  max-md:w-full">
          <div ref={ref}>
            <img
              loading="lazy"
              src={user?.profilePicture || profilePlaceholder}
              className="aspect-[1.23] max-h-[388.68px] w-[210px] max-w-full self-start overflow-hidden object-cover object-center"
            />
          </div>
          <p className="mt-2.5 text-sm font-bold">
            {user?.firstName} {user?.lastName}
          </p>
          {children}
        </div>
      </div>
    </Context.Provider>
  );
});

const SeeMore = ({ action }: { action: () => void }) => {
  return (
    <Button
      onClick={() => action()}
      className="w-full max-w-[140px] border  border-solid border-black 
bg-white text-black hover:border-[color:transparent] hover:bg-primary hover:text-white"
    >
      <p> See more</p>
    </Button>
  );
};

export const SeeMoreTalentButton = () => {
  const navigate = useNavigate();
  const { currentAccountType, setShowModalHandler } = useAppContext();
  const { user } = useContext(Context);

  return (
    <SeeMore
      action={() => {
        navigate(`/${currentAccountType}/talent/${user?.userId}`);
        setShowModalHandler('');
      }}
    />
  );
};
export const TalentCountry = () => {
  const { country } = useContext(Context);

  return (
    <div className="mt-2 whitespace-normal text-wrap break-words rounded bg-[#EAEAEA] px-2 py-1 text-[10px] font-semibold text-subText">
      Country: {country || 'N/A'}
    </div>
  );
};

export const TalentInstitution = () => {
  const { universityDTO } = useContext(Context);

  return (
    <div className="mt-2 whitespace-normal text-wrap break-words rounded bg-[#EAEAEA] px-2 py-1 text-[10px] font-semibold text-subText">
      Instituition: {Helper.truncateText(universityDTO?.name, 44) || 'N/A'}
    </div>
  );
};

export const TalentSkills = () => {
  const { skills } = useContext(Context);

  const computedSkills = Helper.truncateText(
    (skills || [])
      .map(skill => skill?.name)
      .filter(Boolean)
      .join(', '),
    44,
  );

  return (
    <div className="mt-2 whitespace-normal text-wrap break-words rounded bg-[#EAEAEA] px-2 py-1 text-[10px] font-semibold text-subText">
      Skills: {computedSkills || 'N/A'}
    </div>
  );
};
