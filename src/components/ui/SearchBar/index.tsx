import { SearchIcon } from '../../../assets/icons';
import { cn } from '../../../lib/twMerge/cn';
import { Helper } from '../../../utils/helpers';

export default function SearchBar({
  setSearchValue = () => {},
  containerCls,
  className,
  defaultValue,
}: {
  defaultValue?: string;
  containerCls?: string;
  className?: string;
  setSearchValue?: (value: any) => void;
}) {
  const handleChange = Helper.debounce((e: any) => {
    setSearchValue(e.target.value);
  }, 1000);
  return (
    <>
      <div
        className={cn(`relative h-[37px] w-full max-w-[450px] ${containerCls}`)}
      >
        <SearchIcon className="absolute left-[8px] top-[50%] h-[20px] w-[20px] -translate-y-[50%]" />
        <input
          type="search"
          className={cn(`h-full w-full flex-1 border-[1px] border-graySeven bg-grayTwo
            pl-[40px] text-blackFive outline-none transition duration-500 ease-in-out focus:!border-graySeven ${className}`)}
          placeholder="Search by keyword"
          onChange={e => handleChange(e)}
          defaultValue={defaultValue}
        />
      </div>
    </>
  );
}
