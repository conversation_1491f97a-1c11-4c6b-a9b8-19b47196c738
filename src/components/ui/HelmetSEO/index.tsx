import { Helmet } from 'react-helmet-async';

interface HelmetSEOProps {
  title: string;
  description?: string;
  image?: string;
  url?: string;
  type?: string;
}
const HelmetSEO = ({
  title = 'uPIVOTAL',
  description = 'Learn and Connect to Solve Real-World Challenges. Collaborate with tertiary institutions and industry experts globally to deliver innovative solutions.',
  image = 'https://peswana-public-dev.s3.amazonaws.com/logo.JPEG',
  url = 'https://dev.upivotal.com/',
  type = 'website',
}: HelmetSEOProps) => {
  return (
    <Helmet prioritizeSeoTags>
      <title>{title}</title>
      <meta name="description" content={description} />
      <link rel="canonical" href={url} />

      {/* Open Graph meta tags */}
      {title && <meta property="og:title" content={title} />}
      {description && <meta property="og:description" content={description} />}
      {image && <meta property="og:image" content={image} />}
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      {url && <meta property="og:url" content={url} />}
      <meta property="og:type" content={type} />

      {/* Twitter Card for better engagement on Twitter */}
      <meta name="twitter:card" content="summary_large_image" />
      {title && <meta name="twitter:title" content={title} />}
      {description && <meta name="twitter:description" content={description} />}
      {image && <meta name="twitter:image" content={image} />}
    </Helmet>
  );
};
export default HelmetSEO;
