import { forwardRef, ReactNode, Ref } from 'react'

import { useGetCourseCategories } from '../../../hooks/apiQueryHooks/eduQueryHooks'
import { useCustomMedia } from '../../../hooks/useCustomMedia'
import CourseCategoryCardPlaceholder from '../CategoryWidget/Courses/CourseCategoryCardPlaceholder'
import FilterNavbar from '../CommonWidget/FilterNavbar'
import SlideController from '../CommonWidget/SlideController'

type Props = {
  children: ReactNode
  tabQueryName: string
  isLoading?: boolean
}

export default forwardRef(function UpickCategoryCase(
  { children, tabQueryName, isLoading }: Props,
  ref: Ref<HTMLDivElement>
) {
  const { screenSize } = useCustomMedia()

  const { data } = useGetCourseCategories({
    staleTime: 1000 * 60 * 60 * 24,
    cacheTime: 1000 * 60 * 60 * 25,
  })
  return (
    <div className="items-stretch flex flex-col mb-16">
      <SlideController
        dataLength={(data?.data || [])?.length}
        isMobile={screenSize < 768}
        small
      >
        {(data?.data || [])?.length > 0 ? (
          <FilterNavbar tabQueryName={tabQueryName} list={data?.data || []} />
        ) : (
          <></>
        )}
      </SlideController>
      <div
        ref={ref}
        className="overflow-x-auto sm:overflow-x-hidden w-full max-md:max-w-full"
      >
        {isLoading ? (
          <div className="gap-3 flex max-md:items-stretch pb-4 ">
            {Array.from({ length: 4 }, (_, index) => (
              <CourseCategoryCardPlaceholder key={index} />
            ))}
          </div>
        ) : (
          children
        )}
      </div>
    </div>
  )
})
