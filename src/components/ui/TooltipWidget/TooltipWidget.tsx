import { motion } from 'framer-motion';
import { ReactNode, useCallback, useLayoutEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { usePopper } from 'react-popper';

import { cn } from '../../../lib/twMerge/cn';

export const TooltipWidget = ({
  parentRef,
  children,
  className,
  show,
  caseClassName,
}: {
  parentRef: HTMLDivElement | null;
  children?: ReactNode;
  className?: string;
  caseClassName?: string;
  show?: boolean;
}) => {
  const [hideArrow, setHideArrow] = useState(true);
  const [tooltipRef, setTooltipRef] = useState<HTMLDivElement | null>(null);

  const { styles, attributes, update } = usePopper(parentRef!, tooltipRef!, {
    placement: 'auto',
    modifiers: [
      {
        name: 'arrow',
        options: {
          padding: 5,
        },
      },
      {
        name: 'offset',
        options: {
          offset: [0, 5],
        },
      },
    ],
  });

  const handleAnimationStart = useCallback(() => {
    setHideArrow(true);
  }, []);

  const handleAnimationEnd = useCallback(() => {
    setHideArrow(false);
  }, []);

  useLayoutEffect(() => {
    if (show) {
      handleAnimationEnd();
      update && update();
    } else handleAnimationStart();
  }, [show]);

  return createPortal(
    <div
      id="tooltip"
      ref={setTooltipRef}
      style={{ ...styles.popper, zIndex: 10000 }}
      {...attributes.popper}
      className={caseClassName}
    >
      <div
        data-popper-arrow
        id="arrow"
        className={hideArrow ? 'hidden' : 'block'}
        style={styles.arrow}
      ></div>

      {show && (
        <motion.div
          exit={{ opacity: 0 }}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.2, ease: 'easeInOut' }}
          onAnimationStart={handleAnimationStart}
          onAnimationEnd={handleAnimationEnd}
          onAnimationComplete={handleAnimationEnd}
          onClick={e => {
            e.preventDefault();
            e.stopPropagation;
          }}
          className={cn(
            `max-h-[400px] overflow-auto bg-white p-4 ${className || ''}`,
          )}
        >
          {children}
        </motion.div>
      )}
    </div>,
    document.body,
  );
};

type Props = { text: string; className?: string; children: React.ReactNode };

export function ToolTipWrapper({ text, className, children }: Props) {
  const [visible, setVisible] = useState(false);
  const [referenceElement, setReferenceElement] =
    useState<HTMLDivElement | null>(null);
  const [tooltipElement, setTooltipElement] = useState<HTMLDivElement | null>(
    null,
  );
  const { styles, attributes, update } = usePopper(
    referenceElement,
    tooltipElement,
    {
      placement: 'top',
    },
  );

  const handleMouseEnter = () => setVisible(true);
  const handleMouseLeave = () => setVisible(false);

  useLayoutEffect(() => {
    update && update();
  }, [visible]);

  return (
    <div
      className={cn('relative inline-block', className)}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      ref={setReferenceElement}
    >
      {children}
      {createPortal(
        <div
          id="tooltip"
          ref={setTooltipElement}
          className="pointer-events-none z-[2]"
          style={{ ...styles.popper }}
          {...attributes.popper}
        >
          {visible && (
            <motion.div
              exit={{ opacity: 0 }}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.2, ease: 'easeInOut' }}
              className="h-[30px] rounded-lg bg-primary px-3 py-2 opacity-100 duration-300 ease-out"
            >
              <p className="whitespace-nowrap text-center text-xs font-medium leading-4 tracking-normal text-white">
                {text}
              </p>
            </motion.div>
          )}
          <motion.div
            exit={{ opacity: 0 }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.2, ease: 'easeInOut' }}
            data-popper-arrow
            id="arrow"
            className={!visible ? 'hidden' : 'block'}
            style={styles.arrow}
          />
        </div>,
        document.body,
      )}
    </div>
  );
}
