import {
  Children,
  cloneElement,
  FunctionComponentElement,
  isValidElement,
  ReactElement,
  Ref,
  useEffect,
  useRef,
} from 'react';

import { ArrowRightTailless } from '../../../assets/icons';
import { cn } from '../../../lib/twMerge/cn';

type Props = {
  children: FunctionComponentElement<{ ref: Ref<HTMLDivElement> }>;
  isMobile?: boolean;
  dataLength?: number;
  small?: boolean;
  onEndReached?: () => void;
};

const arrowStyle = `group-hover:opacity-100 opacity-0  duration-300 ease-out  absolute -translate-y-[50%] top-[40%] 
w-[50px] h-[50px] rounded-[4px] bg-white shadow-lg 
flex items-center justify-center cursor-pointer`;

export default function SlideController({
  children,
  isMobile = false,
  dataLength = 0,
  small,
  onEndReached = () => {},
}: Props) {
  const containerRef = useRef<HTMLDivElement[]>([]);

  const scrollAmount = 400; // Adjust this value as needed

  const scrollLeft = () => {
    if (containerRef.current) {
      const currentScrollPosition = containerRef.current[0].scrollLeft;
      containerRef.current[0].scrollTo({
        left: currentScrollPosition - scrollAmount,
        behavior: 'smooth',
      });
    }
  };

  const scrollRight = () => {
    if (containerRef.current) {
      const currentScrollPosition = containerRef.current[0].scrollLeft;
      containerRef.current[0].scrollTo({
        left: currentScrollPosition + scrollAmount,
        behavior: 'smooth',
      });
    }
  };

  useEffect(() => {
    const checkIfEndReached = () => {
      if (containerRef.current) {
        const { scrollLeft, scrollWidth, clientWidth } =
          containerRef.current[0];
        if (scrollLeft + clientWidth >= scrollWidth && onEndReached) {
          onEndReached();
        }
      }
    };

    const container = containerRef.current[0];
    if (container) {
      container.addEventListener('scroll', checkIfEndReached);
    }

    // Cleanup listener
    return () => {
      if (container) {
        container.removeEventListener('scroll', checkIfEndReached);
      }
    };
  }, [onEndReached]);

  return (
    <div className={`group relative ${isMobile ? '' : 'px-8'}`}>
      {dataLength > 0 && !isMobile && (
        <div
          className={cn(
            `${arrowStyle} ${
              small ? '-left-[5px] top-[40%] h-[24px] w-[24px]' : '-left-[24px]'
            }`,
          )}
          onClick={scrollLeft}
        >
          <ArrowRightTailless
            className={`rotate-180 ${
              small ? 'h-[16px] w-[16px]' : 'h-[32px] w-[32px]'
            } fill-primary`}
          />
        </div>
      )}
      {Children.map(
        children,
        (child: ReactElement<{ ref: Ref<HTMLDivElement> }>, index) => {
          if (isValidElement(child)) {
            return cloneElement(child, {
              ref: (ref: HTMLDivElement) => (containerRef.current[index] = ref),
            });
          }
          return child;
        },
      )}
      {dataLength > 0 && !isMobile && (
        <div
          className={cn(
            `${arrowStyle}  ${
              small
                ? '-right-[5px] top-[40%] h-[24px] w-[24px]'
                : '-right-[24px]'
            }`,
          )}
          onClick={scrollRight}
        >
          <ArrowRightTailless
            className={`${
              small ? 'h-[16px] w-[16px]' : 'h-[32px] w-[32px]'
            } fill-primary`}
          />
        </div>
      )}
    </div>
  );
}
