import { HTMLAttributes, ReactNode } from 'react'
interface Pro<PERSON> extends HTMLAttributes<HTMLElement> {
  children: ReactNode
  loaded: boolean
  lazyBackgroundImage?: string
}
export default function LazyLoadImageContainer({
  children,
  loaded,
  lazyBackgroundImage,
  className,
  ...rest
}: Props) {
  return (
    <div
      style={{ backgroundImage: `url( ${loaded ? '' : lazyBackgroundImage})` }}
      className={`bg-no-repeat bg-cover  ${className}`}
      {...rest}
    >
      {children}
    </div>
  )
}
