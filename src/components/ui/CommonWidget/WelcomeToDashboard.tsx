import { Link } from 'react-router-dom';

import { useAppContext } from '../../../context/event/AppEventContext';
import { useUserContext } from '../../../context/user/UserContext';
import { userRole } from '../../../data/constants';
import bgImage from '../../../features/WatchVideo/assets/images/rectangleFour.png';
import { UserRoleType } from '../../../types';
import { Helper } from '../../../utils/helpers';

export default function WelcomeToDashboard() {
  const { data, roles } = useUserContext();
  const { setCurrentAccountTypeHandler, setIsUserAccountTypeChoiceHandler } =
    useAppContext();
  return (
    <div
      style={{ backgroundImage: `url(${bgImage})` }}
      className="fixed inset-0 z-100 mx-auto flex h-screen max-w-full 
        items-center justify-center overflow-hidden bg-white bg-cover"
    >
      <div>
        <h3 className="mb-3 text-center text-[28px] font-semibold text-black">
          Welcome {data?.user?.firstName}!
        </h3>
        <p className="mx-auto w-full max-w-[500px] text-center text-[16px]">
          We're excited to have you on board! Your journey with us begins here
        </p>
        <div className="mt-8">
          <p className="text-center text-[14px]">
            Proceed to{' '}
            {roles?.map(accountType => {
              if (accountType === userRole.user) return null;
              return (
                <Link
                  className="text-primary"
                  key={accountType}
                  to={`/${accountType.toLowerCase()}/dashboard-overview`}
                >
                  <li
                    onClick={() => {
                      setIsUserAccountTypeChoiceHandler(true);
                      setCurrentAccountTypeHandler(accountType as UserRoleType);
                    }}
                    className="mb-2 border-b-black px-4 py-1 hover:bg-gray-5 hover:text-primary"
                  >
                    {Helper.capitalizeString(accountType)} Dashboard
                  </li>
                </Link>
              );
            })}
          </p>
        </div>
      </div>
    </div>
  );
}
