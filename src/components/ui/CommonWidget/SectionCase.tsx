import { forwardRef, ReactNode, Ref, useState } from 'react';

import { PaginationType } from '../../../types';
import ViewMore from './ViewMore';
import PaginationNavigator from '../PaginationNavigator/PaginationNavigator';
import { usePaginationHandler } from '@/hooks/usePaginationHandler';

type Props = {
  descriptionText?: ReactNode;
  isAvailableProjectVariant?: boolean;
  children: ReactNode;
  route?: () => void;
  pagination?: PaginationType;
  isLoading?: boolean;
  extraHeaderAction?: ReactNode;
};

export default forwardRef(function SectionCase(
  {
    descriptionText,
    children,
    route,
    pagination,
    isLoading,
    extraHeaderAction,
  }: Props,
  ref: Ref<HTMLDivElement>,
) {
  const { currentPage, handlePageChange } = usePaginationHandler();
  const [start, setStart] = useState(false);
  return (
    <section
      onMouseEnter={() => setStart(true)}
      onMouseLeave={() => setStart(false)}
      className="flex flex-col items-stretch pb-16"
    >
      <div className="flex flex-wrap-reverse items-center justify-between gap-x-2 gap-y-8">
        {descriptionText && (
          <header
            className={`self-stretch text-[20px] font-semibold capitalize leading-10 tracking-tight text-neutral-800`}
          >
            {descriptionText}
          </header>
        )}
        <div className="flex gap-4">
          {extraHeaderAction}
          {route && (
            <div className="flex shrink-0" onClick={route}>
              <ViewMore start={start} />
            </div>
          )}
        </div>
      </div>
      <div
        ref={ref}
        className="w-full overflow-x-auto max-md:max-w-full sm:overflow-x-hidden"
      >
        {children}
      </div>
      {pagination && (
        <div className="mt-4">
          <PaginationNavigator
            isLoading={isLoading || false}
            currentPage={currentPage}
            pagination={pagination}
            handlePageChange={page => handlePageChange(page)}
          />
        </div>
      )}
    </section>
  );
});
