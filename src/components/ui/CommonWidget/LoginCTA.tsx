import { useKeycloak } from '@react-keycloak/web';

import { useAppContext } from '../../../context/event/AppEventContext';
import Button from '../ButtonComponent';
import { mapCurrentAccountTypeToRedirectUrlProp } from '@/utils/helpers/mapCurrentAccountTypeToRedirectUrlProp';

export default function LoginCTA() {
  const { keycloak } = useKeycloak();
  const { currentAccountType } = useAppContext();
  return (
    <div
      className="absolute bottom-[7.6%] flex w-full max-w-[400px] 
    flex-wrap items-center justify-center gap-2 px-2"
    >
      <p className="text-[18px] font-[500] text-white">
        Already have an account?
      </p>
      <Button
        type="button"
        onClick={() => {
          keycloak.login(
            mapCurrentAccountTypeToRedirectUrlProp(currentAccountType),
          );
        }}
        className="mx-auto w-full max-w-[122px]  bg-white "
      >
        <p className="text-[16px] font-[700] text-primary">Sign In</p>
      </Button>
    </div>
  );
}
