import { forwardRef, Ref } from 'react';

import { useHandleQueryParams } from '../../../hooks/useHandleQueryParams';

type Props = { list: any[]; tabQueryName: string };

export default forwardRef(function forwardRefFilterNavbar(
  { list, tabQueryName }: Props,
  ref: Ref<HTMLDivElement>,
) {
  const { handleQuery, query } = useHandleQueryParams();
  return (
    <div
      ref={ref}
      className="mb-4 flex items-center justify-between overflow-x-auto sm:overflow-x-hidden"
    >
      <div
        onClick={() => {
          handleQuery({ [tabQueryName]: undefined });
        }}
        className="flex-1 cursor-pointer px-4 py-2"
      >
        <p
          className={`w-full ${
            query.get(tabQueryName) === null ? 'bg-primary text-white' : ''
          } whitespace-nowrap rounded-[24px] bg-grayFifteen px-5 py-3 text-center text-[10px] font-[600] uppercase text-subText `}
        >
          All
        </p>
      </div>
      {(list || []).map((element, index) => (
        <div
          onClick={() => {
            handleQuery({ [tabQueryName]: element?.categoryRef });
          }}
          key={index}
          className="flex-1 cursor-pointer px-4 py-2"
        >
          <p
            className={`w-full ${
              query.get(tabQueryName) === element?.categoryRef
                ? 'bg-primary text-white'
                : ''
            } grayFifteen whitespace-nowrap rounded-[24px] bg-grayFifteen px-5 py-3 text-center  text-[10px] font-[600] uppercase text-subText`}
          >
            {element?.categoryName}
          </p>
        </div>
      ))}
    </div>
  );
});
