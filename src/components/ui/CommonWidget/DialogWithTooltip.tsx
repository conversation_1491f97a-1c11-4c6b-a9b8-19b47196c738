import { ReactNode } from 'react';
import { Tooltip, TooltipContent, TooltipTrigger } from '../tooltip';
import { cn } from '@/lib/twMerge/cn';

export function DialogWithTooltip({
  className,
  Icon,
  title,
  ...rest
}: {
  className?: string;
  Icon: ReactNode;
  title: string;
}) {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <div
          className={cn(
            'inline rounded-sm opacity-70 transition-opacity data-[state=open]:bg-white data-[state=open]:text-black hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-black focus:ring-offset-2 disabled:pointer-events-none',
            className,
          )}
          {...rest}
        >
          {Icon}
          <span className="sr-only">{title}</span>
        </div>
      </TooltipTrigger>

      <TooltipContent className="bg-black text-white opacity-80" side="bottom">
        <p className="font-medium">{title}</p>
      </TooltipContent>
    </Tooltip>
  );
}
