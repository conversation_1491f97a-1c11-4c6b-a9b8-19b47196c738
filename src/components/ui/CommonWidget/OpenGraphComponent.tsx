import { noImagePlaceholder } from '@/assets/images';
import { ComponentType } from 'react';

type OpenGraphData = {
  title?: string;
  description?: string;
  image?: string;
  url?: string;
} & { isSender: boolean };

export const ChatOpenGraphComponent: ComponentType<OpenGraphData> = ({
  title = 'Title',
  description = 'Description',
  image = noImagePlaceholder,
  url = 'Unknown',
  isSender,
}) => {
  return (
    <div
      className={`relative mx-auto mb-1 w-full max-w-[250px] overflow-hidden rounded-[16px] border bg-white ${
        isSender
          ? '!rounded-tr-none bg-[#A2E0FF] [&_a]:text-black'
          : '!rounded-tl-none bg-white [&_a]:text-primary'
      }`}
    >
      {image && (
        <div className="max-h-[2000px] max-w-[250px]">
          <img
            src={image}
            alt={title || 'Preview Image'}
            className="h-full w-full object-contain"
          />
        </div>
      )}
      <div className="p-3">
        {title && <h2 className="mb-1 text-[12px] font-semibold">{title}</h2>}
        {description && (
          <p className="text-[10px] text-gray-600">
            {description.length > 100
              ? `${description.slice(0, 100)}...`
              : description}
          </p>
        )}
        {url && (
          <a
            href={url}
            target="_blank"
            rel="noopener noreferrer"
            className="break-all text-[10px] text-blue-500 hover:underline"
          >
            {url}
          </a>
        )}
      </div>
    </div>
  );
};
