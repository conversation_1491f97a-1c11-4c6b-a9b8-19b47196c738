import { useCallback, useRef, useState } from 'react';

import { CaretArrowDown, FilterIcon } from '@/assets/icons';

import SearchBar from '@/components/ui/SearchBar';
import { Spinner } from '@/components/ui/CommonWidget/Loader';
import { VirtualScroller } from '@/components/ui/VirtualScroller/VirtualScroller';

import { useOnClickOutside } from '@/hooks/useOnClickOutside';
import { useAppContext } from '@/context/event/AppEventContext';
import { useGetSDGs } from '@/hooks/apiQueryHooks/eduQueryHooks';
import { useGetCountries } from '@/hooks/apiQueryHooks/userQueryHooks';

import { cn } from '@/lib/twMerge/cn';
import { useFilteredData } from '@/hooks/useFilteredData';
import { Helper } from '@/utils/helpers';

type values = { [key: string]: string };

export const FilterByCountryAndSDG = ({
  className,
}: {
  className?: string;
}) => {
  const {
    sdgFilters,
    handleSetSdgFilters,
    countryFilters,
    handleSetCountriesFilters,
  } = useAppContext();

  const [searchValue, setSearchValue] = useState<values>({});

  const handleChange = (name: string, value: string) => {
    setSearchValue(prev => ({ ...prev, [name]: value }));
  };

  const [isOpen, setIsOpen] = useState(false);
  const ref = useRef<HTMLDivElement>(null);
  useOnClickOutside(ref, (e?: MouseEvent | TouchEvent) => {
    const target = e?.target as Element;
    const filterButton = document.querySelector('#filter-btn');

    if (
      (target?.id && target.id === 'filter-btn') ||
      filterButton?.contains(target)
    )
      return;
    setIsOpen(false);
  });

  const { data: countries, isLoading: isLoadingCountries } = useGetCountries();
  const { data: goals, isLoading: isLoadingSdgs } = useGetSDGs({
    staleTime: 1000 * 60 * 60 * 24,
    cacheTime: 1000 * 60 * 60 * 25,
  });

  const countryList = Helper.createListForCountries(
    countries?.data?.countries || [],
  );
  const sdgList = Helper.createListForSDGs(goals?.data || [])?.map(sdg => ({
    category: sdg?.category,
    categoryRef: sdg?.categoryRef,
  }));

  const filteredCountries = useFilteredData({
    data: countryList,
    searchValue: searchValue?.country,
    size: countryList?.length,
  });
  const filteredSdgs = useFilteredData({
    data: sdgList,
    searchValue: searchValue?.sdg,
    key: 'category',
  });

  const [dropdown, setDropdown] = useState('');

  const handleDropdown = useCallback(
    (currentRef: string) => {
      if (dropdown === currentRef) setDropdown('');
      else setDropdown(currentRef);
    },
    [dropdown],
  );

  const isActiveDropdown = (x: string) => dropdown === x;

  return (
    <div className="relative">
      <div
        onClick={() => setIsOpen(previous => !previous)}
        id="filter-btn"
        className={cn(
          `flex h-[37px] items-center border bg-grayTwo px-6 ${
            isOpen ? 'border-primary text-primary' : ''
          } cursor-pointer ${className}`,
        )}
      >
        <FilterIcon
          className={`${isOpen ? 'stroke-primary' : 'stroke-grayTen'} mr-3`}
        />
        <p>Filter</p>
      </div>
      {isOpen && (
        <div
          ref={ref}
          className="absolute right-0 top-[40px] z-[10] max-h-[350px] w-[303px]"
        >
          <div className="cursor-pointer border border-graySeven bg-grayTwo px-4 py-4">
            <div className="bg-grayTwo">
              <div
                id={'country'}
                className={` ${
                  isActiveDropdown('country') ? 'text-primary' : ''
                } flex items-center justify-between px-[20px] `}
                onClick={() => handleDropdown('country')}
              >
                <h2 className="py-2 text-[14px] font-[500] tracking-wider">
                  Countries
                </h2>
                <CaretArrowDown
                  className={`   ${
                    isActiveDropdown('country')
                      ? 'rotate-180 stroke-primary'
                      : ''
                  } h-3 w-3 duration-500 ease-in-out`}
                />
              </div>
              <VirtualScroller<(typeof filteredCountries)[0]>
                data={filteredCountries}
                element={({ item }) => (
                  <li className="flex items-center justify-between px-1 py-2">
                    <div className="flex gap-x-2">
                      <input
                        className="cursor-pointer checked:!bg-primary"
                        type="checkbox"
                        checked={countryFilters.includes(item.value!)}
                        onChange={() => handleSetCountriesFilters(item.value!)}
                      />
                      <p>{item.value}</p>
                    </div>
                  </li>
                )}
                className={`${isActiveDropdown('country') ? 'max-h-[350px]' : 'max-h-0'} ease relative flex flex-col overflow-auto text-[14px] text-subText duration-500 [&_.scroller]:z-0`}
              >
                <SearchBar
                  setSearchValue={value => handleChange('country', value)}
                  defaultValue={searchValue?.country || ''}
                  containerCls="z-[1] sticky top-0 rounded bg-white"
                  className="bg-white"
                />
                {isLoadingCountries && <Spinner className="mx-auto my-4" />}
              </VirtualScroller>
            </div>
            <div className="mt-1 bg-grayTwo">
              <div
                id={'sdg'}
                className={` ${
                  isActiveDropdown('sdg') ? 'text-primary' : ''
                } flex items-center justify-between px-[20px] `}
                onClick={() => handleDropdown('sdg')}
              >
                <h2 className="py-2 text-[14px] font-[500] tracking-wider">
                  SDGs
                </h2>
                <CaretArrowDown
                  className={`${
                    isActiveDropdown('sdg') ? 'rotate-180 stroke-primary' : ''
                  } h-3 w-3 duration-500 ease-in-out`}
                />
              </div>
              <VirtualScroller<(typeof filteredSdgs)[0]>
                data={filteredSdgs}
                element={({ item }) => (
                  <li className="flex items-center justify-between px-1 py-2">
                    <div className="flex items-center gap-x-2">
                      <input
                        className="cursor-pointer checked:!bg-primary"
                        type="checkbox"
                        checked={sdgFilters.includes(item.categoryRef!)}
                        onChange={() => handleSetSdgFilters(item.categoryRef!)}
                      />
                      <p>{item.category}</p>
                    </div>
                  </li>
                )}
                className={`${isActiveDropdown('sdg') ? 'max-h-[350px]' : 'max-h-0'} ease relative flex flex-col overflow-auto text-[14px] text-subText duration-500`}
              >
                <SearchBar
                  setSearchValue={value => handleChange('sdg', value)}
                  defaultValue={searchValue?.sdg || ''}
                  containerCls="z-[1] sticky top-0 rounded"
                  className="bg-white"
                />
                {isLoadingSdgs && <Spinner className="mx-auto my-4" />}
              </VirtualScroller>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
