import { CancelIcon } from '@/assets/icons';
import { noImagePlaceholder } from '@/assets/images';
import { ComponentType } from 'react';

type OpenGraphData = {
  title?: string;
  description?: string;
  image?: string;
  url?: string;
} & {
  setShowOpenGraphData?: (value: boolean) => void;
  shouldHideCancelButton?: boolean;
};

const OpenGraphPreviewComponent: ComponentType<OpenGraphData> = ({
  title = 'Title',
  description = 'Description',
  image = noImagePlaceholder,
  url = 'Unknown',
  setShowOpenGraphData = () => {},
  shouldHideCancelButton = false,
}) => {
  return (
    <div className="relative w-full overflow-hidden rounded-2xl border bg-white">
      {image && (
        <img
          src={image}
          alt={title || 'Preview Image'}
          className="h-32 w-full object-cover"
        />
      )}
      <div className="p-4">
        {title && <h2 className="mb-2 text-lg font-semibold">{title}</h2>}
        {description && (
          <p className="mb-2 text-sm text-gray-600">{description}</p>
        )}
        {url && (
          <a
            href={url}
            target="_blank"
            rel="noopener noreferrer"
            className="text-sm text-blue-500 hover:underline"
          >
            {title}
          </a>
        )}
      </div>
      {!shouldHideCancelButton && (
        <span
          onClick={e => e.stopPropagation()}
          className="group absolute right-4 top-3 cursor-pointer rounded-[50%] border-[0.3px] 
      bg-[#879cae] p-[6px] duration-300 ease-in-out hover:scale-110 hover:border-primary hover:bg-[#EAEBEC]"
        >
          <CancelIcon
            onClick={() => setShowOpenGraphData(false)}
            className="h-3 w-3 stroke-white duration-300 ease-in-out group-hover:stroke-primary sm:h-6 sm:w-6"
          />
        </span>
      )}
    </div>
  );
};

export default OpenGraphPreviewComponent;

export const ChatOpenGraphPreviewComponent: ComponentType<OpenGraphData> = ({
  title = 'Title',
  description = 'Description',
  image = noImagePlaceholder,
  url = 'Unknown',
  setShowOpenGraphData = () => {},
  shouldHideCancelButton = false,
}) => {
  return (
    <div className="relative mx-auto mb-1 flex max-h-fit w-full max-w-[95%] items-center overflow-hidden border bg-white">
      {image && (
        <div className="max-h-[80px] max-w-[80px]">
          <img
            src={image}
            alt={title || 'Preview Image'}
            className="h-full w-full object-contain"
          />
        </div>
      )}
      <div className="p-3">
        {title && <h2 className="mb-1 text-[12px] font-semibold">{title}</h2>}
        {description && (
          <p className="text-[10px] text-gray-600">
            {description.length > 100
              ? `${description.slice(0, 100)}...`
              : description}
          </p>
        )}
        {url && (
          <a
            href={url}
            target="_blank"
            rel="noopener noreferrer"
            className="text-[10px] text-blue-500 hover:underline"
          >
            {url}
          </a>
        )}
      </div>
      {!shouldHideCancelButton && (
        <span
          onClick={e => e.stopPropagation()}
          className="group absolute right-2 top-2 cursor-pointer rounded-[50%] border-[0.3px] 
      bg-[#879cae] p-[6px] duration-300 ease-in-out hover:scale-110 hover:border-primary hover:bg-[#EAEBEC]"
        >
          <CancelIcon
            onClick={() => setShowOpenGraphData(false)}
            className="h-2 w-2 stroke-white duration-300 ease-in-out group-hover:stroke-primary sm:h-3 sm:w-3"
          />
        </span>
      )}
    </div>
  );
};
