import { MarkIcon } from '@/assets/icons';
import { useCreditEventContext } from '@/features/Payment/context/credit/PaymentEventContext';

export default function StepNotifier() {
  const currentStepIndex = [
    'billingAddressForm',
    'summaryDetails',
    'stripeCardsElement',
  ];
  const { paymentStep } = useCreditEventContext();
  const currentStep = paymentStep;

  const activeClass = 'bg-lightOrangeTwo border-primary shadow-sm border';
  return (
    <div className="flex items-center text-[12px]">
      <div
        className={`my-4 flex
h-8 w-8  max-w-full flex-shrink-0 items-center justify-center rounded-[50%] border py-2.5 font-medium leading-6 sm:my-8  ${
          currentStepIndex.indexOf(currentStep) >
          currentStepIndex.indexOf('billingAddressForm')
            ? 'bg-primary'
            : currentStep === 'billingAddressForm'
              ? activeClass
              : 'bg-grayTwenty text-grayNine'
        }`}
      >
        {currentStepIndex.indexOf(currentStep) >
        currentStepIndex.indexOf('billingAddressForm') ? (
          <MarkIcon className="h-4 w-4 stroke-white" />
        ) : (
          '1'
        )}
      </div>
      <hr className="my-4 w-[200px] border border-primary sm:my-8" />
      <div
        className={` my-4 flex 
 h-8 w-8  max-w-full flex-shrink-0 items-center justify-center rounded-[50%] border py-2.5 font-medium  leading-6   sm:my-8   ${
   currentStepIndex.indexOf(currentStep) >
   currentStepIndex.indexOf('summaryDetails')
     ? 'bg-primary'
     : currentStep === 'summaryDetails'
       ? activeClass
       : 'bg-grayTwenty text-grayNine'
 }`}
      >
        {currentStepIndex.indexOf(currentStep) >
        currentStepIndex.indexOf('summaryDetails') ? (
          <MarkIcon className="h-4 w-4 stroke-white" />
        ) : (
          '2'
        )}
      </div>
      <hr className="my-4 w-[200px] border border-primary sm:my-8" />
      <div
        className={` my-4 flex 
 h-8 w-8  max-w-full flex-shrink-0 items-center justify-center rounded-[50%] border py-2.5 font-medium  leading-6   sm:my-8   ${
   currentStepIndex.indexOf(currentStep) >
   currentStepIndex.indexOf('stripeCardsElement')
     ? 'bg-primary'
     : currentStep === 'stripeCardsElement'
       ? activeClass
       : 'bg-grayTwenty text-grayNine'
 }`}
      >
        {currentStepIndex.indexOf(currentStep) >
        currentStepIndex.indexOf('stripeCardsElement') ? (
          <MarkIcon className="h-4 w-4 stroke-white" />
        ) : (
          '3'
        )}
      </div>
    </div>
  );
}
