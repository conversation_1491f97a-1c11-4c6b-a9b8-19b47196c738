import { profilePlaceholder } from '../../../assets/images';
import { Helper } from '../../../utils/helpers';

export default function PosterWidget({ createdBy }: { createdBy: string }) {
  return (
    <div className="mb-4 mt-4 flex items-center justify-center gap-3 self-start">
      <img
        loading="lazy"
        src={profilePlaceholder}
        className=" my-auto aspect-square w-10 max-w-full shrink-0 overflow-hidden rounded-[50%] object-contain object-center"
      />
      <div className="flex grow basis-[0%] flex-col items-stretch self-stretch px-5">
        <div className="-mr-5 whitespace-nowrap text-sm leading-6 tracking-normal text-gray-500">
          Posted by:
        </div>
        <div className="-mr-5 mt-1 whitespace-nowrap text-base font-medium leading-6 text-neutral-800">
          {`${Helper.capitalizeString(createdBy)}` || 'N/A'}
        </div>
      </div>
    </div>
  );
}
