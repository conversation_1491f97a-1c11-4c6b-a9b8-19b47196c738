import { motion } from 'framer-motion';
export default function SliderNavigator({
  slideIndex,
  setSlideIndexhandler,
}: {
  slideIndex: number;
  setSlideIndexhandler: (value: number) => void;
}) {
  return (
    <div className="absolute bottom-12 left-5 z-[10] mt-8 flex items-center justify-center gap-x-3 sm:bottom-40 sm:gap-x-4 lg:left-28 xxl:left-0">
      <span
        onClick={() => setSlideIndexhandler(0)}
        className={`h-[10px] cursor-pointer bg-grayFifteen
          transition duration-[2000] ease-in-out sm:h-[12px] ${
            slideIndex === 0
              ? 'w-[40px] rounded-[24px] sm:w-[48px]'
              : 'w-[10px] rounded-[50%] sm:w-[12px]'
          }`}
      >
        {slideIndex === 0 && (
          <motion.span
            initial={{ width: '0%' }}
            animate={{ width: '100%' }}
            transition={{ duration: 10, ease: 'linear' }}
            className="block h-full rounded-[24px] bg-primary"
          />
        )}
      </span>
      <span
        onClick={() => setSlideIndexhandler(1)}
        className={`h-[10px] cursor-pointer  bg-grayFifteen 
          transition duration-[2000] ease-in-out sm:h-[12px] ${
            slideIndex === 1
              ? 'w-[40px] rounded-[24px] sm:w-[48px]'
              : 'w-[10px] rounded-[50%] sm:w-[12px]'
          }`}
      >
        {slideIndex === 1 && (
          <motion.span
            initial={{ width: '0%' }}
            animate={{ width: '100%' }}
            transition={{ duration: 10, ease: 'linear' }}
            className="block h-full rounded-[24px] bg-primary"
          />
        )}
      </span>
      <span
        onClick={() => setSlideIndexhandler(2)}
        className={`h-[10px] cursor-pointer  bg-grayFifteen 
          transition duration-[2000] ease-in-out sm:h-[12px] ${
            slideIndex === 2
              ? 'w-[40px] rounded-[24px] sm:w-[48px]'
              : 'w-[10px] rounded-[50%] sm:w-[12px]'
          }`}
      >
        {slideIndex === 2 && (
          <motion.span
            initial={{ width: '0%' }}
            animate={{ width: '100%' }}
            transition={{ duration: 10, ease: 'linear' }}
            className="block h-full rounded-[24px] bg-primary"
          />
        )}
      </span>
    </div>
  );
}
