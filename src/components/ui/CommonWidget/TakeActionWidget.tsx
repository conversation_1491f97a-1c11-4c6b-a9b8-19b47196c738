import { ReactNode } from 'react';

import { DeleteIcon } from '../../../assets/icons';
import { useAppContext } from '../../../context/event/AppEventContext';
import Button from '../ButtonComponent';
import TakeActionModal from '../Modals/TakeActionModal';

export default function TakeActionWidget({
  actionHandler,
  isLoading,
  children,
  actionHandlerText,
  hideIcon,
  Icon = (
    <DeleteIcon className="h-6 w-6 stroke-white group-disabled:stroke-primary" />
  ),
  onCloseModalActionHandler,
}: {
  actionHandler: () => void;
  onCloseModalActionHandler?: () => void;
  isLoading: boolean;
  children: ReactNode;
  actionHandlerText: string;
  hideIcon?: boolean;
  Icon?: ReactNode;
}) {
  const { setShowModalHandler } = useAppContext();
  return (
    <TakeActionModal onCloseModalActionHandler={onCloseModalActionHandler}>
      {children}
      <div className="mt-10 flex items-center justify-center gap-x-[14px]">
        <Button
          type="button"
          onClick={() => {
            onCloseModalActionHandler
              ? onCloseModalActionHandler()
              : setShowModalHandler('');
          }}
          className="w-full max-w-[140px] border border-primary bg-white 
    text-primary   hover:bg-primary hover:text-white"
        >
          <p className="text-[12px]">Cancel</p>
        </Button>
        <Button
          disabled={isLoading}
          type="button"
          onClick={() => {
            actionHandler();
          }}
          className={`group w-full max-w-[140px] bg-primary text-white
           disabled:border  disabled:border-primary disabled:bg-white   disabled:text-primary`}
        >
          <div className="flex items-center gap-x-[10px]">
            {!hideIcon && Icon}
            <p className="text-[12px]">{actionHandlerText}</p>
          </div>
        </Button>
      </div>
    </TakeActionModal>
  );
}
