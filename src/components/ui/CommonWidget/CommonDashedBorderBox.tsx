import { ComponentProps, ReactNode } from 'react'

import { cn } from '../../../lib/twMerge/cn'

type Props = {
  children: ReactNode
}

export default function CommonDashedBorderBox({
  children,
  className,
}: Props & ComponentProps<'div'>) {
  return (
    <div
      className={cn(`text-neutral-600 text-center text-sm whitespace-nowrap border bg-orange-300 
bg-opacity-10 px-16 rounded-sm border-dashed border-orange-600 max-md:px-5 h-[300px] 
flex items-center justify-center ${className}`)}
    >
      {children}
    </div>
  )
}
