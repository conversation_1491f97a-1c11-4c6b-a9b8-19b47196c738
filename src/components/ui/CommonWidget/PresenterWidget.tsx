import { profilePlaceholder } from '../../../assets/images'
import { Helper } from '../../../utils/helpers'

export default function PresenterWidget({
  presentedBy,
}: {
  presentedBy: string
}) {
  return (
    <div className="justify-center items-center flex gap-3 mt-4 self-start mb-4">
      <img
        loading="lazy"
        src={profilePlaceholder}
        className=" rounded-[50%] aspect-square object-contain object-center w-10 overflow-hidden shrink-0 max-w-full my-auto"
      />
      <div className="items-stretch self-stretch flex grow basis-[0%] flex-col px-5">
        <div className="text-gray-500 text-sm leading-6 tracking-normal whitespace-nowrap -mr-5">
          Presented by:
        </div>
        <div className="text-neutral-800 text-base font-medium leading-6 whitespace-nowrap -mr-5 mt-1">
          {`${Helper.capitalizeString(presentedBy)}` || 'N/A'}
        </div>
      </div>
    </div>
  )
}
