import { useCallback, useRef, useState } from 'react';

import { CaretArrowDown, FilterIcon } from '@/assets/icons';

import SearchBar from '@/components/ui/SearchBar';
import { Spinner } from '@/components/ui/CommonWidget/Loader';
import { VirtualScroller } from '@/components/ui/VirtualScroller/VirtualScroller';

import { useOnClickOutside } from '@/hooks/useOnClickOutside';
import { useAppContext } from '@/context/event/AppEventContext';
import { useGetCountries } from '@/hooks/apiQueryHooks/userQueryHooks';

import { cn } from '@/lib/twMerge/cn';
import { useFilteredData } from '@/hooks/useFilteredData';
import { Helper } from '@/utils/helpers';

type values = { [key: string]: string };

export const FilterByCountry = ({ className }: { className?: string }) => {
  const { countryFilters, handleSetCountriesFilters } = useAppContext();

  const [searchValue, setSearchValue] = useState<values>({});

  const handleChange = (name: string, value: string) => {
    setSearchValue(prev => ({ ...prev, [name]: value }));
  };

  const [isOpen, setIsOpen] = useState(false);
  const ref = useRef<HTMLDivElement>(null);
  useOnClickOutside(ref, (e?: MouseEvent | TouchEvent) => {
    const target = e?.target as Element;
    const filterButton = document.querySelector('#filter-btn');

    if (
      (target?.id && target.id === 'filter-btn') ||
      filterButton?.contains(target)
    )
      return;
    setIsOpen(false);
  });

  const { data: countries, isLoading: isLoadingCountries } = useGetCountries();

  const countryList = Helper.createListForCountries(
    countries?.data?.countries || [],
  );

  const filteredCountries = useFilteredData({
    data: countryList,
    searchValue: searchValue?.country,
    size: countryList?.length,
  });

  const [dropdown, setDropdown] = useState('');

  const handleDropdown = useCallback(
    (currentRef: string) => {
      if (dropdown === currentRef) setDropdown('');
      else setDropdown(currentRef);
    },
    [dropdown],
  );

  const isActiveDropdown = (x: string) => dropdown === x;

  return (
    <div className="relative rounded-[10px]">
      <div
        onClick={() => setIsOpen(previous => !previous)}
        id="filter-btn"
        className={cn(
          `flex h-[37px] items-center rounded-[10px] border bg-grayTwo px-6 ${
            isOpen ? 'border-primary text-primary' : ''
          } cursor-pointer ${className}`,
        )}
      >
        <FilterIcon
          className={`${isOpen ? 'stroke-primary' : 'stroke-grayTen'} mr-3`}
        />
        <p>Filter</p>
      </div>
      {isOpen && (
        <div
          ref={ref}
          className="absolute right-0 top-[40px] z-[10] max-h-[350px] w-[303px]"
        >
          <div className="cursor-pointer border border-graySeven bg-grayTwo px-4 py-4">
            <div className="bg-grayTwo">
              <div
                id={'country'}
                className={` ${
                  isActiveDropdown('country') ? 'text-primary' : ''
                } flex items-center justify-between px-[20px] `}
                onClick={() => handleDropdown('country')}
              >
                <h2 className="py-2 text-[14px] font-[500] tracking-wider">
                  Countries
                </h2>
                <CaretArrowDown
                  className={`   ${
                    isActiveDropdown('country')
                      ? 'rotate-180 stroke-primary'
                      : ''
                  } h-3 w-3 duration-500 ease-in-out`}
                />
              </div>
              <VirtualScroller<(typeof filteredCountries)[0]>
                data={filteredCountries}
                element={({ item }) => (
                  <li className="flex items-center justify-between px-1 py-2">
                    <div className="flex gap-x-2">
                      <input
                        className="cursor-pointer checked:!bg-primary"
                        type="checkbox"
                        checked={countryFilters.includes(item.value!)}
                        onChange={() => handleSetCountriesFilters(item.value!)}
                      />
                      <p>{item.value}</p>
                    </div>
                  </li>
                )}
                className={`${isActiveDropdown('country') ? 'max-h-[350px]' : 'max-h-0'} ease relative flex flex-col overflow-auto text-[14px] text-subText duration-500 [&_.scroller]:z-0`}
              >
                <SearchBar
                  setSearchValue={value => handleChange('country', value)}
                  defaultValue={searchValue?.country || ''}
                  containerCls="z-[1] sticky top-0 rounded bg-white"
                  className="bg-white"
                />
                {isLoadingCountries && <Spinner className="mx-auto my-4" />}
              </VirtualScroller>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
