type Props = { text: string }

export default function ToolTip({ text }: Props) {
  return (
    <div
      className="rounded-lg bg-primary opacity-0 h-[30px] 
    duration-300 ease-out relative group-hover:opacity-100 py-2 px-3"
    >
      <p
        className="text-white text-center text-xs font-medium 
        leading-4 tracking-normal whitespace-nowrap
        "
      >
        {text}
      </p>
      <span
        className="w-[8px] h-[8px] rotate-45 absolute bottom-0 left-[16px] 
         bg-primary translate-y-[50%]"
      />
    </div>
  )
}
