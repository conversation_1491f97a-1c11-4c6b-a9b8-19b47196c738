import { motion } from 'framer-motion';
import { ComponentProps } from 'react';

import { rotate } from '../../../lib/framerMotion/animation.framerMotion';
import { cn } from '../../../lib/twMerge/cn';

interface Props extends ComponentProps<'div'> {}

export const Loader = () => {
  return (
    <div className="three-body">
      {Array.from({ length: 3 }, (_, index) => (
        <div className="three-body__dot" key={index} />
      ))}
    </div>
  );
};

export const AnimatedPlaceholder = ({ className, ...rest }: Props) => {
  return (
    <p
      className={cn(
        `animate-pulse rounded-[4px] bg-gradient-to-r  from-[#ca9a8987] ${className}`,
      )}
      {...rest}
    >
      &nbsp;
    </p>
  );
};
export const AnimatedPlaceholderForTable = ({
  className,
  cellNumber = 5,
}: {
  className?: string;
  cellNumber?: number;
} = {}) => {
  return (
    <tr
      className={cn(
        `animate-pulse rounded-[4px] bg-gradient-to-r  from-[#ca9a8987] ${className}`,
      )}
    >
      {Array.from({ length: cellNumber }, (_, index) => (
        <td key={index}>&nbsp;</td>
      ))}
    </tr>
  );
};
export const Spinner = ({ className }: Props) => (
  <motion.p
    variants={rotate}
    animate="show"
    className={cn(`mx-auto flex h-[20px] w-[20px] rounded-[50%]
border-[3px] border-primary border-b-[transparent] ${className}`)}
  />
);
