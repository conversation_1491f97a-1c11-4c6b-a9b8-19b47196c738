import { ReactNode } from 'react';
import { useNavigate } from 'react-router-dom';

import { ArrowBackIcon } from '../../../assets/icons';
import { useHandleQueryParams } from '../../../hooks/useHandleQueryParams';

type Props = {
  children: ReactNode;
  route?: string;
};

export default function CommonHeaderWithBackArrow({ children, route }: Props) {
  const navigate = useNavigate();
  const { query } = useHandleQueryParams();
  return (
    <div className="flex items-center gap-x-2">
      {!query.get('viewProject') && (
        <ArrowBackIcon
          className="cursor-pointer"
          onClick={() => route && navigate(route)}
        />
      )}
      {children}
    </div>
  );
}
