import { useState } from 'react';
import { Tooltip, TooltipProvider, TooltipTrigger } from '../tooltip';
import { LinkIcon } from '@/features/Conversation/assets/icons';
import { TooltipContent } from '@/components/ui/tooltip';

const CopyLinkTooltip = ({
  copyMessage = 'Share link to channel',
}: {
  copyMessage?: string;
}) => {
  const [open, setOpen] = useState(false);
  const [isCopied, setCopy] = useState(false);

  const handleCopy = () => {
    navigator.clipboard.writeText(window.location.href);
    setCopy(true);
    setOpen(true);

    // Keep tooltip open for 2 seconds
    setTimeout(() => {
      setCopy(false);
      setOpen(false);
    }, 2000);
  };

  return (
    <TooltipProvider>
      <Tooltip open={open} onOpenChange={setOpen}>
        <TooltipTrigger asChild>
          <div
            onClick={handleCopy}
            className="flex items-center gap-x-1 rounded-sm opacity-70 transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-black focus:ring-offset-2 disabled:pointer-events-none"
          >
            <LinkIcon className="h-5 w-5 cursor-pointer fill-[gray] group-hover:fill-primary" />
          </div>
        </TooltipTrigger>

        <TooltipContent
          className="bg-black text-white opacity-80"
          side="bottom"
        >
          <p className="font-medium">
            {isCopied ? 'Copied' : `${copyMessage}`}
          </p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default CopyLinkTooltip;
