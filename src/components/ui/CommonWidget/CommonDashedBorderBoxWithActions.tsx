import { ComponentProps } from 'react';

import { notFound } from '../../../assets/images';
import Button from '../ButtonComponent';
import CommonDashedBorderBox from './CommonDashedBorderBox';
import { Spinner } from './Loader';

type Props = {
  message: string;
  buttonText: string;
  buttonAction?: () => void;
  isLoading?: boolean;
};

export default function CommonDashedBorderBoxWithActions({
  buttonText,
  message,
  buttonAction,
  className,
  isLoading,
}: Props & Omit<ComponentProps<typeof CommonDashedBorderBox>, 'children'>) {
  return (
    <CommonDashedBorderBox className={`${className}`}>
      <div>
        <img src={notFound} alt=" not found" className="mx-auto mb-[2rem]" />
        <div className="mb-4 text-center text-sm font-medium text-neutral-600">
          {message}
        </div>
        {buttonAction && (
          <div className="mx-auto my-auto mt-auto w-full rounded-[5px] border border-solid border-black text-[16px] hover:border-[color:transparent]">
            <Button
              onClick={() => buttonAction()}
              disabled={isLoading}
              className="w-full bg-transparent text-black hover:bg-primary hover:text-white "
            >
              {isLoading ? <Spinner /> : <p>{buttonText}</p>}
            </Button>
          </div>
        )}
      </div>
    </CommonDashedBorderBox>
  );
}
