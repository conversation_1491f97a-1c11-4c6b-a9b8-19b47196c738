import { profilePlaceholder } from '../../../assets/images';
import { Helper } from '../../../utils/helpers';

export default function CreatorWidget({ createdBy }: { createdBy: string }) {
  return (
    <div className="mb-4 mt-4 flex items-center justify-center gap-4 self-start">
      <img
        loading="lazy"
        src={profilePlaceholder}
        className=" my-auto aspect-square w-10 max-w-full shrink-0 overflow-hidden rounded-[50%] object-contain object-center"
      />
      <div className="flex grow basis-[0%] flex-col items-stretch gap-1 self-stretch">
        <div className="whitespace-nowrap text-sm leading-4 tracking-normal text-gray-500">
          Created by:
        </div>
        <div className="whitespace-nowrap text-base font-medium leading-6 text-neutral-800">
          {`${Helper.capitalizeString(createdBy)}` || 'N/A'}
        </div>
      </div>
    </div>
  );
}
