import {
  ChromeSvg,
  EdgeSvg,
  ErrorSvg,
  FirefoxSvg,
  SafariSvg,
} from '@/assets/icons';
import Button from '../ButtonComponent';
import bgImage from '../../../features/WatchVideo/assets/images/rectangleFour.png';
import { Link } from 'react-router-dom';

export default function Error() {
  return (
    <div
      style={{ backgroundImage: `url(${bgImage})` }}
      className="fixed inset-0 z-100 mx-auto flex h-screen max-w-full 
      items-center justify-center bg-white bg-cover"
    >
      <div className="flex h-[400px] w-full items-center justify-center">
        <div className="flex flex-col items-center justify-center gap-4 sm:flex-row">
          <ErrorSvg className="w-full max-w-[534px] max-xs:max-h-[150px]" />
          <div>
            <h4 className="mb-3 text-[16px] font-[600] text-primary max-sm:text-center sm:text-[24px]">
              Something went wrong
            </h4>
            <p className="mx-auto max-w-[525px] text-[12px] text-[#2F2F2F] max-sm:text-center sm:text-[14px]">
              Whoops, we've encountered an issue displaying the requested info.
              Please refresh or download the latest version of your browser.
            </p>
            <div className="mt-8 flex w-fit items-center gap-3 max-sm:mx-auto">
              <Link
                target="_blank"
                to="https://www.google.com/chrome/update/?_gl=1*6btuy2*_up*MQ..&gclid=EAIaIQobChMIx4jXqMXriAMVAYBQBh3T_BCHEAAYASAAEgLRLPD_BwE&gclsrc=aw.ds"
              >
                <ChromeSvg />
              </Link>
              <Link
                target="_blank"
                to="https://www.mozilla.org/en-US/firefox/download/"
              >
                <FirefoxSvg />
              </Link>
              <Link target="_blank" to="https://support.apple.com/en-us/102665">
                <SafariSvg />
              </Link>
              <Link
                target="_blank"
                to="https://www.microsoft.com/en-us/edge/download?form=MA13FJ"
              >
                <EdgeSvg />
              </Link>
            </div>
            <Button
              onClick={() => window.location.reload()}
              type="button"
              className="mt-8 w-full max-w-[200px] bg-primary max-sm:mx-auto"
            >
              <p className="text-[16px] font-[700] text-white">Refresh</p>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
