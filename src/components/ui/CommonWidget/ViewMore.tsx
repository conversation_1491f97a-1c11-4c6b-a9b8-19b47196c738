import { motion } from 'framer-motion'
import { ComponentProps } from 'react'

import { arrowRight } from '../../../assets/icons'

interface Props extends ComponentProps<'div'> {
  message?: string
  start: boolean
}
export default function ViewMore({
  start,
  message = 'Browse All',
  ...rest
}: Props) {
  return (
    <div {...rest} className="flex gap-x-2 items-center cursor-pointer">
      <p className="text-primary text-[16px] ">{message}</p>
      <motion.img
        animate={start ? { x: ['4px', '0px', '4px', '0px'] } : { x: '0px' }}
        transition={{
          repeatDelay: 1,
          duration: 0.5,
          repeat: Number.POSITIVE_INFINITY,
        }}
        src={arrowRight}
        loading="lazy"
      />
    </div>
  )
}
