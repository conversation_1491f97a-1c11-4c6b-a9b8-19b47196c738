import { cn } from '@/lib/twMerge/cn';

interface Props extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  [key: string]: any;
}
export default function Button({
  className,
  children,
  ...rest
}: Props): JSX.Element {
  return (
    <button
      {...rest}
      className={cn(
        'flex h-[37px] min-h-[37px] items-center justify-center whitespace-nowrap rounded-[5px] p-4 outline-none transition duration-500 ease-in-out',
        className,
      )}
    >
      {children}
    </button>
  );
}
