import { FC, ReactNode, useRef } from 'react';
import { useVirtualizer } from '@tanstack/react-virtual';

import { cn } from '@/lib/twMerge/cn';

export const VirtualScroller = <T,>({
  data,
  className = 'max-h-[400px]',
  children,
  element: Element,
  estimateSize = 45,
}: {
  data: T[];
  children?: ReactNode;
  element: FC<{ item: T }>;
  className?: string;
  estimateSize?: number;
}) => {
  const parentRef = useRef<HTMLDivElement>(null);

  const rowVirtualizer = useVirtualizer({
    count: data.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => estimateSize,
  });

  const items = rowVirtualizer.getVirtualItems();

  return (
    <div ref={parentRef} className={cn(`overflow-auto ${className}`)}>
      {children}
      <div
        className="scroller relative w-full"
        style={{
          height: rowVirtualizer.getTotalSize(),
        }}
      >
        <div
          className="absolute left-0 top-0 w-full "
          style={{
            transform: `translateY(${items[0]?.start ?? 0}px)`,
          }}
        >
          {items.map(virtualItem => (
            <div
              key={virtualItem.key}
              data-index={virtualItem.index}
              ref={rowVirtualizer.measureElement}
            >
              <Element item={data[virtualItem.index]} />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
