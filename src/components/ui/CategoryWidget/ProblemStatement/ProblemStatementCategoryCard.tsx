import { createContext, ReactNode, useContext, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import { useAppContext } from '../../../../context/event/AppEventContext';
import { ProblemStatement } from '../../../../types';
import Button from '../../ButtonComponent';
import { TooltipWidget } from '../../TooltipWidget/TooltipWidget';
import Player from '../../WatchWidget/player';
import { useGetProject } from '@/features/Categories/hooks/apiQueryHooks/eduQueryHooks';
import { cn } from '@/lib/twMerge/cn';
import { useHandleQueryParams } from '@/hooks/useHandleQueryParams';

interface Props {
  problemStatement: Partial<ProblemStatement>;
  children: ReactNode;
}

const Context = createContext({} as Partial<ProblemStatement>);

export default function ProblemStatementCategoryCard({
  problemStatement,
  children,
}: Props) {
  const { problemStatementRef, descVidUrl, title, description } =
    problemStatement || {};

  const [isHovered, setIsHovered] = useState(false);
  const [cardRef, setCardRef] = useState<HTMLDivElement | null>(null);

  return (
    <Context.Provider
      value={{
        problemStatementRef,
        descVidUrl,
        title,
        description,
      }}
    >
      <div
        ref={setCardRef}
        onMouseOver={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        className="flex w-full max-w-[300px] flex-shrink-0 flex-col items-stretch self-stretch border-[2.2px] border-grayNineTeen"
      >
        <div className="mx-auto flex h-full w-full flex-col rounded bg-white pb-5 pl-2.5 pr-2 pt-2  shadow-md">
          <div className="flex flex-col items-stretch justify-center self-stretch bg-white py-px">
            <div className="relative rounded-[10px] pt-[56.25%]">
              <Player url={descVidUrl || ''} />
            </div>
          </div>
          <div className="mb-[23px] mt-2 px-2 text-[14px] font-medium tracking-normal text-blackFive">
            {(title || '')?.length > 50
              ? `${(title || '').slice(0, 50)}...`
              : title}
          </div>
          {children}
        </div>
        <TooltipWidget
          show={isHovered}
          parentRef={cardRef}
          className="w-full max-w-[300px] p-0 shadow-[0px_2px_7px_0px_#0000001F]"
        >
          <div className="relative flex flex-col gap-2">
            <p className="sticky top-0 flex-1 bg-white px-4 pt-4 text-[14px] font-medium">
              {title}
            </p>
            <div className="flex-1 whitespace-normal px-4 pb-4 text-[14px]">
              {description}
            </div>
          </div>
        </TooltipWidget>
      </div>
    </Context.Provider>
  );
}

export const ProblemStatementCategoryButtons = ({
  watchRoute,
  isAddProjectStateVariant = false,
}: {
  watchRoute?: string;
  isAddProjectStateVariant?: boolean;
}) => {
  const navigate = useNavigate();
  const { currentAccountType } = useAppContext();
  const { problemStatementRef } = useContext(Context);
  const { query } = useHandleQueryParams();
  const goalRef = query.get('goal_ref') || '';

  return (
    <div className="mt-auto">
      {isAddProjectStateVariant && (
        <Button
          onClick={() =>
            navigate(
              `/${currentAccountType}/dashboard-projects/add-project?p_s_ref=${problemStatementRef}${goalRef ? `&goal_ref=${goalRef}` : ''}`,
            )
          }
          className="mb-4 w-full border border-primary bg-white text-black hover:border-[color:transparent] hover:bg-primary hover:text-white"
        >
          <p>Proceed To Solve</p>
        </Button>
      )}
      <Button
        onClick={() => {
          navigate(`${watchRoute}?p_s_ref=${problemStatementRef}`);
        }}
        className={`w-full rounded-[8px] border border-[#F1F1F3] bg-[#F7F7F8] text-black hover:bg-primary hover:text-white`}
      >
        <p>View Details</p>
      </Button>
    </div>
  );
};

export const SelectProbmlemStatmentButton = () => {
  const { projectRef } = useParams();
  const { handleQuery } = useHandleQueryParams();
  const { setShowModalHandler } = useAppContext();
  const { problemStatementRef } = useContext(Context);

  const { data: project } = useGetProject(projectRef || '');

  const isSelected = project?.data?.problemStatementRef === problemStatementRef;

  const handleSelect = () => {
    handleQuery({
      p_s_ref: problemStatementRef || '',
      action: isSelected ? 'unselect' : 'select',
    });
    setShowModalHandler('AddProblemStatementToProject');
  };

  return (
    <>
      <Button
        onClick={handleSelect}
        className={cn(
          'w-full max-w-[140px] hover:border-[color:transparent] hover:bg-primary hover:text-white disabled:cursor-not-allowed disabled:bg-transparent disabled:text-primary',
          {
            'bg-primary text-white disabled:bg-primary disabled:text-white':
              isSelected,
            'border border-solid  border-black bg-white text-black':
              !isSelected,
          },
        )}
      >
        <p> {isSelected ? 'Unselect' : 'Select'}</p>
      </Button>
    </>
  );
};
