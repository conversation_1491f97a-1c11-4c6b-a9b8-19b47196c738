import { useCallback, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { useAppContext } from '../../../../context/event/AppEventContext';
import { useUserContext } from '../../../../context/user/UserContext';
import DownloadableFilesList from '../../../../features/WatchVideo/components/ui/problemStatement/DownloadableFilesList';
import Button from '../../ButtonComponent';

export default function ProblemStatementDescriptions({
  description,
  problemStatementRef,
  fileTitle,
  fileUrl,
}: {
  description: string | undefined;
  problemStatementRef?: string;
  fileTitle: string;
  fileUrl: string;
}) {
  const { isLoggedIn } = useUserContext();
  const { currentAccountType } = useAppContext();

  const [tab, setTab] = useState('description');
  const navigate = useNavigate();

  const handleTab = useCallback((value: string) => setTab(value), []);

  const activeClass = 'border-b bg-white border-primary shadow-sm';
  return (
    <div className="pb-[30px] text-[16px] md:pr-8">
      <div className="flex gap-2">
        <div
          className={`my-4 w-[200px] max-w-full cursor-pointer items-stretch 
          justify-center px-5 py-2.5 text-center font-medium  leading-6 text-neutral-800 sm:my-8 ${
            tab === 'description' ? activeClass : ''
          }`}
          onClick={() => handleTab('description')}
        >
          Description
        </div>
        <div
          className={`my-4 w-[200px] max-w-full cursor-pointer items-stretch 
          justify-center px-5 py-2.5 text-center font-medium  leading-6 text-neutral-800 sm:my-8 ${
            tab === 'download' ? activeClass : ''
          }`}
          onClick={() => handleTab('download')}
        >
          Download Files
        </div>
      </div>
      {tab === 'description' ? (
        <>
          {description ? (
            <div className="max-w-[635px] text-base leading-7 text-stone-950">
              <p className="text-[14px] font-[400]">
                <strong className="text-[16px] font-[700]">Brief: </strong>
                {description}
              </p>
            </div>
          ) : (
            <p className="text-[12px]">N/A</p>
          )}
          <div className="mt-[40px] w-full">
            {isLoggedIn ? (
              problemStatementRef && (
                <Button
                  onClick={() =>
                    navigate(
                      `/${currentAccountType}/dashboard-projects/add-project?p_s_ref=${problemStatementRef}`,
                    )
                  }
                  className="w-full max-w-[171px] bg-primary text-white  "
                >
                  <p>Proceed To Solve</p>
                </Button>
              )
            ) : (
              <Button
                onClick={() => navigate('/signup')}
                className="w-full max-w-[171px] bg-primary text-white  "
              >
                <p>Get Started</p>
              </Button>
            )}
          </div>
        </>
      ) : (
        <DownloadableFilesList
          title={fileTitle || ''}
          downloadableFile={fileUrl || ''}
        />
      )}
    </div>
  );
}
