import { ComponentProps } from 'react';

import { cn } from '../../../../lib/twMerge/cn';
import { ProblemStatement } from '../../../../types';
import ProblemStatementCategoryCard, {
  ProblemStatementCategoryButtons,
} from './ProblemStatementCategoryCard';

interface Props extends ComponentProps<'div'> {
  categoryList: ProblemStatement[];
  watchRoute: string;
  isAddProjectStateVariant?: boolean;
}

export default function ProblemStatementCategoryList({
  categoryList,
  watchRoute,
  isAddProjectStateVariant,
  className,
}: Props) {
  return (
    <div className={cn(`flex gap-3 pb-4 max-md:items-stretch ${className}`)}>
      {categoryList?.length > 0 ? (
        categoryList?.map((problemStatement, index) => (
          <ProblemStatementCategoryCard
            key={index}
            problemStatement={problemStatement}
          >
            <ProblemStatementCategoryButtons
              watchRoute={watchRoute}
              isAddProjectStateVariant={isAddProjectStateVariant}
            />
          </ProblemStatementCategoryCard>
        ))
      ) : (
        <p className="w-full py-4 text-center">No Statements found</p>
      )}
    </div>
  );
}
