import {
  createContext,
  forwardRef,
  MouseEvent,
  ReactNode,
  Ref,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';
import DOMPurify from 'dompurify';
import { useNavigate, useParams } from 'react-router-dom';

import { noImagePlaceholder } from '../../../../assets/images';
import { useAppContext } from '../../../../context/event/AppEventContext';
import { Project } from '../../../../features/Categories/types';
import { useAddProjectToGoal } from '../../../../features/projectManagementGoals';
import { useRemoveProjectFromGoal } from '../../../../features/projectManagementGoals/hooks/apiQueryhooks/eduQueryHooks';
import useGetGoalDetailsData from '../../../../features/projectManagementGoals/hooks/useGetGoalDetailsData';
import useHandleApiFeebackWithToast from '../../../../hooks/useHandleApiFeebackWithToast';
import { useHandleQueryParams } from '../../../../hooks/useHandleQueryParams';
import { GET_GOAL_DETAILS_QUERY } from '../../../../utils/queryKeys';
import Button from '../../ButtonComponent';
import { useGetSDGs } from '@/hooks/apiQueryHooks/eduQueryHooks';
import { TooltipWidget } from '../../TooltipWidget/TooltipWidget';
import { cn } from '@/lib/twMerge/cn';
import { projectTag } from '@/data/constants';
import useManageActiveSubscription from '@/hooks/useManageActiveSubscription';
import CoverElement from '../../CommonWidget/CoverElement';
import { usePaginationHandler } from '@/hooks/usePaginationHandler';
import { Helper } from '@/utils/helpers';

interface Props {
  children: ReactNode;
  hideToolTip?: boolean;
  project?: Partial<Project>;
  index?: number;
  pageIndex?: number;
}

const Context = createContext({} as Partial<Project>);

export default forwardRef(function ProjectCategoryCard(
  { project, hideToolTip = false, children, pageIndex, index }: Props,
  ref: Ref<HTMLDivElement>,
) {
  const {
    projectImageUrl,
    projectName,
    projectRef,
    projectCountries,
    categoryRefs,
    projectDescription,
    tag,
    creatorUniversity,
  } = project || {};
  const [isHovered, setIsHovered] = useState(false);
  const [isTagHovered, setIsTagHovered] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);
  const tagRef = useRef<HTMLDivElement>(null);

  const { currentPage, pageSize, pageSizeForInfiniteQuery } =
    usePaginationHandler();

  const {
    isInactiveOrExpiredOrCancelled,
    basicProjectLimit,
    innovatorProjectLimit,
    acceleratorProjectLimit,
    isAccelerator,
    isInnovator,
  } = useManageActiveSubscription();
  const { setShowModalHandler } = useAppContext();

  const cardIndex = index ? currentPage * pageSize - pageSize + index : 0;

  const cardIndexForInfiniteQuery =
    pageIndex && index
      ? (pageIndex + 1) * pageSizeForInfiniteQuery -
        pageSizeForInfiniteQuery +
        index
      : 0;

  const isInActiveForbasicPackage =
    isInactiveOrExpiredOrCancelled &&
    (cardIndexForInfiniteQuery || cardIndex) > basicProjectLimit;
  const isInActiveForInnovatorPackage =
    (cardIndexForInfiniteQuery || cardIndex) > innovatorProjectLimit &&
    isInnovator;

  const isInActiveForAcceleratorPackage =
    (cardIndexForInfiniteQuery || cardIndex) > acceleratorProjectLimit &&
    isAccelerator;

  const handleTagTooltip = (
    event: MouseEvent<HTMLDivElement>,
    value: boolean,
  ) => {
    event.stopPropagation();
    event.preventDefault();
    if (project?.tag !== projectTag.ADMITTED) return;
    setIsTagHovered(value);
  };

  useEffect(() => {
    const img = new Image();
    img.src = projectImageUrl || '';
  }, [projectImageUrl]);

  return (
    <Context.Provider
      value={{
        projectRef,
        projectName,
        projectCountries,
        categoryRefs,
        creatorUniversity,
      }}
    >
      <div className="relative flex w-[19%] min-w-[244px] items-stretch">
        <div
          className="flex w-full flex-col 
        self-stretch rounded-[4px] border-[2.2px] border-grayNineTeen bg-white p-4 shadow-md max-md:w-full"
          ref={cardRef}
          onMouseOver={() => setIsHovered(true)}
          onMouseOut={() => setIsHovered(false)}
        >
          {tag === projectTag.ADMITTED && (
            <div
              ref={tagRef}
              onMouseOver={e => handleTagTooltip(e, true)}
              onMouseOut={e => handleTagTooltip(e, false)}
              className={cn(
                'absolute right-4 top-4 bg-[#01820E] px-1.5 py-1 text-[12px] font-semibold leading-[13.44px] text-white',
              )}
            >
              {tag?.replace('_', ' ')}
            </div>
          )}
          <div ref={ref}>
            <img
              loading="lazy"
              src={projectImageUrl || noImagePlaceholder}
              className="aspect-[1.23] max-h-[388.68px] w-[210px] max-w-full self-start overflow-hidden object-cover object-center"
            />
          </div>
          {children}
        </div>
        {isInActiveForbasicPackage ||
        isInActiveForInnovatorPackage ||
        isInActiveForAcceleratorPackage ? (
          <CoverElement
            className="cursor-pointer bg-[#fff]/60"
            onClick={() => {
              !isInactiveOrExpiredOrCancelled
                ? setShowModalHandler('ProjectLimitModal')
                : setShowModalHandler('InactiveSubscriptionModal');
            }}
          />
        ) : null}
      </div>
      <TooltipWidget
        show={isTagHovered}
        parentRef={tagRef.current}
        className="w-full max-w-[300px] rounded bg-primary p-3 text-[12px] text-white"
      >
        Admitted means the creator of this project has been verified as a
        student or faculty member of one of our verified tertiary institutions.
      </TooltipWidget>
      <TooltipWidget
        show={isHovered && !hideToolTip}
        parentRef={cardRef.current}
        className="w-full max-w-[300px] rounded border border-primary p-0 shadow-[0px_2px_7px_0px_#0000001F]"
      >
        <div className="relative flex flex-col gap-2">
          <p className="sticky top-0 flex-1 bg-white px-4 pt-4 text-[14px] font-bold">
            {projectName || ''}
          </p>

          <div
            className="flex-1 whitespace-normal px-4 pb-4 text-[14px] text-subText"
            dangerouslySetInnerHTML={{
              __html: DOMPurify.sanitize(projectDescription || 'N/A'),
            }}
          />
        </div>
      </TooltipWidget>
    </Context.Provider>
  );
});

export const ProjectCategoryCardForAvailableProject = forwardRef(
  function ProjectCategoryCardForAvailableProject(
    { project, hideToolTip = false, children }: Props,
    ref: Ref<HTMLDivElement>,
  ) {
    const {
      projectImageUrl,
      projectName,
      projectRef,
      projectCountries,
      categoryRefs,
      projectDescription,
      tag,
      creatorUniversity,
    } = project || {};
    const [isHovered, setIsHovered] = useState(false);
    const [isTagHovered, setIsTagHovered] = useState(false);
    const cardRef = useRef<HTMLDivElement>(null);
    const tagRef = useRef<HTMLDivElement>(null);

    const handleTagTooltip = (
      event: MouseEvent<HTMLDivElement>,
      value: boolean,
    ) => {
      event.stopPropagation();
      event.preventDefault();
      if (project?.tag !== projectTag.ADMITTED) return;
      setIsTagHovered(value);
    };

    return (
      <Context.Provider
        value={{
          projectRef,
          projectName,
          projectCountries,
          categoryRefs,
          creatorUniversity,
        }}
      >
        <div className="relative flex w-[19%] min-w-[244px] items-stretch">
          <div
            className="flex w-full flex-col 
          self-stretch rounded-[4px] border-[2.2px] border-grayNineTeen bg-white p-4 shadow-md max-md:w-full"
            ref={cardRef}
            onMouseOver={() => setIsHovered(true)}
            onMouseOut={() => setIsHovered(false)}
          >
            {tag === projectTag.ADMITTED && (
              <div
                ref={tagRef}
                onMouseOver={e => handleTagTooltip(e, true)}
                onMouseOut={e => handleTagTooltip(e, false)}
                className={cn(
                  'absolute right-4 top-4 bg-[#01820E] px-1.5 py-1 text-[12px] font-semibold leading-[13.44px] text-white',
                )}
              >
                {tag?.replace('_', ' ')}
              </div>
            )}
            <div ref={ref}>
              <img
                loading="lazy"
                src={projectImageUrl || noImagePlaceholder}
                className="aspect-[1.23] max-h-[388.68px] w-[210px] max-w-full self-start overflow-hidden object-cover object-center"
              />
            </div>
            {children}
          </div>
        </div>
        <TooltipWidget
          show={isTagHovered}
          parentRef={tagRef.current}
          className="w-full max-w-[300px] rounded bg-primary p-3 text-[12px] text-white"
        >
          Admitted means the creator of this project has been verified as a
          student or faculty member of one of our verified tertiary
          institutions.
        </TooltipWidget>
        <TooltipWidget
          show={isHovered && !hideToolTip}
          parentRef={cardRef.current}
          className="w-full max-w-[300px] rounded border border-primary p-0 shadow-[0px_2px_7px_0px_#0000001F]"
        >
          <div className="relative flex flex-col gap-2">
            <p className="sticky top-0 flex-1 bg-white px-4 pt-4 text-[14px] font-bold">
              {projectName || ''}
            </p>

            <div
              className="flex-1 whitespace-normal px-4 pb-4 text-[14px] text-subText"
              dangerouslySetInnerHTML={{
                __html: DOMPurify.sanitize(projectDescription || 'N/A'),
              }}
            />
          </div>
        </TooltipWidget>
      </Context.Provider>
    );
  },
);
const SeeMore = ({ action }: { action: () => void }) => {
  return (
    <Button
      onClick={() => action()}
      className="mt-auto w-full max-w-[140px]  border border-solid 
border-black bg-white text-black hover:border-[color:transparent] hover:bg-primary hover:text-white"
    >
      <p> See more</p>
    </Button>
  );
};

export const ProjectName = () => {
  const { projectName } = useContext(Context);
  return (
    <div className=" mt-3 w-full max-w-[210px] self-start truncate break-all text-[14px] font-bold capitalize text-stone-950">
      {projectName && projectName?.length > 50
        ? `${projectName.slice(0, 50)}...`
        : projectName}
    </div>
  );
};

export const ProjectSdgs = () => {
  const { categoryRefs } = useContext(Context);

  const { data, isSuccess } = useGetSDGs({
    staleTime: 1000 * 60 * 60 * 24,
    cacheTime: 1000 * 60 * 60 * 25,
  });

  const categoryData = Helper.truncateText(
    (data?.data || [])
      .filter(item => (categoryRefs || [])?.includes(item.categoryRef))
      .map(category => category?.categoryName)
      .join(', '),
    44,
  );

  return isSuccess ? (
    <div className="mt-2 whitespace-normal text-wrap break-words rounded bg-[#EAEAEA] px-2 py-1 text-[10px] font-semibold text-subText">
      SDGs: {categoryData || 'N/A'}
    </div>
  ) : null;
};

export const ProjectCountry = () => {
  const { projectCountries = [] } = useContext(Context);

  const computedCountries = Helper.truncateText(
    (projectCountries || []).filter(Boolean).join(', '),
    44,
  );

  return (
    <div className="mt-2 whitespace-normal text-wrap break-words rounded bg-[#EAEAEA] px-2 py-1 text-[10px] font-semibold text-subText">
      Country Focus: {computedCountries || 'N/A'}
    </div>
  );
};

export const ProjectInstitution = () => {
  const { creatorUniversity } = useContext(Context);
  return (
    <div className="mt-2 whitespace-normal text-wrap break-words rounded bg-[#EAEAEA] px-2 py-1 text-[10px] font-semibold text-subText">
      Institution: {Helper.truncateText(creatorUniversity || '', 44) || 'N/A'}
    </div>
  );
};

export const SeeMoreProjectButton = () => {
  const navigate = useNavigate();
  const { teamRef } = useParams();
  const { currentAccountType, setShowModalHandler } = useAppContext();
  const { projectRef } = useContext(Context);

  return (
    <SeeMore
      action={() => {
        navigate(
          teamRef
            ? `/${currentAccountType}/dashboard-projects/${projectRef}?team_ref=${teamRef}`
            : `/${currentAccountType}/dashboard-projects/${projectRef}`,
        );
        setShowModalHandler('');
      }}
    />
  );
};
export const SeeMoreProjectFromGoalsButton = () => {
  const { handleQuery, query } = useHandleQueryParams();
  const { projectRef } = useContext(Context);
  const { setShowModalHandler } = useAppContext();
  return (
    <SeeMore
      action={() => {
        handleQuery({
          project_ref: projectRef,
          goal_ref: query.get('goal_ref') || '',
          created_by: query.get('created_by') || '',
          view_project: 'viewProject',
        });
        setShowModalHandler('SeeMoreAboutProjectForGoalsModal');
      }}
    />
  );
};
export const SelectProjectButton = () => {
  const { query } = useHandleQueryParams();
  const { projectRef } = useContext(Context);
  const { mutate, isLoading } = useAddProjectToGoal({
    ...useHandleApiFeebackWithToast({ queryKey: [GET_GOAL_DETAILS_QUERY] }),
  });
  const { mutate: removeProject } = useRemoveProjectFromGoal({
    ...useHandleApiFeebackWithToast({ queryKey: [GET_GOAL_DETAILS_QUERY] }),
  });
  const handleSelect = () => {
    mutate({
      projectRef: projectRef || '',
      goalRef: query.get('goal_ref') || '',
    });
  };
  const handleUnSelect = () => {
    removeProject({
      projectRef: projectRef || '',
      goalRef: query.get('goal_ref') || '',
    });
  };

  const { data } = useGetGoalDetailsData({
    goalRef: query.get('goal_ref') || '',
    createdBy: query.get('created_by') || '',
  });
  const isSelected = data?.data.projects
    .map(project => project?.projectRef)
    .includes(projectRef || '');
  return (
    <Button
      disabled={isLoading}
      onClick={() => {
        if (isSelected) {
          handleUnSelect();
        } else {
          handleSelect();
        }
      }}
      className={`${
        isSelected
          ? 'bg-primary text-white'
          : 'border border-solid  border-black bg-white text-black'
      } mt-auto w-full max-w-[140px] hover:border-[color:transparent] hover:bg-primary hover:text-white
    `}
    >
      <p>{isSelected ? 'Unselect' : 'Select'}</p>
    </Button>
  );
};
