import { profilePlaceholder } from '../../../../assets/images';
import { ProjectlevelUserRoleType } from '../../../../types';

type Props = {
  isProjectTeamPageVariant?: boolean;
  userId: string;
  projectRole: ProjectlevelUserRoleType;
  firstName: string;
  lastName: string;
};
export function ProjectMemberCard({ firstName, lastName }: Props) {
  return (
    <div className={`flex w-[38%] items-center gap-5`}>
      <div className="flex items-stretch gap-2">
        <img
          loading="lazy"
          src={profilePlaceholder}
          className="aspect-square w-12 max-w-full shrink-0 overflow-hidden 
          rounded-[50%] object-contain object-center"
        />
        <div className="my-auto grow self-center whitespace-nowrap text-base text-neutral-500">
          {`${firstName} ${lastName && lastName.slice(0, 1).toUpperCase()}.`}
        </div>
      </div>
    </div>
  );
}
