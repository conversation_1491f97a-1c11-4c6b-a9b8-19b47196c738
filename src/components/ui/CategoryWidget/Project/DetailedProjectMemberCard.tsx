import { profilePlaceholder } from '@/assets/images';
import { useAppContext } from '@/context/event/AppEventContext';
import useGetTeamLevelBasedAccess from '@/hooks/useGetTeamLevelBasedAccess';
import { useNavigate } from 'react-router-dom';
import Button from '../../ButtonComponent';
import { DataResponse, TeamlevelUserRoleType } from '@/types';
import { ChatIcon2 } from '@/assets/icons';
import { useCreateChatMessage } from '@/hooks/apiQueryHooks/chatQueryHooks';
import useHandleApiFeebackWithToast from '@/hooks/useHandleApiFeebackWithToast';

type Props = {
  teamRole: TeamlevelUserRoleType;
  firstName: string;
  lastName: string;
  userId: string;
};
export function DetailedProjectMemberCard({
  firstName,
  lastName,
  userId,
}: Props) {
  const { currentAccountType } = useAppContext();
  const navigate = useNavigate();
  const { isTeamLeadAtEachMemberLevel, isLoggedInUserThisTeamMember } =
    useGetTeamLevelBasedAccess({
      teamMemberUserId: userId || '',
    });

  const next = (res: DataResponse<any>) => {
    navigate(`/${currentAccountType}/chat?chat_room_id=${res?.data?._id}`);
  };
  const { mutate: createChatRoom } = useCreateChatMessage({
    ...useHandleApiFeebackWithToast({ next }),
  });
  return (
    <div
      className="flex w-[24%] min-w-[200px] flex-col items-stretch
       self-stretch  rounded-[4px] border-[2.2px] border-grayNineTeen py-2  shadow-md max-md:w-full"
    >
      <img
        src={profilePlaceholder}
        className="mx-auto h-full max-h-[100px] w-full max-w-[100px] rounded-[50%]"
      />
      <div className="">
        <div className=" flex flex-col gap-1 px-5 py-4">
          <p className="mx-auto w-fit bg-lightOrangeTwo p-2 text-[14px] leading-[22px]">
            {isTeamLeadAtEachMemberLevel ? 'Team Lead' : 'Team Member'}
          </p>
          <p className="text-center text-[14px] font-medium leading-[24px]">
            {' '}
            {`${firstName} ${lastName}`}
          </p>
        </div>
        <div className="flex items-center justify-center gap-x-1">
          {!isLoggedInUserThisTeamMember && (
            <ChatIcon2
              onClick={() => createChatRoom({ receiverId: userId })}
              className="h-5 w-5 cursor-pointer fill-graySixteen hover:fill-primary"
            />
          )}
          <div className="border-t border-t-graySeven px-5 pb-5 pt-5">
            <Button
              onClick={() => navigate(`${userId}`)}
              className="mx-auto h-[33px] w-full max-w-[145px] whitespace-nowrap rounded-[2.7px] border-[1px] border-grayTen bg-transparent text-[12px] hover:border-primary hover:bg-primary hover:text-white"
            >
              View Full Profile
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
