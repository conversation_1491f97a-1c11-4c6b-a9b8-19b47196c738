import { PencilIcon } from '@/features/Profile/assets';

import useGetProjectLevelBasedAccess from '@/hooks/useGetProjectLevelBasedAccess';
import { useAppContext } from '@/context/event/AppEventContext';
import { cn } from '@/lib/twMerge/cn';
import { ReactNode } from 'react';
import DOMPurify from 'dompurify';

export default function ProjectDescription({
  hideEdit = false,
  description,
  title,
}: {
  hideEdit?: boolean;
  description: string | undefined;
  className?: string;
  title?: ReactNode;
}) {
  const { setShowModalHandler } = useAppContext();
  const { isLoggedInUserAProjectAdmin, isCreatorAtProjectLevel } =
    useGetProjectLevelBasedAccess();

  return (
    <div className="h-full w-full rounded-lg border border-[#D9D9D9] p-4 text-[16px] leading-[26px] text-stone-950">
      <div className={cn('mb-4 flex items-center justify-between')}>
        {title ? (
          title
        ) : (
          <h5 className="text-sm text-primary sm:text-base">
            Project Description
          </h5>
        )}
        {!hideEdit && (
          <>
            {(isLoggedInUserAProjectAdmin || isCreatorAtProjectLevel) && (
              <button
                className="flex cursor-pointer items-center gap-2 leading-none text-primary"
                onClick={() =>
                  setShowModalHandler('UpdateProjectInformationModal')
                }
              >
                Edit Project <PencilIcon className="stroke-primary" />
              </button>
            )}
          </>
        )}
      </div>
      {description ? (
        <div className="text-sm font-normal">
          <div
            className="inline"
            dangerouslySetInnerHTML={{
              __html: DOMPurify.sanitize(description),
            }}
          />
        </div>
      ) : (
        <p className="text-sm">N/A</p>
      )}
    </div>
  );
}
