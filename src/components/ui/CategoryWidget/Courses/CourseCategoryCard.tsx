import { ComponentProps, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { noImagePlaceholder } from '../../../../assets/images';
import { useGetSDG } from '../../../../hooks/apiQueryHooks/eduQueryHooks';
import { cn } from '../../../../lib/twMerge/cn';
import { ArrayIndexElement, CourseList } from '../../../../types';
import { Helper } from '../../../../utils/helpers';
import Button from '../../ButtonComponent';
import { TooltipWidget } from '../../TooltipWidget/TooltipWidget';

export default function CourseCategoryCard({
  courseImageUrl,
  title,
  className,
  watchRoute,
  courseRef,
  courseProvider,
  duration,
  categoryRef,
  ...rest
}: ArrayIndexElement<CourseList> & ComponentProps<'div'>) {
  const navigate = useNavigate();
  const { data } = useGetSDG(categoryRef, {
    enabled: !!categoryRef,
  });
  const color = Helper.getBgForCourseCategoryCard(
    data?.data?.categoryName || '',
  );

  const [isHovered, setIsHovered] = useState(false);

  const [cardRef, setCardRef] = useState<HTMLDivElement | null>(null);

  useEffect(() => {
    const img = new Image();
    img.src = courseImageUrl || '';
  }, [courseImageUrl]);

  return (
    <div
      ref={setCardRef}
      onMouseOver={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      className={cn(
        `flex w-full max-w-[300px] flex-shrink-0 cursor-pointer flex-col items-stretch  self-stretch rounded 
        border-[2.2px] border-grayNineTeen bg-white shadow-md ${className}`,
      )}
    >
      <div
        onClick={() =>
          navigate(
            `${watchRoute}?c_ref=${courseRef}&provider=${courseProvider}&system_category_ref=course`,
          )
        }
        className="mx-auto flex w-full flex-col pl-2.5 pr-2 pt-2"
      >
        <div className="flex flex-col items-stretch justify-center self-stretch bg-white py-px">
          <img
            loading="lazy"
            src={courseImageUrl || noImagePlaceholder}
            className="h-[150px] object-cover"
          />
        </div>
        <span
          className={`${color} mx-auto my-[14px] w-[fit-content] rounded-lg bg-opacity-10 px-2 py-1 text-center  text-[10px] font-semibold uppercase leading-4`}
        >
          {data?.data?.categoryName}
        </span>
        <div className="my-[14px] text-[14px] font-medium text-blackFive">
          {title.length > 50 ? `${title.slice(0, 50)}...` : title}
        </div>
      </div>
      <div className="mt-auto">
        <div className="min-h-[1px] w-full self-stretch bg-gray-200" />
        <div className="my-[14px] px-2 text-center text-[14px] font-medium leading-7 tracking-normal text-grayTen">
          Duration:{' '}
          <span className="text-grayTwelve">
            {duration ? `${duration}` : 'N/A'}{' '}
          </span>
        </div>
      </div>
      <TooltipWidget
        show={isHovered}
        parentRef={cardRef}
        className="w-full max-w-[300px] p-0 shadow-[0px_2px_7px_0px_#0000001F]"
      >
        <div className="relative flex flex-col gap-2">
          <p className="sticky top-0 flex-1 bg-white px-4 pt-4 text-[14px] font-medium">
            {title}
          </p>
          <div className="flex-1 whitespace-normal px-4 text-[14px]">
            {rest?.instructors && rest?.instructors?.length > 0 && (
              <ul className="mb-4 flex flex-col items-stretch text-[14px]">
                <div className="w-full tracking-tight text-blueGray max-md:max-w-full">
                  Instructors
                </div>
                <ul className="list-inside list-disc">
                  {rest?.instructors.map((text: string, index: number) => (
                    <li
                      key={index}
                      className="mt-0.5 w-full tracking-normal text-black max-md:max-w-full"
                    >
                      {text}
                    </li>
                  ))}
                </ul>
              </ul>
            )}
            {rest?.whatYouWillLearn && rest?.whatYouWillLearn?.length > 0 && (
              <ul className="mb-4 flex flex-col items-stretch text-[14px]">
                <ul className="list-inside list-disc">
                  {rest?.whatYouWillLearn.map((text: string, index: number) => (
                    <li
                      key={index}
                      className="mt-0.5 flex w-full gap-2 tracking-normal text-black max-md:max-w-full"
                    >
                      <div className="w-4 flex-shrink-0">
                        <img
                          loading="lazy"
                          src="https://cdn.builder.io/api/v1/image/assets/TEMP/afe39a0318deaa25375f4036c83d99d758a0932a439cdd7c4c196bb6ae11bc89?apiKey=73784b02ab414143b033253eca33962e&"
                          className="max-w-full shrink-0 overflow-hidden object-contain object-center"
                        />
                      </div>
                      <span>{text}</span>{' '}
                    </li>
                  ))}
                </ul>
              </ul>
            )}
            <Button
              onClick={() =>
                navigate(
                  `${watchRoute}?c_ref=${courseRef}&provider=${courseProvider}&system_category_ref=course`,
                )
              }
              className="mb-4 w-full max-w-full bg-primary capitalize text-white"
            >
              <p>Get Started</p>
            </Button>
          </div>
        </div>
      </TooltipWidget>
    </div>
  );
}
