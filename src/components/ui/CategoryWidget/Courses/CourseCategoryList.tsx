import { ComponentProps } from 'react'

import { cn } from '../../../../lib/twMerge/cn'
import { CourseList } from '../../../../types'
import CourseCategoryCard from './CourseCategoryCard'
interface Props extends ComponentProps<'div'> {
  courseList: CourseList

  watchRoute: string
}
export default function CourseCategoryList({
  courseList,
  watchRoute,
  className,
}: Props) {
  return (
    <div className={cn(`gap-3 flex max-md:items-stretch pb-4 ${className}`)}>
      {courseList.length > 0 ? (
        courseList.map(({ courseRef, ...others }, index) => (
          <CourseCategoryCard
            key={courseRef + index}
            watchRoute={`${watchRoute}`}
            courseRef={courseRef}
            {...others}
          />
        ))
      ) : (
        <p className="text-center w-full py-4">No Courses found</p>
      )}
    </div>
  )
}
