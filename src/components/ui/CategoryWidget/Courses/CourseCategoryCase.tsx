import { forwardRef, ReactNode, Ref, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { useGetCourseCategories } from '../../../../hooks/apiQueryHooks/eduQueryHooks';
import { useCustomMedia } from '../../../../hooks/useCustomMedia';
import FilterNavbar from '../../CommonWidget/FilterNavbar';
import SlideController from '../../CommonWidget/SlideController';
import ViewMore from '../../CommonWidget/ViewMore';
import CourseCategoryCardPlaceholder from './CourseCategoryCardPlaceholder';

type Props = {
  headerChildren?: ReactNode;
  hideTypeAsText?: boolean;
  children: ReactNode;
  thirdPartyLogo: string;
  tabQueryName: string;
  viewAllCoursesRoute?: string;
  isLoading?: boolean;
};

export default forwardRef(function CourseCategoryCase(
  {
    headerChildren,
    hideTypeAsText = false,
    children,
    thirdPartyLogo,
    tabQueryName,
    viewAllCoursesRoute,
    isLoading,
  }: Props,
  ref: Ref<HTMLDivElement>,
) {
  const [start, setStart] = useState(false);

  const navigate = useNavigate();

  const { screenSize } = useCustomMedia();

  const { data } = useGetCourseCategories({
    staleTime: 1000 * 60 * 60 * 24,
    cacheTime: 1000 * 60 * 60 * 25,
  });
  return (
    <div className="mb-16 flex flex-col items-stretch">
      <div className="mb-4 max-w-[125px]">
        {!hideTypeAsText && (
          <img
            src={thirdPartyLogo}
            className="h-full w-full"
            alt="partner company logo"
            loading="lazy"
          />
        )}
      </div>
      {headerChildren}
      <SlideController
        dataLength={(data?.data || [])?.length}
        isMobile={screenSize < 768}
        small
      >
        {(data?.data || [])?.length > 0 ? (
          <FilterNavbar tabQueryName={tabQueryName} list={data?.data || []} />
        ) : (
          <></>
        )}
      </SlideController>
      <div
        ref={ref}
        className="w-full overflow-x-auto max-md:max-w-full sm:overflow-x-hidden"
      >
        {isLoading ? (
          <div className="flex gap-3 pb-4 max-md:items-stretch ">
            {Array.from({ length: 4 }, (_, index) => (
              <CourseCategoryCardPlaceholder key={index} />
            ))}
          </div>
        ) : (
          children
        )}
      </div>
      {viewAllCoursesRoute && (
        <div
          onMouseEnter={() => setStart(true)}
          onMouseLeave={() => setStart(false)}
          className="mt-[24px] w-fit bg-white px-6 py-2"
        >
          <ViewMore
            onClick={() => navigate(`${viewAllCoursesRoute}`)}
            message="Browse All"
            start={start}
          />
        </div>
      )}
    </div>
  );
});
