import { ArrowBackIcon, ArrowRightIcon } from '../../../assets/icons'
import { useGetPagination } from '../../../hooks/useGetPagination'
import { PaginationType } from '../../../types'
import { AnimatedPlaceholder } from '../CommonWidget/Loader'

const PaginationNavigator = ({
  isLoading,
  pagination,
  currentPage,
  handlePageChange,
}: {
  isLoading: boolean
  currentPage: number
  pagination: PaginationType | undefined
  handlePageChange: (argument: number) => void
}) => {
  const totalPages = pagination?.totalPages ?? 0 // Fallback to 0 if totalPages is undefined
  const pages = useGetPagination(currentPage, totalPages)
  if (isLoading)
    return (
      <div className="px-4 max-w-[1000px]">
        <AnimatedPlaceholder className=" w-[300px] mr-auto h-[40px]  rounded-[10px]" />
      </div>
    )
  if (!pagination) return null
  if (pagination?.total === 0) return null
  return (
    <div className="text-[14px] text-black">
      <div className="flex items-center gap-4 h-12">
        <button
          disabled={currentPage === 1}
          onClick={() => handlePageChange(currentPage - 1)}
          type="button"
          className="w-[37px] aspect-w-1 aspect-h-1 flex items-center justify-center cursor-pointer disabled:cursor-not-allowed focus:outline-none"
        >
          <ArrowBackIcon className="w-6 h-6" />
        </button>
        {pages.map((page, index) => (
          <button
            key={index}
            onClick={() => typeof page === 'number' && handlePageChange(page)}
            type="button"
            className={`rounded-full min-w-[37px] min-h-[37px] h-[37px] aspect-w-1 aspect-h-1 flex items-center justify-center transition-all duration-200 ease-in-out cursor-pointer focus:outline-none ${
              currentPage === page
                ? 'bg-primary text-[#fff]'
                : 'hover:text-primary'
            } ${
              page === '...'
                ? 'outline-none hover:text-black cursor-default'
                : ''
            }`}
          >
            <span className="mt-1">
              {page === '...' ? '...' : String(page).padStart(2, '0')}
            </span>
          </button>
        ))}
        <button
          disabled={currentPage === pagination?.totalPages}
          onClick={() => handlePageChange(currentPage + 1)}
          type="button"
          className="w-[37px] aspect-w-1 aspect-h-1 flex items-center justify-center cursor-pointer disabled:cursor-not-allowed focus:outline-none"
        >
          <ArrowRightIcon className="fill-blackSix w-6 h-6" />
        </button>
      </div>
    </div>
  )
}

export default PaginationNavigator
