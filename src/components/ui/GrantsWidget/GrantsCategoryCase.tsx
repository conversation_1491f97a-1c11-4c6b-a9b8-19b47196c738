import { forwardRef, ReactNode, Ref } from 'react';

import { useGetSDGs } from '../../../hooks/apiQueryHooks/eduQueryHooks';
import { useCustomMedia } from '../../../hooks/useCustomMedia';
import CourseCategoryCardPlaceholder from '../CategoryWidget/Courses/CourseCategoryCardPlaceholder';
import FilterNavbar from '../CommonWidget/FilterNavbar';
import SlideController from '../CommonWidget/SlideController';

type Props = {
  children: ReactNode;
  tabQueryName: string;
  isLoading?: boolean;
};

export default forwardRef(function GrantsCategoryCase(
  { children, tabQueryName, isLoading }: Props,
  ref: Ref<HTMLDivElement>,
) {
  const { screenSize } = useCustomMedia();

  const { data } = useGetSDGs({
    staleTime: 1000 * 60 * 60 * 24,
    cacheTime: 1000 * 60 * 60 * 25,
  });
  return (
    <div className="mb-16 flex flex-col items-stretch">
      <SlideController
        dataLength={(data?.data || [])?.length}
        isMobile={screenSize < 768}
        small
      >
        {(data?.data || [])?.length > 0 ? (
          <FilterNavbar tabQueryName={tabQueryName} list={data?.data || []} />
        ) : (
          <></>
        )}
      </SlideController>
      <div
        ref={ref}
        className="w-full overflow-x-auto max-md:max-w-full sm:overflow-x-hidden"
      >
        {isLoading ? (
          <div className="flex gap-3 pb-4 max-md:items-stretch ">
            {Array.from({ length: 4 }, (_, index) => (
              <CourseCategoryCardPlaceholder key={index} />
            ))}
          </div>
        ) : (
          children
        )}
      </div>
    </div>
  );
});
