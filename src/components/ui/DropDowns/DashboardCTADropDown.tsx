import { MouseEvent } from 'react';
import { Link, useNavigate, useParams } from 'react-router-dom';

import { useAppContext } from '../../../context/event/AppEventContext';
import { useHandleCanCreateTeam } from '../../../features/Teams/hooks/useHandleCanCreateTeam';
import { Spinner } from '../CommonWidget/Loader';
import { userRole } from '@/data/constants';
import { useUserContext } from '@/context/user/UserContext';
import { useHandleCanCreateProject } from '@/features/Categories/hooks/useHandleCanCreateProject';

export default function DashboardCTADropDown() {
  const { accountType } = useParams();
  const { currentAccountType, setShowModalHandler } = useAppContext();
  const { hasBenefactorRole } = useUserContext();
  const navigate = useNavigate();

  const { handleTrigger: teamHandleTrigger, isFetching } =
    useHandleCanCreateTeam({
      onSuccess: () => {
        navigate(`/${currentAccountType}/teams/add-teams`);
        setShowModalHandler('');
      },
    });

  const {
    handleTrigger: projectHandleTrigger,
    isFetching: isFetchingCanCreateProject,
  } = useHandleCanCreateProject({
    onSuccess: () => {
      setShowModalHandler('ShouldSkipChooseProblemStatement');
    },
  });

  const handleTeamsClick = (e: MouseEvent<HTMLElement>) => {
    e.preventDefault();
    if (
      accountType?.toLowerCase() === userRole.benefactor.toLowerCase() &&
      hasBenefactorRole
    ) {
      navigate(`/${currentAccountType}/dashboard-grant/add-grant`);
      setShowModalHandler('');
    } else {
      teamHandleTrigger();
    }
  };
  const handleProjectsClick = (e: MouseEvent<HTMLElement>) => {
    e.preventDefault();
    projectHandleTrigger();
  };

  return (
    <div className="relative min-h-[100px] w-[300px] rounded-[8px] bg-white py-4 text-black shadow-lg">
      <ul>
        <li
          onClick={handleTeamsClick}
          className="mb-2 cursor-pointer border-b-black px-4 py-1 hover:bg-gray-5 hover:text-primary"
        >
          {accountType?.toLowerCase() === userRole.benefactor.toLowerCase() &&
          hasBenefactorRole ? (
            <span>Grant</span>
          ) : (
            <>{isFetching ? <Spinner className="mx-0" /> : 'Team'}</>
          )}
        </li>
        <li
          onClick={handleProjectsClick}
          className="mb-2 cursor-pointer border-b-black px-4 py-1 hover:bg-gray-5 hover:text-primary"
        >
          <>
            {isFetchingCanCreateProject ? (
              <Spinner className="mx-0" />
            ) : (
              'Project'
            )}
          </>
        </li>

        <Link
          to={`/${currentAccountType}/dashboard-statements/add-problem-statement`}
        >
          <li
            onClick={() => setShowModalHandler('')}
            className="mb-2 border-b-black px-4 py-1 hover:bg-gray-5 hover:text-primary"
          >
            Problem statement
          </li>
        </Link>
      </ul>
    </div>
  );
}
