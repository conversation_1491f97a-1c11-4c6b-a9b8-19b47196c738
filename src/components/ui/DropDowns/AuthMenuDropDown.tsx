import { useKeycloak } from '@react-keycloak/web';
import { SetStateAction } from 'react';
import { Link } from 'react-router-dom';

import { useAppContext } from '../../../context/event/AppEventContext';
import { useUserContext } from '../../../context/user/UserContext';
import { userRole } from '../../../data/constants';
import { UserRoleType } from '../../../types';
import { Helper } from '../../../utils/helpers';

type Props = {
  setShowDropDown(value: SetStateAction<boolean>): void;
};
export default function AuthMenuDropDown({ setShowDropDown }: Props) {
  const { keycloak } = useKeycloak();
  const { roles, hasAllAccountType } = useUserContext();
  const {
    currentAccountType,
    setCurrentAccountTypeHandler,
    setIsUserAccountTypeChoiceHandler,
  } = useAppContext();
  return (
    <div className="relative min-h-[100px] min-w-[200px] rounded-[8px] bg-white py-4 font-[500] text-black xs:whitespace-nowrap">
      <ul>
        {roles?.map(accountType => {
          if (accountType === userRole.user) return null;
          return (
            <Link
              key={accountType}
              to={`/${accountType.toLowerCase()}/dashboard-overview`}
            >
              <li
                onClick={() => {
                  setShowDropDown(false);
                  if (currentAccountType === accountType) {
                    setIsUserAccountTypeChoiceHandler(true);
                    setCurrentAccountTypeHandler(
                      currentAccountType as UserRoleType,
                    );
                  }
                  setIsUserAccountTypeChoiceHandler(true);
                  setCurrentAccountTypeHandler(accountType as UserRoleType);
                }}
                className="mb-2 border-b-black px-4 py-1 hover:bg-gray-5 hover:text-primary"
              >
                {Helper.capitalizeString(accountType)} Dashboard
              </li>
            </Link>
          );
        })}
        {!hasAllAccountType && (
          <Link to="/user-onboarding">
            <li
              onClick={() => setShowDropDown(false)}
              className="mb-2 border-b-black px-4 py-1 hover:bg-gray-5 hover:text-primary"
            >
              Add account
            </li>
          </Link>
        )}
        <li
          onClick={() => keycloak.logout()}
          className="mb-2 cursor-pointer border-b-black px-4 py-1 hover:bg-gray-5 hover:text-primary"
        >
          Log Out
        </li>
      </ul>
      {/* <span
        className="w-[12px] h-[12px] rotate-45 absolute top-0 right-[85px] 
        -translate-y-[50%] bg-gray-5"
      /> */}
    </div>
  );
}
