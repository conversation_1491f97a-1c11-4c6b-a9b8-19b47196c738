import { useKeycloak } from '@react-keycloak/web';
import { Link } from 'react-router-dom';

import { useAppContext } from '../../../context/event/AppEventContext';
import { useUserContext } from '../../../context/user/UserContext';
import { userRole } from '../../../data/constants';
import { UserRoleType } from '../../../types';
import { Helper } from '../../../utils/helpers';

export default function DashboardAuthMenuDropDown() {
  const { roles, hasAllAccountType } = useUserContext();
  const { keycloak } = useKeycloak();
  const {
    currentAccountType,
    setCurrentAccountTypeHandler,
    setShowModalHandler,
  } = useAppContext();
  return (
    <div className="relative min-h-[100px] w-[300px] rounded-[8px] bg-white py-4 text-black shadow-lg">
      <ul>
        {roles?.map(accountType => {
          if (accountType === userRole.user) return null;
          return (
            <Link
              key={accountType}
              to={`/${accountType.toLowerCase()}/dashboard-overview`}
            >
              <li
                onClick={() => {
                  setShowModalHandler('');
                  if (currentAccountType === accountType) {
                    setCurrentAccountTypeHandler(
                      currentAccountType as UserRoleType,
                    );
                  }
                  setCurrentAccountTypeHandler(accountType as UserRoleType);
                }}
                className="mb-2 border-b-black px-4 py-1 hover:bg-gray-5 hover:text-primary"
              >
                {Helper.capitalizeString(accountType)} Dashboard
              </li>
            </Link>
          );
        })}
        {!hasAllAccountType && (
          <Link to="/user-onboarding">
            <li
              onClick={() => setShowModalHandler('')}
              className="mb-2 border-b-black px-4 py-1 hover:bg-gray-5 hover:text-primary"
            >
              Add account
            </li>
          </Link>
        )}
        <li
          onClick={() => keycloak.logout()}
          className="mb-2 cursor-pointer border-b-black px-4 py-1 hover:bg-gray-5 hover:text-primary"
        >
          Log Out
        </li>
      </ul>
    </div>
  );
}
