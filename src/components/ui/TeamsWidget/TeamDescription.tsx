import { useAppContext } from '@/context/event/AppEventContext';
import { PencilIcon } from '@/features/Profile/assets';
import useGetTeamLevelBasedAccess from '@/hooks/useGetTeamLevelBasedAccess';

export default function TeamDescription({
  description,
}: {
  description: string | undefined;
}) {
  const { setShowModalHandler } = useAppContext();
  const { isLoggedInUserATeamLead } = useGetTeamLevelBasedAccess();

  return (
    <div className="text-[16px] leading-[26px] text-stone-950">
      <div className="mb-4 flex justify-between gap-4">
        <h5>Team Description</h5>
        {isLoggedInUserATeamLead && (
          <button
            className="flex cursor-pointer items-center gap-2 leading-none text-primary"
            onClick={() => setShowModalHandler('UpdateProjectTeamModal')}
          >
            Edit Team <PencilIcon className="stroke-primary" />
          </button>
        )}
      </div>
      {description ? (
        <p className="text-[14px] font-[400]">Introduction: {description}</p>
      ) : (
        <p className="text-[12px]">N/A</p>
      )}
    </div>
  );
}
