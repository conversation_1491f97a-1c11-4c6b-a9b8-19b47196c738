import { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import Select from 'react-select';

import { ChatIcon2, DeleteIcon } from '../../../assets/icons';
import { profilePlaceholder } from '../../../assets/images';
import { useAppContext } from '../../../context/event/AppEventContext';
import { CategoryHelper } from '../../../features/Categories/utils/helpers';
import {
  teamLevelUserRoleOptions,
  useUpdateTeamMember,
} from '../../../features/Teams';
import { useCreateChatMessage } from '../../../hooks/apiQueryHooks/chatQueryHooks';
import { useCustomMedia } from '../../../hooks/useCustomMedia';
import useGetTeamLevelBasedAccess from '../../../hooks/useGetTeamLevelBasedAccess';
import useHandleApiFeebackWithToast from '../../../hooks/useHandleApiFeebackWithToast';
import { useHandleQueryParams } from '../../../hooks/useHandleQueryParams';
import {
  selectStyles,
  smallMediaSelectStyles,
} from '../../../lib/reactSelect/react-select-style';
import {
  ArrayIndexElement,
  DataResponse,
  ExtractData,
  ServerTeamMembers,
  TeamlevelUserRoleType,
} from '../../../types';
import { GET_TEAM_MEMBERS_QUERY } from '../../../utils/queryKeys';
import CoverElement from '../CommonWidget/CoverElement';
import useManageActiveSubscription from '@/hooks/useManageActiveSubscription';

type Props = {
  isProjectTeamPageVariant?: boolean;
  index: number;
};

export function TeamMemberCard({
  userId,
  teamRole,
  userDTO,
  index,
}: Props &
  Pick<
    ArrayIndexElement<ExtractData<ServerTeamMembers>>,
    'userDTO' | 'userId' | 'teamRole'
  >) {
  const {
    isInactiveOrExpiredOrCancelled,
    basicTeamMemberLimit,
    innovatorTeamMemberLimit,
    acceleratorTeamMemberLimit,
    isAccelerator,
    isInnovator,
  } = useManageActiveSubscription();

  const isInActiveForbasicPackage =
    isInactiveOrExpiredOrCancelled &&
    (index ? index > basicTeamMemberLimit : false);
  const isInActiveForInnovatorPackage = index
    ? index > innovatorTeamMemberLimit && isInnovator
    : false;
  const isInActiveForAcceleratorPackage = index
    ? index > acceleratorTeamMemberLimit && isAccelerator
    : false;

  const { screenSize } = useCustomMedia();
  const { handleQuery } = useHandleQueryParams();
  const navigate = useNavigate();
  const { setShowModalHandler, currentAccountType } = useAppContext();
  const { teamRef } = useParams();
  const [value, setValue] = useState(
    CategoryHelper.createReponseOptions(teamRole),
  );
  const { mutate } = useUpdateTeamMember({
    ...useHandleApiFeebackWithToast({
      queryKey: [GET_TEAM_MEMBERS_QUERY],
    }),
  });
  const onChange = (newValue: TeamlevelUserRoleType) => {
    mutate({
      teamRole: newValue,
      userId: userId || '',
      teamRef: teamRef || '',
    });
    setValue(CategoryHelper.createReponseOptions(newValue));
  };
  const {
    isLoggedInUserATeamLead,
    isLoggedInUserThisTeamMember,
    isLoggedInUserATeamMember,
    isCreatorAtEachMemberLevel,
  } = useGetTeamLevelBasedAccess({
    teamMemberUserId: userId,
  });
  const next = (res: DataResponse<any>) => {
    navigate(`/${currentAccountType}/chat?chat_room_id=${res?.data?._id}`);
  };
  const { mutate: createChatRoom } = useCreateChatMessage({
    ...useHandleApiFeebackWithToast({ next, noSuccessToast: true }),
  });
  return (
    <div className={`relative flex flex-col justify-between gap-3 sm:flex-row`}>
      <div
        className="flex cursor-pointer items-center gap-2"
        onClick={() => {
          setShowModalHandler('ViewTeamMemberModal');
          handleQuery({ id: userId });
        }}
      >
        <img
          loading="lazy"
          src={userDTO?.profilePicture || profilePlaceholder}
          className="aspect-square w-12 max-w-full shrink-0 overflow-hidden 
          rounded-[50%] object-contain object-center"
        />
        <div className="my-auto grow self-center whitespace-nowrap text-base font-[500] text-black">
          {`${userDTO.firstName} ${userDTO.lastName || ''}`}
        </div>
      </div>
      <div className="flex items-center gap-x-3">
        <div className="relative w-[240px]">
          <Select
            value={value}
            options={teamLevelUserRoleOptions}
            styles={screenSize > 768 ? selectStyles : smallMediaSelectStyles}
            isSearchable
            onChange={option =>
              onChange(
                (typeof option !== 'string' && option?.value) || 'TEAM_MEMBER',
              )
            }
            isClearable={false}
            placeholder="Role"
            menuPortalTarget={document.body}
            menuPosition={'absolute'}
            menuPlacement={'auto'}
            menuShouldScrollIntoView={false}
            defaultValue={CategoryHelper.createReponseOptions(teamRole)}
          />
          {(!isLoggedInUserATeamLead || isCreatorAtEachMemberLevel) && (
            <CoverElement />
          )}
        </div>
        {userDTO?.university?.name && (
          <input
            className="input max-w-[250px]"
            value={userDTO?.university?.name}
            disabled
          />
        )}
        {!isLoggedInUserThisTeamMember && (
          <div className="flex items-center gap-x-3">
            {isLoggedInUserATeamMember && (
              <ChatIcon2
                onClick={() => createChatRoom({ receiverId: userId })}
                className="h-5 w-5 cursor-pointer fill-graySixteen hover:fill-primary"
              />
            )}
            {isLoggedInUserATeamLead && (
              <DeleteIcon
                onClick={() => {
                  setShowModalHandler('DeleteTeamMember');
                  navigate(`?id=${userId}`);
                }}
                className="h-5 w-5 cursor-pointer stroke-disabled hover:stroke-primary"
              />
            )}
          </div>
        )}
      </div>
      {isInActiveForbasicPackage ||
      isInActiveForInnovatorPackage ||
      isInActiveForAcceleratorPackage ? (
        <CoverElement
          className="cursor-pointer bg-[#fff]/60"
          onClick={() => {
            !isInactiveOrExpiredOrCancelled
              ? setShowModalHandler('MemberLimitModal')
              : setShowModalHandler('InactiveSubscriptionModal');
          }}
        />
      ) : null}
    </div>
  );
}
