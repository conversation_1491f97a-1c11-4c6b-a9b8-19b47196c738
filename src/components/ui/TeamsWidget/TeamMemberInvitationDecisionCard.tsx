import { useState } from 'react';
import { useParams } from 'react-router-dom';

import { profilePlaceholder } from '../../../assets/images';
import { useProcessRequestToJoinProject } from '../../../features/Categories/hooks/apiQueryHooks/eduQueryHooks';
import useHandleApiFeebackWithToast from '../../../hooks/useHandleApiFeebackWithToast';
import { GET_PROJECT_MEMBERS_QUERY } from '../../../utils/queryKeys';
import Button from '../ButtonComponent';

type Props = {
  userId: string;
  firstName: string;
  lastName: string;
};

export function TeamMemberInvitationDecisionCard({
  userId,
  firstName,
  lastName,
}: Props) {
  const [decision, setDecision] = useState('');
  const { projectRef } = useParams();
  const { isFetching } = useProcessRequestToJoinProject(
    {
      userId: userId || '',
      decision,
      projectRef: projectRef || '',
    },
    {
      enabled: !!decision,
      ...useHandleApiFeebackWithToast({
        queryKey: [GET_PROJECT_MEMBERS_QUERY],
      }),
    },
  );
  const onChange = (decision: 'ACCEPT' | 'REJECT') => {
    setDecision(decision);
  };
  return (
    <div className="flex max-w-[407px] items-center justify-between gap-5">
      <div className="flex items-stretch justify-between gap-3">
        <img
          loading="lazy"
          src={profilePlaceholder}
          className="aspect-square w-12 max-w-full shrink-0 overflow-hidden rounded-[50%] object-contain object-center"
        />
        <div className="my-auto grow self-center whitespace-nowrap text-base text-neutral-500">
          {`${firstName} ${lastName && lastName.slice(0, 1).toUpperCase()}.`}
        </div>
      </div>
      <div className="flex items-center gap-x-2 text-white">
        <Button
          disabled={isFetching}
          onClick={() => onChange('ACCEPT')}
          className="h-[30px] w-full max-w-[100px] bg-successTwo disabled:bg-white disabled:text-black"
        >
          <p className="text-[10px]">Accept</p>
        </Button>
        <Button
          disabled={isFetching}
          onClick={() => onChange('REJECT')}
          className="h-[30px] w-full max-w-[100px] bg-dangerTwo disabled:bg-white disabled:text-black"
        >
          <p className="text-[10px]">Decline</p>
        </Button>
      </div>
    </div>
  );
}
