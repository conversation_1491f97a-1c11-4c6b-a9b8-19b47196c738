import { Avatar, AvatarGroup } from '@mui/material';
import { MouseEvent } from 'react';
import { useNavigate } from 'react-router-dom';

import { AddIcon, Viewicon } from '../../../assets/icons';
import { profilePlaceholder } from '../../../assets/images';
import { useAppContext } from '../../../context/event/AppEventContext';
import { useGetTeamMembers } from '../../../features/Teams/hooks/apiQueryhooks/eduQueryHooks';
import { useHandleCanAddMember } from '../../../features/Teams/hooks/useHandleCanAddMember';
import useGetTeamLevelBasedAccess from '../../../hooks/useGetTeamLevelBasedAccess';
import { useHandleQueryParams } from '../../../hooks/useHandleQueryParams';
import { ArrayIndexElement, ExtractData, ServerTeams } from '../../../types';
import { Helper } from '../../../utils/helpers';
import { Spinner } from '../CommonWidget/Loader';
import { DialogWithTooltip } from '../CommonWidget/DialogWithTooltip';

type Props = {
  isPreviewState?: boolean;
  selectedTeamRef?: string;
  handleSelect?: (id: string) => void;
};

export function TeamPreviewCard({
  isPreviewState,
  handleSelect,
  selectedTeamRef,
  teamName,
  teamRef,
}: Props &
  Pick<
    ArrayIndexElement<ExtractData<ServerTeams>['teams']>,
    'teamRef' | 'teamName'
  >) {
  const { currentAccountType } = useAppContext();
  const navigate = useNavigate();
  const { query } = useHandleQueryParams();
  const { data: teamMembers } = useGetTeamMembers({
    teamRef: teamRef || '',
    inviteStatus: 'JOINED',
  });
  const { isLoadingTeamMembers, isLoadingUser, isLoggedInUserATeamLead } =
    useGetTeamLevelBasedAccess({
      currentTeamRef: teamRef,
    });

  const useHandleCanAddMemberForTeamPreviewCard = () =>
    useHandleCanAddMember({
      teamRef,
      onSuccess: () =>
        navigate(`/${currentAccountType}/teams/${teamRef}/add-team-member`),
    });

  const { handleTrigger, isFetching } =
    useHandleCanAddMemberForTeamPreviewCard();

  const handleAddMember = (e: MouseEvent<SVGElement>) => {
    e.preventDefault();
    handleTrigger();
  };

  if (isLoadingUser || isLoadingTeamMembers)
    return (
      <div
        className="flex h-[100px] w-full max-w-[444px]  items-center 
      justify-center  self-stretch"
      >
        <Spinner />
      </div>
    );
  return (
    <div
      onClick={() => handleSelect && handleSelect(teamRef || '')}
      className={`${
        isPreviewState
          ? 'mb-6'
          : `${
              selectedTeamRef === teamRef
                ? 'border-primary bg-orange-300 bg-opacity-10'
                : `${
                    !!selectedTeamRef && 'border-transparent opacity-50'
                  } border-transparent`
            } cursor-pointer border px-[6px] py-2`
      } `}
    >
      <div className="mb-1 flex items-center gap-x-4">
        <Avatar
          sx={{
            width: 50,
            height: 50,
            bgcolor: Helper.stringToColor(teamName || ''),
          }}
        >
          {teamName?.slice(0, 1).toUpperCase()}
        </Avatar>
        <AvatarGroup total={teamMembers?.data?.length || 0}>
          {teamMembers?.data &&
            teamMembers?.data
              .map(({ userDTO: { firstName, profilePicture }, userId }) => (
                <Avatar src={profilePicture || profilePlaceholder} key={userId}>
                  {firstName.slice(0, 1).toUpperCase()}
                </Avatar>
              ))
              .slice(0, 5)}
        </AvatarGroup>

        <div className="flex items-center gap-x-2">
          {!query.get('view_project') && (
            <div className="group relative">
              <DialogWithTooltip
                Icon={
                  <Viewicon
                    onClick={() => navigate(`team/${teamRef}`)}
                    className="cursor-pointer "
                  />
                }
                title="View team"
              />
            </div>
          )}
          {isPreviewState && isLoggedInUserATeamLead && (
            <div className="group relative">
              {isFetching ? (
                <Spinner />
              ) : (
                <>
                  <DialogWithTooltip
                    Icon={
                      <AddIcon
                        onClick={handleAddMember}
                        className="cursor-pointer"
                      />
                    }
                    title="Add team members"
                  />
                </>
              )}
            </div>
          )}
        </div>
      </div>
      <h4 className="mb-2 bg-gradient-to-r from-orange-5 to-orange-15 bg-clip-text font-semibold  text-transparent">
        {teamName}
      </h4>
    </div>
  );
}
