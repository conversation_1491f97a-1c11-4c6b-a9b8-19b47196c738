import { motion } from 'framer-motion';
import { useParams } from 'react-router-dom';

import { CancelIcon } from '../../../assets/icons';
import { useAppContext } from '../../../context/event/AppEventContext';
import { useGetTeamMembers } from '../../../features/Teams';
import { Spinner } from '../CommonWidget/Loader';
import { TeamMemberPreviewCard } from './TeamMemberPreviewCard';

export function TeamMemberJoinedOrPendingWidget() {
  const { teamRef } = useParams();
  const { setShowModalHandler } = useAppContext();
  const { data: teamMembers, isLoading } = useGetTeamMembers({
    teamRef: teamRef || '',
  });
  if (isLoading)
    return (
      <div
        className="flex h-[400px] w-full max-w-[444px]  items-center
      justify-center  self-stretch"
      >
        <Spinner />
      </div>
    );
  return teamMembers?.data && teamMembers?.data.length > 0 ? (
    <motion.div
      exit={{ opacity: 0, scale: 0 }}
      initial={{ opacity: 0, x: '100vw' }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay: 0.5 }}
      className="fixed top-[120px] mx-auto max-h-[80vh] w-[94%] max-w-[458px] overflow-y-auto bg-white px-6 py-4 
       shadow-lg xs:right-4"
    >
      <div className=" py-6">
        <div className="mb-4 bg-gradient-to-r from-orange-5 to-orange-15 bg-clip-text text-base font-bold capitalize  text-transparent">
          Team Members
        </div>

        {teamMembers &&
          teamMembers.data?.map(
            ({ inviteStatus, userDTO: { firstName, lastName }, userId }) => (
              <div key={userId} className="mb-6">
                <TeamMemberPreviewCard
                  firstName={firstName}
                  lastName={lastName}
                  inviteStatus={inviteStatus}
                />
              </div>
            ),
          )}
      </div>
      <CancelIcon
        onClick={() => setShowModalHandler('')}
        className="absolute right-4 top-4 cursor-pointer stroke-black"
      />
    </motion.div>
  ) : null;
}
