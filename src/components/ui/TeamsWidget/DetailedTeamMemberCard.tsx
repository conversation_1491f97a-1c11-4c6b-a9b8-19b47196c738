import { useNavigate } from 'react-router-dom';

import { profilePlaceholder } from '../../../assets/images';
import { useAppContext } from '../../../context/event/AppEventContext';
import useGetTeamLevelBasedAccess from '../../../hooks/useGetTeamLevelBasedAccess';
import { TeamlevelUserRoleType } from '../../../types';
import Button from '../ButtonComponent';

type Props = {
  teamRole: TeamlevelUserRoleType;
  firstName: string;
  lastName: string;
};
export function DetailedTeamMemberCard({ firstName, lastName }: Props) {
  const { currentAccountType } = useAppContext();
  const navigate = useNavigate();
  const { isLoggedInUserATeamLead, isLoggedInUserThisTeamMember } =
    useGetTeamLevelBasedAccess();
  return (
    <div className="flex w-[24%] min-w-[200px] flex-col items-stretch  self-stretch max-md:w-full">
      <img src={profilePlaceholder} className="h-[257px] w-full" />
      <div className="bg-[#fff]">
        <div className=" flex flex-col gap-1 px-5 py-4 text-center">
          <p className="text-[18px] font-medium leading-[24px]">
            {' '}
            {`${firstName} ${lastName}`}
          </p>
          <p className="text-[14px] leading-[22px] text-primary">
            {isLoggedInUserATeamLead ? 'Team Lead' : 'Team Member'}
          </p>
        </div>
        {!isLoggedInUserThisTeamMember && (
          <div className="border-t border-t-graySeven px-5 pb-5 pt-5">
            <Button
              onClick={() => navigate(`/${currentAccountType}/chat`)}
              className="mx-auto h-[33px] w-full max-w-[145px] whitespace-nowrap rounded-[2.7px] border-[1px] border-grayTen bg-transparent text-[12px] hover:border-primary hover:bg-primary hover:text-white"
            >
              Send A Message
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
