import { useNavigate, useParams } from 'react-router-dom';
import {
  createContext,
  forwardRef,
  ReactNode,
  Ref,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import { noTeamsPlaceholder } from '../../../assets/images';

import Button from '../ButtonComponent';
import { TooltipWidget } from '../TooltipWidget/TooltipWidget';

import {
  useAddTeamToProject,
  useAddExternalTeamToProject,
} from '../../../features/Categories';
import { useAppContext } from '../../../context/event/AppEventContext';
import {
  useGetProjectTeams,
  useRemoveTeamFromProject,
} from '../../../features/Categories/hooks/apiQueryHooks/eduQueryHooks';
import { useHandleQueryParams } from '../../../hooks/useHandleQueryParams';
import useHandleApiFeebackWithToast from '../../../hooks/useHandleApiFeebackWithToast';

import { Team } from '../../../features/Teams/type';
import { GET_PROJECT_TEAMS_QUERY } from '../../../utils/queryKeys';
import CoverElement from '../CommonWidget/CoverElement';
import useManageActiveSubscription from '@/hooks/useManageActiveSubscription';
import { useGetTalent } from '@/features/Talent/hooks/apiQueryhooks/talentQueryHook';
import { useGetTeamMembers } from '@/features/Teams';
import { cn } from '@/lib/twMerge/cn';
import { Spinner } from '../CommonWidget/Loader';
import { useHandleCanAddMember } from '@/features/Teams/hooks/useHandleCanAddMember';
import { usePaginationHandler } from '@/hooks/usePaginationHandler';

interface Props {
  children: ReactNode;
  hideToolTip?: boolean;
  hideDescription?: boolean;
  team?: Partial<Team>;
  index?: number;
  pageIndex?: number;
}

const Context = createContext({} as Partial<Team>);

export default forwardRef(function TeamCard(
  { children, hideToolTip = false, team, pageIndex, index }: Props,
  ref: Ref<HTMLDivElement>,
) {
  const { teamName, teamRef, image, teamDescription, principalLocation } =
    team || {};
  const [isHovered, setIsHovered] = useState(false);
  const { currentPage, pageSize, pageSizeForInfiniteQuery } =
    usePaginationHandler();
  const cardRef = useRef<HTMLDivElement>(null);
  const {
    isInactiveOrExpiredOrCancelled,
    basicTeamLimit,
    innovatorTeamLimit,
    acceleratorTeamLimit,
    isAccelerator,
    isInnovator,
  } = useManageActiveSubscription();
  const { setShowModalHandler } = useAppContext();

  const cardIndex = index ? currentPage * pageSize - pageSize + index : 0;

  const cardIndexForInfiniteQuery =
    pageIndex && index
      ? (pageIndex + 1) * pageSizeForInfiniteQuery -
        pageSizeForInfiniteQuery +
        index
      : 0;

  const isInActiveForbasicPackage =
    isInactiveOrExpiredOrCancelled &&
    (cardIndexForInfiniteQuery || cardIndex) > basicTeamLimit;
  const isInActiveForInnovatorPackage =
    (cardIndexForInfiniteQuery || cardIndex) > innovatorTeamLimit &&
    isInnovator;

  const isInActiveForAcceleratorPackage =
    (cardIndexForInfiniteQuery || cardIndex) > acceleratorTeamLimit &&
    isAccelerator;

  useEffect(() => {
    const img = new Image();
    img.src = image || '';
  }, [image]);

  return (
    <Context.Provider
      value={{ teamRef, teamName, principalLocation, teamDescription }}
    >
      <div className="relative flex w-[19%] min-w-[244px] items-stretch">
        <div
          className="flex w-full flex-col 
        self-stretch rounded-[4px] border-[2.2px] border-grayNineTeen bg-white p-4 shadow-md max-md:w-full"
          ref={cardRef}
          onMouseOver={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          <div ref={ref}>
            <img
              loading="lazy"
              src={image || noTeamsPlaceholder}
              className={`aspect-[1.23] max-h-[388.68px] w-[210px] max-w-full self-start overflow-hidden object-cover object-center`}
            />
          </div>
          {children}
        </div>
        {isInActiveForbasicPackage ||
        isInActiveForInnovatorPackage ||
        isInActiveForAcceleratorPackage ? (
          <CoverElement
            className="cursor-pointer bg-[#fff]/60"
            onClick={() => {
              !isInactiveOrExpiredOrCancelled
                ? setShowModalHandler('TeamLimitModal')
                : setShowModalHandler('InactiveSubscriptionModal');
            }}
          />
        ) : null}
      </div>
      <TooltipWidget
        show={isHovered && !hideToolTip}
        parentRef={cardRef.current}
        className="w-full max-w-[300px] rounded border border-primary p-0 shadow-[0px_2px_7px_0px_#0000001F]"
      >
        <div className="relative flex flex-col gap-2">
          <p className="sticky top-0 flex-1 bg-white px-4 pt-4 text-[14px] font-bold">
            {teamName || ''}
          </p>

          <div className="flex-1 whitespace-normal px-4 pb-4 text-[14px] text-subText">
            {teamDescription || 'N/A'}
          </div>
        </div>
      </TooltipWidget>
    </Context.Provider>
  );
});

export const TeamCardForAvailableTeam = forwardRef(
  function TeamCardForAvailableTeam(
    { children, hideToolTip = false, team }: Props,
    ref: Ref<HTMLDivElement>,
  ) {
    const { teamName, teamRef, image, teamDescription, principalLocation } =
      team || {};
    const [isHovered, setIsHovered] = useState(false);
    const cardRef = useRef<HTMLDivElement>(null);

    return (
      <Context.Provider
        value={{ teamRef, teamName, principalLocation, teamDescription }}
      >
        <div className="relative flex w-[19%] min-w-[244px] items-stretch">
          <div
            className="flex w-full flex-col 
        self-stretch rounded-[4px] border-[2.2px] border-grayNineTeen bg-white p-4 shadow-md max-md:w-full"
            ref={cardRef}
            onMouseOver={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          >
            <div ref={ref}>
              <img
                loading="lazy"
                src={image || noTeamsPlaceholder}
                className="aspect-[1.23] max-h-[388.68px] w-[210px] max-w-full self-start overflow-hidden object-cover object-center"
              />
            </div>
            {children}
          </div>
        </div>
        <TooltipWidget
          show={isHovered && !hideToolTip}
          parentRef={cardRef.current}
          className="w-full max-w-[300px] rounded border border-primary p-0 shadow-[0px_2px_7px_0px_#0000001F]"
        >
          <div className="relative flex flex-col gap-2">
            <p className="sticky top-0 flex-1 bg-white px-4 pt-4 text-[14px] font-bold">
              {teamName || ''}
            </p>

            <div className="flex-1 whitespace-normal px-4 pb-4 text-[14px] text-subText">
              {teamDescription || 'N/A'}
            </div>
          </div>
        </TooltipWidget>
      </Context.Provider>
    );
  },
);

export const TeamNameAndDescription = () => {
  const { teamName, teamDescription } = useContext(Context);
  return (
    <>
      <div className="mt-3 max-w-[340px] self-start text-[14px] font-bold capitalize text-stone-950">
        {teamName && teamName?.length > 50
          ? `${teamName.slice(0, 50)}...`
          : teamName}
      </div>
      {teamDescription && (
        <div className="mt-2 max-w-[340px] self-start text-[10px] text-subText">
          {teamDescription?.length > 50
            ? `${teamDescription.slice(0, 50)}...`
            : teamDescription}
        </div>
      )}
    </>
  );
};

const SeeMore = ({ action }: { action: () => void }) => {
  return (
    <Button
      onClick={() => action()}
      className="w-full max-w-[140px] border  border-solid border-black 
bg-white text-black hover:border-[color:transparent] hover:bg-primary hover:text-white"
    >
      <p> See more</p>
    </Button>
  );
};

export const SeeMoreTeamButton = () => {
  const navigate = useNavigate();
  const { currentAccountType, setShowModalHandler } = useAppContext();
  const { teamRef } = useContext(Context);

  return (
    <SeeMore
      action={() => {
        navigate(`/${currentAccountType}/teams/${teamRef}`);
        setShowModalHandler('');
      }}
    />
  );
};
export const SeeMoreTeamFromGoalsButton = ({ team }: { team: string }) => {
  const { handleQuery } = useHandleQueryParams();
  const { teamRef } = useContext(Context);
  const { setShowModalHandler } = useAppContext();
  return (
    <SeeMore
      action={() => {
        handleQuery({
          team_ref: teamRef,
          team,
        });
        setShowModalHandler('SeeMoreAboutTeamForGoalsModal');
      }}
    />
  );
};
const SelectTeamButton = ({
  handleSelect,
  handleUnSelect = () => {},
  teamRef,
  isLoading,
  text,
}: {
  handleSelect: () => void;
  handleUnSelect?: () => void;
  teamRef: string;
  isLoading: boolean;
  text: string;
}) => {
  const { projectRef } = useParams();
  const { query } = useHandleQueryParams();
  const { data } = useGetProjectTeams({
    projectRef: projectRef || query.get('project_ref') || '',
  });
  const isSelected = data?.data.map(team => team?.teamRef).includes(teamRef);
  return (
    <Button
      disabled={isLoading}
      onClick={() => {
        if (isSelected) {
          handleUnSelect();
        } else {
          handleSelect();
        }
      }}
      className={`${
        isSelected
          ? 'bg-primary text-white'
          : 'border border-solid  border-black bg-white text-black'
      } w-full max-w-[140px] hover:border-[color:transparent] hover:bg-primary hover:text-white 
      disabled:cursor-not-allowed disabled:bg-transparent disabled:text-primary
    `}
    >
      <p> {isSelected ? 'Unselect' : text}</p>
    </Button>
  );
};
export const SelectMyTeamButton = () => {
  const { query } = useHandleQueryParams();
  const { teamRef } = useContext(Context);
  const { projectRef } = useParams();
  const { mutate: addTeam, isLoading } = useAddTeamToProject({
    ...useHandleApiFeebackWithToast({ queryKey: [GET_PROJECT_TEAMS_QUERY] }),
  });
  const { mutate: removeTeam } = useRemoveTeamFromProject({
    ...useHandleApiFeebackWithToast({ queryKey: [GET_PROJECT_TEAMS_QUERY] }),
  });

  const handleSelect = () => {
    addTeam({
      projectRef: projectRef || query.get('project_ref') || '',
      teamRef: teamRef || '',
    });
  };
  const handleUnSelect = () => {
    removeTeam({
      projectRef: projectRef || query.get('project_ref') || '',
      teamRef: teamRef || '',
    });
  };
  return (
    <SelectTeamButton
      handleSelect={() => handleSelect()}
      handleUnSelect={() => handleUnSelect()}
      teamRef={teamRef || ''}
      isLoading={isLoading}
      text="Select"
    />
  );
};
export const SelectAvailableTeamButton = () => {
  const { query } = useHandleQueryParams();
  const { teamRef } = useContext(Context);
  const { projectRef } = useParams();
  const { mutate: addTeam, isLoading } = useAddExternalTeamToProject({
    ...useHandleApiFeebackWithToast({ queryKey: [GET_PROJECT_TEAMS_QUERY] }),
  });
  const { mutate: removeTeam } = useRemoveTeamFromProject({
    ...useHandleApiFeebackWithToast({ queryKey: [GET_PROJECT_TEAMS_QUERY] }),
  });

  const handleSelect = () => {
    addTeam({
      projectRef: projectRef || query.get('project_ref') || '',
      teamRef: teamRef || '',
    });
  };
  const handleUnSelect = () => {
    removeTeam({
      projectRef: projectRef || query.get('project_ref') || '',
      teamRef: teamRef || '',
    });
  };
  return (
    <SelectTeamButton
      handleSelect={() => handleSelect()}
      handleUnSelect={() => handleUnSelect()}
      teamRef={teamRef || ''}
      isLoading={isLoading}
      text="Invite"
    />
  );
};
export const SelectMyTeamForAvailableProjectButton = () => {
  const { setShowModalHandler } = useAppContext();
  const { handleQuery } = useHandleQueryParams();
  const { teamRef, teamName } = useContext(Context);

  return (
    <div>
      <SelectTeamButton
        handleSelect={() => {
          setShowModalHandler('ShouldProceedToAddTeamToProject');
          handleQuery({ team_ref: teamRef, team_name: teamName });
        }}
        teamRef={teamRef || ''}
        isLoading={false}
        text="Select"
      />
    </div>
  );
};

export const TeamPrincipalLocation = () => {
  const { principalLocation } = useContext(Context);

  return (
    <div className="mt-2 whitespace-normal text-wrap break-words rounded bg-[#EAEAEA] px-2 py-1 text-[10px] font-semibold text-subText">
      Principal location: {principalLocation || 'N/A'}
    </div>
  );
};

export const SelectTeamForTalentButton = () => {
  const { id = '' } = useParams();
  const { teamRef } = useContext(Context);
  const { handleQuery } = useHandleQueryParams();
  const { setShowModalHandler } = useAppContext();

  const useHandleCanAddMemberForTeamCard = () =>
    useHandleCanAddMember({
      teamRef: teamRef!,
      onSuccess: () => {
        handleQuery({
          team_ref: teamRef || '',
          action: isSelected ? 'unselect' : 'select',
        });
        setShowModalHandler('AddTalentToTeam');
      },
    });

  const { handleTrigger, isFetching } = useHandleCanAddMemberForTeamCard();

  const { data: talent } = useGetTalent(id || '');
  const { data: teamMembers } = useGetTeamMembers({
    teamRef: teamRef || '',
  });

  const isSelected = useMemo(() => {
    return (teamMembers?.data || []).find(
      member => member?.userId === talent?.data?.user?.userId,
    );
  }, [teamMembers, talent]);

  const handleSelect = () => {
    if (isSelected) {
      handleQuery({
        team_ref: teamRef || '',
        action: isSelected ? 'unselect' : 'select',
      });
      setShowModalHandler('AddTalentToTeam');
    } else handleTrigger();
  };

  return (
    <>
      <Button
        onClick={handleSelect}
        disabled={isFetching}
        className={cn(
          'w-full max-w-[140px] hover:border-[color:transparent] hover:bg-primary hover:text-white disabled:cursor-not-allowed disabled:bg-transparent disabled:text-primary',
          {
            'bg-primary text-white disabled:bg-primary disabled:text-white':
              isSelected,
            'border border-solid  border-black bg-white text-black':
              !isSelected,
          },
        )}
      >
        {isFetching ? (
          <Spinner />
        ) : (
          <p> {isSelected ? 'Unselect' : 'Select'}</p>
        )}
      </Button>
    </>
  );
};
