import { Link } from 'react-router-dom';

import { ChatIcon2, CheckIcon, PendingIcon } from '../../../assets/icons';
import { profilePlaceholder } from '../../../assets/images';
import { useAppContext } from '../../../context/event/AppEventContext';
import useGetTeamLevelBasedAccess from '../../../hooks/useGetTeamLevelBasedAccess';

type Props = {
  firstName: string;
  lastName: string;
  inviteStatus: string;
};

export function TeamMemberPreviewCard({
  firstName,
  lastName,
  inviteStatus,
}: Props) {
  const { currentAccountType } = useAppContext();
  const { isLoggedInUserThisTeamMember } = useGetTeamLevelBasedAccess();
  return (
    <div className="flex max-w-[407px] items-center justify-between gap-x-10">
      <div className="flex items-stretch justify-between gap-3">
        <img
          loading="lazy"
          src={profilePlaceholder}
          className="aspect-square w-12 max-w-full shrink-0 overflow-hidden rounded-[50%] object-contain object-center"
        />
        <div className="my-auto grow self-center whitespace-nowrap text-base text-neutral-500">
          {`${firstName} ${lastName}`}
        </div>
      </div>
      {
        <>
          {inviteStatus === 'JOINED' ? (
            <div
              className="flex items-center gap-x-2 rounded-[4px] bg-[rgba(86,255,174,0.10)] 
            px-2 py-1 text-[10px] font-semibold"
            >
              <CheckIcon />
              <p className="flex items-center">{inviteStatus}</p>
            </div>
          ) : (
            <div
              className="flex items-center gap-x-2 rounded-[4px] bg-lightYellow 
            px-2 py-1 text-[10px] font-semibold"
            >
              <PendingIcon />
              <p>{inviteStatus}</p>
            </div>
          )}
        </>
      }
      {isLoggedInUserThisTeamMember && (
        <Link to={`/${currentAccountType}/chat`}>
          <ChatIcon2 className="h-5 w-5 cursor-pointer fill-graySixteen hover:fill-primary" />
        </Link>
      )}
    </div>
  );
}
