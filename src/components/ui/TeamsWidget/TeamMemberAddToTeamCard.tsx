import { useNavigate } from 'react-router-dom';

import { profilePlaceholder } from '../../../assets/images';
import { useAppContext } from '../../../context/event/AppEventContext';
import useGetTeamLevelBasedAccess from '../../../hooks/useGetTeamLevelBasedAccess';
import Button from '../ButtonComponent';

type Props = {
  userId: string;
  firstName: string;
  lastName: string;
};

export function TeamMemberAddToTeamCard({
  userId,
  firstName,
  lastName,
}: Props) {
  const { setShowModalHandler } = useAppContext();
  const navigate = useNavigate();
  const { isLoggedInUserATeamLead } = useGetTeamLevelBasedAccess();
  return (
    <div className="flex max-w-[407px] items-center justify-between gap-5">
      <div className="flex items-stretch justify-between gap-3">
        <img
          loading="lazy"
          src={profilePlaceholder}
          className="aspect-square w-12 max-w-full shrink-0 overflow-hidden rounded-[50%] object-contain object-center"
        />
        <div className="my-auto grow self-center whitespace-nowrap text-base text-neutral-500">
          {`${firstName} ${lastName && lastName.slice(0, 1).toUpperCase()}.`}
        </div>
      </div>
      {isLoggedInUserATeamLead && (
        <div>
          <Button
            onClick={() => {
              navigate(`?id=${userId}`);
              setShowModalHandler('AddTeamMemberConfirmRoleModal');
            }}
            className="h-[20px] w-full max-w-[120px] bg-primary text-white disabled:bg-transparent disabled:text-primary"
          >
            <p className="text-[10px]">Select</p>
          </Button>
        </div>
      )}
    </div>
  );
}
