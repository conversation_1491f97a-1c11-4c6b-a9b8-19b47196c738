import { ReactNode } from 'react'
import { <PERSON> } from 'react-router-dom'

import { upivotalLogoWhite } from '../../../assets/icons'
import { mechBgSmall, mechPictureOverlay } from '../../../assets/images'
import mech from '../../../assets/images/mechBgSmall.png'
import useLazyLoadingHandler from '../../../hooks/useLazyLoadingHandler'
import LazyLoadImageContainer from '../CommonWidget/LazyLoadImageContainer'

export default function BrandBanner({ loginCTA }: { loginCTA?: ReactNode }) {
  const { loaded, imgRefs } = useLazyLoadingHandler()

  return (
    <div
      className="max-sm:hidden flex-1 self-stretch max-h-[initial] bg-gradient-to-r from-orange-5 from-[20%] 
    to-orange-15 to-[80%] rounded-[20px]"
    >
      <div
        className="relative max-h-[initial]
     h-full flex justify-center"
      >
        <LazyLoadImageContainer
          loaded={loaded}
          lazyBackgroundImage={mech}
          className="h-full w-full"
        >
          <img
            ref={(element) => (imgRefs.current[0] = element!)}
            loading="lazy"
            src={mechBgSmall}
            alt=""
            className={`w-full h-full sm:object-cover transition ease-in-out duration-300 rounded-[20px] ${
              loaded ? 'opacity-100' : 'opacity-0'
            }`}
          />
        </LazyLoadImageContainer>
        <img
          loading="lazy"
          src={mechPictureOverlay}
          alt=""
          className={`absolute top-[10%] aspect-[1.4] max-h-[90%] max-w-[90%] transition ease-in-out duration-300 ${
            loaded ? 'opacity-100' : 'opacity-0'
          }`}
        />
        <Link to="/">
          <div className="flex items-center aspect-[3.9] max-w-[117px] w-full absolute top-[6%] left-[4%]">
            <img
              loading="lazy"
              src={upivotalLogoWhite}
              className={`w-full h-full`}
            />
          </div>
        </Link>
        {loginCTA && loginCTA}
      </div>
    </div>
  )
}
