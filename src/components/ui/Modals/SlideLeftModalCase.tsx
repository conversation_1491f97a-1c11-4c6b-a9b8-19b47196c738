import { motion } from 'framer-motion'
import { ComponentProps, ReactNode, useRef } from 'react'

import { useAppContext } from '../../../context/event/AppEventContext'
import { useOnClickOutside } from '../../../hooks/useOnClickOutside'

interface Props extends ComponentProps<'div'> {
  children: ReactNode
}
export default function SlideLeftModalCase({ children, className }: Props) {
  const clickRef = useRef<HTMLDivElement>(null)
  const { setShowModalHandler } = useAppContext()
  useOnClickOutside(clickRef, () => setShowModalHandler(''))

  return (
    <motion.div
      exit={{ opacity: 0, x: '100vw' }}
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      transition={{ duration: 0.4 }}
      className={`fixed inset-0 backdrop-filter backdrop-blur-sm backdrop-saturate-50
     z-[9999] flex justify-end`}
    >
      <motion.div
        exit={{ opacity: 0, x: '100vw' }}
        initial={{ opacity: 0, x: '100vw' }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: 0.5, type: 'tween' }}
        className={`${className}`}
        ref={clickRef}
      >
        {children}
      </motion.div>
    </motion.div>
  )
}
