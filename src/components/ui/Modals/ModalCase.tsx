import { motion } from 'framer-motion';
import { ComponentProps, ReactNode } from 'react';

import { CancelIcon } from '../../../assets/icons';
import { useAppContext } from '../../../context/event/AppEventContext';
import { cn } from '@/lib/twMerge/cn';

interface Props extends ComponentProps<'div'> {
  children: ReactNode;
  hideCancelButton?: boolean;
  hidePrimaryBlurEffect?: boolean;
  onClose?: () => void;
  containerClass?: string;
}
export default function ModalCase({
  children,
  className,
  hidePrimaryBlurEffect = false,
  hideCancelButton = false,
  containerClass = '',
  onClose,
}: Props) {
  const { setShowModalHandler } = useAppContext();
  const handleClose = () => {
    // handleQuery({
    //   q: undefined,
    //   viewProject: undefined,
    //   viewTeams: undefined,
    //   current_slide: undefined,
    // });

    if (!onClose) setShowModalHandler('');
    else onClose();
  };

  return (
    <motion.div
      exit={{ opacity: 0, scale: 0 }}
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      transition={{ duration: 0.4 }}
      className={cn(`fixed inset-0 z-[9999] flex items-center
       justify-center rounded-[20px] backdrop-blur-sm backdrop-saturate-50 backdrop-filter ${containerClass}`)}
      onClick={onClose ? onClose : handleClose}
    >
      <motion.div
        initial={{ opacity: 0, y: '-100vh' }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        onClick={e => e.stopPropagation()}
        id="modal-container"
        className={cn(
          `overflow-y-auto overflow-x-hidden overscroll-contain  rounded-[20px] shadow-2xl ${className}`,
        )}
      >
        {hidePrimaryBlurEffect && (
          <div
            className=" primary__gradient absolute right-0 top-0 z-[-1] h-[200px] w-[50px] 
          -translate-x-[50%] rotate-45 sm:h-[200px] sm:w-[100px]"
          />
        )}
        {children}
        {!hideCancelButton && (
          <span
            onClick={() => {
              onClose ? onClose() : handleClose();
            }}
            className="absolute right-3 top-3 cursor-pointer rounded-[50%] border-[0.3px] 
        border-primary bg-slate-100 p-[6px] duration-300 ease-in-out hover:scale-110"
          >
            <CancelIcon className="h-3 w-3 stroke-black sm:h-6 sm:w-6" />
          </span>
        )}
      </motion.div>
    </motion.div>
  );
}
