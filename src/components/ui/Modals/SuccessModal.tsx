import { ReactNode } from 'react';

import { successIcon } from '../../../assets/images';
import ModalCase from './ModalCase';

type Props = {
  children: ReactNode;
};

export default function SuccessModal({ children }: Props) {
  return (
    <ModalCase
      className="mx-auto w-[95%] bg-white px-4 py-8 text-black
     sm:max-w-[623px] sm:px-8 sm:py-32"
    >
      <div className="mt-8">
        <div className="max-w-[571px]">
          <div className="mb-2 flex items-center justify-center">
            <img
              className="max-h-full max-w-full"
              src={successIcon}
              alt="success icon display"
            />
          </div>
          {children}
        </div>
      </div>
    </ModalCase>
  );
}
