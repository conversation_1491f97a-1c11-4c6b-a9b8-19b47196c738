import { ReactNode } from 'react';

import { alertCircle } from '../../../assets/images';
import ModalCase from './ModalCase';
import { cn } from '@/lib/twMerge/cn';
type Props = {
  children: ReactNode;
  className?: string;
  onCloseModalActionHandler?: () => void;
};
export default function TakeActionModal({
  children,
  className,
  onCloseModalActionHandler,
}: Props) {
  return (
    <ModalCase
      onClose={onCloseModalActionHandler}
      className={cn(
        'mx-auto w-[95%]  bg-white px-4 py-8 text-black sm:max-w-[623px] sm:px-8 sm:py-32',
        className,
      )}
    >
      <div className="mt-8">
        <div className="max-w-[571px]">
          <div className="flex items-center justify-center">
            <img
              className="h-[80px] w-[80px]"
              src={alertCircle}
              alt="delete icon display"
            />
          </div>
          {children}
        </div>
      </div>
    </ModalCase>
  );
}
