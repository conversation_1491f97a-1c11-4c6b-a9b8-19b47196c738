import { useAppContext } from '@/context/event/AppEventContext';
import { AnimatePresence } from 'framer-motion';
import ModalCase from './ModalCase';
import Button from '../ButtonComponent';
import { useNavigate } from 'react-router-dom';

import moment from 'moment';
import { Helper } from '@/utils/helpers';

import useManageActiveSubscription from '@/hooks/useManageActiveSubscription';

export default function InactiveSubscriptionModal() {
  const { showModal, setShowModalHandler, currentAccountType } =
    useAppContext();
  const navigate = useNavigate();
  const { subscription, totalSubsriptionDue, subscriptionType } =
    useManageActiveSubscription();
  return (
    <AnimatePresence>
      {showModal === 'InactiveSubscriptionModal' && (
        <ModalCase
          className="relative mx-auto max-h-[80%] w-[95%] overflow-y-auto  bg-white
      px-4 py-8 text-black sm:max-w-[624px] sm:px-8 sm:py-20"
        >
          <div className="mt-8">
            <p className="mx-auto mb-4 w-full max-w-[450px] text-center text-[16px] text-subText">
              Subscription fee of{' '}
              <span className="text-primary">{totalSubsriptionDue}</span> was
              due on{' '}
              <span className="text-primary">
                {subscription?.updatedAt
                  ? moment(subscription?.updatedAt).format('MMMM D, YYYY')
                  : null}
              </span>{' '}
              for your{' '}
              <span className="text-primary">
                {Helper.capitalizeString(subscriptionType)} Package
              </span>
              . Please update your payment information
            </p>
            <div className="mt-10 flex w-full flex-wrap justify-center gap-9">
              <Button
                type="button"
                className="group h-[36px] w-full max-w-[147px] whitespace-nowrap !rounded-none border-[0.73px] border-grayNine bg-transparent hover:border-primary hover:bg-primary"
                onClick={() => {
                  setShowModalHandler('');
                }}
              >
                <p className="text-[12.6px] leading-[24px] text-grayNine group-hover:text-white">
                  Cancel
                </p>
              </Button>
              <Button
                type="button"
                onClick={() => {
                  navigate(
                    `/${currentAccountType}/subscriptions?c_page=subscription&returnUrl=/${currentAccountType}/dashboard-overview`,
                  );
                  setShowModalHandler('');
                }}
                className="h-[36px] w-full max-w-[200px] whitespace-nowrap !rounded-none border-[0.73px] border-primary bg-primary hover:border-black hover:bg-black disabled:cursor-not-allowed disabled:border-disabled disabled:bg-disabled disabled:text-opacity-70"
              >
                <p className="text-[12.6px] leading-[24px] text-white">
                  Proceed To Payment
                </p>
              </Button>
            </div>
          </div>
        </ModalCase>
      )}
    </AnimatePresence>
  );
}
