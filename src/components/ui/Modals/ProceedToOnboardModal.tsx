import { useAppContext } from '@/context/event/AppEventContext';
import { AnimatePresence } from 'framer-motion';
import ModalCase from './ModalCase';
import Button from '../ButtonComponent';
import { useNavigate } from 'react-router-dom';

export default function ProceedToOnboardModal() {
  const { showModal, setShowModalHandler } = useAppContext();
  const navigate = useNavigate();
  return (
    <AnimatePresence>
      {showModal === 'ProceedToOnboardModal' && (
        <ModalCase
          className="relative mx-auto max-h-[80%] w-[95%] overflow-y-auto  bg-white
      px-4 py-8 text-black sm:max-w-[624px] sm:px-8 sm:py-20"
        >
          <div className="mt-8">
            <p className="mx-auto mb-4 w-full max-w-[450px] text-center text-[16px] text-subText">
              Onboard to access additional features.
            </p>
            <div className="mt-10 flex w-full flex-wrap justify-center gap-x-9 gap-y-3">
              <Button
                type="button"
                onClick={() => {
                  navigate(
                    `/user-onboarding?redirectUri=${encodeURIComponent(window.location.href)}`,
                  );
                  setShowModalHandler('');
                }}
                className="h-[36px] w-full max-w-[200px] whitespace-nowrap !rounded-none border-[0.73px] border-primary bg-primary hover:border-black hover:bg-black disabled:cursor-not-allowed disabled:border-disabled disabled:bg-disabled disabled:text-opacity-70"
              >
                <p className="text-[12.6px] leading-[24px] text-white">
                  Onboard To Proceed
                </p>
              </Button>
            </div>
          </div>
        </ModalCase>
      )}
    </AnimatePresence>
  );
}
