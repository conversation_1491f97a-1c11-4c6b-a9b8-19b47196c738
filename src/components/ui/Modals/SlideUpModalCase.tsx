import { motion } from 'framer-motion';
import { ComponentProps, ReactNode, useRef } from 'react';

import { useAppContext } from '../../../context/event/AppEventContext';
import { useOnClickOutside } from '../../../hooks/useOnClickOutside';

interface Props extends ComponentProps<'div'> {
  children: ReactNode;
}
export default function SlideUpModalCase({ children, className }: Props) {
  const clickRef = useRef<HTMLDivElement>(null);
  const { setShowModalHandler } = useAppContext();
  useOnClickOutside(clickRef, () => setShowModalHandler(''));

  return (
    <motion.div
      exit={{ opacity: 0, y: '50px' }}
      initial={{ opacity: 0, y: '50px' }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.5, type: 'tween' }}
      className={`${className}`}
      ref={clickRef}
    >
      {children}
    </motion.div>
  );
}
