import { ComponentProps, ReactNode } from 'react';

import { cn } from '../../../lib/twMerge/cn';
import Button from '../ButtonComponent';
import { Spinner } from '../CommonWidget/Loader';

type ButtonProps = ComponentProps<typeof Button>;

interface Props extends ButtonProps {
  isDirty?: boolean;
  isValid?: boolean;
  isLoading: boolean;

  children: ReactNode;
}

export default function SharedFormButton({
  isDirty,
  isValid,
  isLoading,
  className,
  children,
  ...rest
}: Props) {
  return (
    <Button
      type="submit"
      disabled={!isDirty || !isValid || isLoading}
      className={cn(`group mt-[32px] 
        w-full max-w-[400px]   
        border border-primary bg-primary text-white disabled:cursor-not-allowed disabled:bg-transparent disabled:text-primary ${className}`)}
      {...rest}
    >
      {isLoading ? <Spinner className="" /> : children}
    </Button>
  );
}
