import React from 'react'
import { Link, NavLink } from 'react-router-dom'

let hashFragment: string = ''
let observer: MutationObserver | null = null
let asyncTimerId: number | null = null
let scrollFunction: ((element: HTMLElement) => void) | null = null

function reset(): void {
  hashFragment = ''
  if (observer !== null) observer.disconnect()
  if (asyncTimerId !== null) {
    window.clearTimeout(asyncTimerId)
    asyncTimerId = null
  }
}

function isInteractiveElement(element: HTMLElement): boolean {
  const formTags: string[] = ['BUTTON', 'INPUT', 'SELECT', 'TEXTAREA']
  const linkTags: string[] = ['A', 'AREA']
  return (
    (formTags.includes(element.tagName) && !element.hasAttribute('disabled')) ||
    (linkTags.includes(element.tagName) && element.hasAttribute('href'))
  )
}

function getElementAndScroll(): boolean {
  let element: HTMLElement | null = null
  if (hashFragment === '#') {
    // use document.body instead of document.documentElement because of a bug in smoothscroll-polyfill in safari
    // see https://github.com/iamdustan/smoothscroll/issues/138
    // while smoothscroll-polyfill is not included, it is the recommended way to implement smoothscroll
    // in browsers that don't natively support el.scrollIntoView({ behavior: 'smooth' })
    element = document.body
  } else {
    // check for element with matching id before assuming '#top' is the top of the document
    // see https://html.spec.whatwg.org/multipage/browsing-the-web.html#target-element
    const id: string = hashFragment.replace('#', '')
    element = document.getElementById(id)
    if (element === null && hashFragment === '#top') {
      // see above comment for why document.body instead of document.documentElement
      element = document.body
    }
  }

  if (element !== null) {
    scrollFunction!(element)

    // update focus to where the page is scrolled to
    // unfortunately, this doesn't work in safari (desktop and iOS) when blur() is called
    const originalTabIndex: string | null = element.getAttribute('tabindex')
    if (originalTabIndex === null && !isInteractiveElement(element)) {
      element.setAttribute('tabindex', '-1')
    }
    element.focus({ preventScroll: true })
    if (originalTabIndex === null && !isInteractiveElement(element)) {
      // for some reason calling blur() in safari resets the focus region to where it was previously,
      // if blur() is not called it works in safari, but then are stuck with default focus styles
      // on an element that otherwise might never have had focus styles applied, so not an option
      element.blur()
      element.removeAttribute('tabindex')
    }

    reset()
    return true
  }
  return false
}

function hashLinkScroll(timeout: number | undefined): void {
  // Push onto the callback queue so it runs after the DOM is updated
  window.setTimeout(() => {
    if (getElementAndScroll() === false) {
      if (observer === null) {
        observer = new MutationObserver(getElementAndScroll)
      }
      observer.observe(document, {
        attributes: true,
        childList: true,
        subtree: true,
      })
      // if the element doesn't show up in the specified timeout or 10 seconds, stop checking
      asyncTimerId = window.setTimeout(() => {
        reset()
      }, timeout || 10_000)
    }
  }, 0)
}

/* eslint-disable react/display-name */
export function genericHashLink(As: typeof Link | typeof NavLink) {
  /* eslint-disable react/display-name */
  return React.forwardRef<HTMLAnchorElement | HTMLSpanElement, any>(
    (props, ref) => {
      let linkHash: string = ''
      if (typeof props.to === 'string' && props.to.includes('#')) {
        linkHash = `#${props.to.split('#').slice(1).join('#')}`
      } else if (
        typeof props.to === 'object' &&
        typeof props.to.hash === 'string'
      ) {
        linkHash = props.to.hash
      }

      const passDownProps: any = {}
      if (As === NavLink) {
        passDownProps.isActive = (match: any, location: any) =>
          match && match.isExact && location.hash === linkHash
      }

      function handleClick(
        e: React.MouseEvent<HTMLAnchorElement | HTMLSpanElement>
      ): void {
        reset()
        hashFragment = props.elementId ? `#${props.elementId}` : linkHash
        if (props.onClick) props.onClick(e)
        if (
          hashFragment !== '' &&
          // ignore non-vanilla click events, same as react-router
          // below logic adapted from react-router: https://github.com/ReactTraining/react-router/blob/fc91700e08df8147bd2bb1be19a299cbb14dbcaa/packages/react-router-dom/modules/Link.js#L43-L48
          !e.defaultPrevented && // onClick prevented default
          e.button === 0 && // ignore everything but left clicks
          (!props.target || props.target === '_self') && // let the browser handle "target=_blank" etc
          !(e.metaKey || e.altKey || e.ctrlKey || e.shiftKey) // ignore clicks with modifier keys
        ) {
          scrollFunction =
            props.scroll ||
            ((element) =>
              props.smooth
                ? element.scrollIntoView({ behavior: 'smooth' })
                : element.scrollIntoView())
          hashLinkScroll(props.timeout)
        }
      }
      /* eslint-disable @typescript-eslint/no-unused-vars */
      const { scroll, smooth, timeout, elementId, ...filteredProps } = props
      return (
        <As
          {...passDownProps}
          {...filteredProps}
          onClick={handleClick}
          ref={ref}
        >
          {props.children}
        </As>
      )
    }
  )
}

export const HashLink = genericHashLink(Link)

export const NavHashLink = genericHashLink(NavLink)
