import { ComponentProps, ReactNode } from 'react';
import { Controller, FieldErrors } from 'react-hook-form';

import { FormDataType, OverrideProp as OverrideProperty } from '../../types';
import FormLabel from './FormLabel';
import FormSelect from './FormSelect';

type SelectProps<T> = OverrideProperty<
  ComponentProps<typeof FormSelect>,
  { name: keyof T }
>;
interface Props<T extends FormDataType> extends SelectProps<T> {
  errors: FieldErrors<T>;
  name: keyof T;
  control: any;
  labelName?: string;
  optionsArr: any[];
  formLabel?: ReactNode;
}
export default function FormSelectBox<T extends FormDataType>({
  name,
  errors,
  control,
  labelName,
  optionsArr,
  formLabel: AltFormLabel,
  ...rest
}: Props<T>) {
  return (
    <>
      {AltFormLabel ? (
        AltFormLabel
      ) : (
        <FormLabel
          name={name as string}
          labelName={labelName || ''}
          className={`after:text-[16px] after:text-primary after:content-["*"] `}
        />
      )}
      <Controller
        name={name as string}
        control={control}
        render={({ field: { onChange, onBlur, value } }) => (
          <FormSelect
            id={name as string}
            value={value ? optionsArr.find(x => x.value === value) : value}
            onChange={(option: any) => {
              Array.isArray(option)
                ? onChange(option.map(({ value }) => value))
                : onChange(option.value);
            }}
            onBlur={onBlur}
            name={name as string}
            {...rest}
          />
        )}
      />
      {errors[name as keyof FieldErrors<T>] && (
        <p className="mt-2 text-[12px] text-red-800 sm:text-[14px]">
          {errors[name as keyof FieldErrors<T>]?.message as ReactNode}
        </p>
      )}
    </>
  );
}
