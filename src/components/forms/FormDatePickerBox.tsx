import { ComponentProps, ReactNode } from 'react';
import { Controller, FieldErrors } from 'react-hook-form';

import { FormDataType, OverrideProp as OverrideProperty } from '../../types';
import FormDatePicker from './FormDatePicker';
import FormLabel from './FormLabel';

type SelectProps<T> = OverrideProperty<
  ComponentProps<typeof FormDatePicker>,
  { name: keyof T }
>;
interface Props<T extends FormDataType> extends SelectProps<T> {
  errors: FieldErrors<T>;
  name: keyof T;
  control: any;
  labelName?: string;
  formLabel?: ReactNode;
}
export default function FormDatePickerBox<T extends FormDataType>({
  name,
  errors,
  control,
  labelName,
  formLabel: AltFormLabel,
  ...rest
}: Omit<Props<T>, 'render'>) {
  return (
    <>
      {AltFormLabel ? (
        AltFormLabel
      ) : (
        <FormLabel
          name={name as string}
          labelName={labelName || ''}
          className={`after:text-[16px] after:text-primary after:content-["*"] `}
        />
      )}
      <Controller
        name={name as string}
        control={control}
        render={({ field: { onChange, onBlur, value } }) => (
          <FormDatePicker
            selected={value}
            onChange={value => {
              onChange(value);
            }}
            onBlur={onBlur}
            {...rest}
          />
        )}
      />
      {errors[name as keyof FieldErrors<T>] && (
        <p className="mt-2 text-[12px] text-red-800 sm:text-[14px]">
          {errors[name as keyof FieldErrors<T>]?.message as ReactNode}
        </p>
      )}
    </>
  );
}
