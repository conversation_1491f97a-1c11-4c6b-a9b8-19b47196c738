import { ComponentProps, ReactNode, useEffect, useRef } from 'react';
import { Controller, FieldErrors } from 'react-hook-form';
import PhoneInput from 'react-phone-input-2';

import { useCustomMedia } from '../../hooks/useCustomMedia';
import {
  containerStyle,
  dropdownStyle,
  inputStyle,
  smallMediaContainerStyle,
} from '../../lib/phoneInput2/react-tel-input-2';
import { FormDataType, OverrideProp as OverrideProperty } from '../../types';
import FormLabel from './FormLabel';
import { useHandleQueryParams } from '@/hooks/useHandleQueryParams';

type ControllerProps<T> = OverrideProperty<
  ComponentProps<typeof Controller>,
  { name: keyof T }
>;
interface Props<T extends FormDataType> extends ControllerProps<T> {
  errors: FieldErrors<T>;
  name: keyof T;
  control: any;
  formLabel?: ReactNode;
  labelName?: string;
  placeholder?: string;
}

export default function PhoneInputBox<T extends FormDataType>({
  name,
  errors,
  control,
  labelName,
  formLabel: AltFormLabel,
  placeholder,
  ...rest
}: Omit<Props<T>, 'render'>) {
  const phoneRef = useRef<HTMLInputElement>(null);
  const { screenSize } = useCustomMedia();
  const { query } = useHandleQueryParams();
  useEffect(() => {
    if (query.get('edit_phone_no')) phoneRef?.current?.focus();
  }, []);
  return (
    <>
      {AltFormLabel ? (
        AltFormLabel
      ) : (
        <FormLabel
          name={name as string}
          labelName={labelName || ''}
          className={`after:text-[16px] after:text-primary after:content-["*"] `}
        />
      )}
      <Controller
        name={name as string}
        control={control}
        render={({ field: { onChange, onBlur, value } }) => (
          <PhoneInput
            inputProps={{ ref: phoneRef }}
            placeholder={placeholder || 'Phone Number +****************'}
            specialLabel={''}
            country={'us'}
            value={value}
            onChange={onChange}
            onBlur={onBlur}
            containerStyle={
              screenSize > 768 ? containerStyle : smallMediaContainerStyle
            }
            inputStyle={inputStyle}
            dropdownStyle={dropdownStyle}
          />
        )}
        {...rest}
      />
      {errors[name as keyof FieldErrors<T>] && (
        <p className="mt-2 text-[12px] text-red-800 sm:text-[14px]">
          {errors[name as keyof FieldErrors<T>]?.message as ReactNode}
        </p>
      )}
    </>
  );
}
