import 'react-datepicker/dist/react-datepicker.css';

import { ComponentProps } from 'react';
import DatePicker from 'react-datepicker';

type SelectProps = Omit<ComponentProps<typeof DatePicker>, ''>;

interface Props extends SelectProps {
  selectsRange?: never;
  selectsMultiple?: never;
  showMonthYearDropdown?: never;
  onChange?: (
    date: Date | null,
    event?: React.MouseEvent<HTMLElement> | React.KeyboardEvent<HTMLElement>,
  ) => void;
}

export default function FormDatePicker({ ...rest }: Props) {
  return (
    <DatePicker
      showMonthDropdown
      showYearDropdown
      dropdownMode="select"
      {...rest}
    />
  );
}
