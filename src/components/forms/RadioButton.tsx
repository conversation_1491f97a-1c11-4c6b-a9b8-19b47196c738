import { ComponentProps } from 'react';
import { UseFormRegisterReturn } from 'react-hook-form';

import { OverrideProp as OverrideProperty } from '../../types';

type InputProps = OverrideProperty<
  ComponentProps<'input'>,
  {
    name: string;
  }
>;
interface Props extends InputProps {
  registerHanlder: () => UseFormRegisterReturn<string>;
}

export default function RadioButton({
  registerHanlder,
  ...rest
}: Omit<Props, 'name' | 'type'>) {
  return (
    <>
      <input
        className="h-4 w-4 border-2 border-black text-primary checked:border-primary checked:bg-primary focus:ring-2 focus:ring-primary"
        type="radio"
        {...rest}
        {...registerHanlder()}
      />
    </>
  );
}
