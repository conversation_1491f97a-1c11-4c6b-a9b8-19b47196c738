import { ComponentProps, MutableRefObject, useRef } from 'react';
import { UseFormRegisterReturn } from 'react-hook-form';

import { OverrideProp as OverrideProperty } from '../../types';
import { forwardRef } from 'react';

type InputProps = OverrideProperty<
  ComponentProps<'textarea'>,
  {
    name: string;
  }
>;
interface Props extends InputProps {
  registerHanlder: () => UseFormRegisterReturn<string>;
}

export const FormTextArea = forwardRef<
  HTMLTextAreaElement,
  Omit<Props, 'name'>
>(function FormTextArea(
  { registerHanlder, ...rest }: Omit<Props, 'name'>,
  ref,
) {
  const localRef = useRef<HTMLTextAreaElement>(null);
  const insideRef = ref || localRef;
  const { ref: formHandlerRef, ...formRest } = registerHanlder();
  return (
    <>
      <textarea
        {...rest}
        {...formRest}
        ref={e => {
          formHandlerRef(e);
          (insideRef as MutableRefObject<HTMLTextAreaElement>).current = e!;
        }}
      ></textarea>
    </>
  );
});

export default FormTextArea;
