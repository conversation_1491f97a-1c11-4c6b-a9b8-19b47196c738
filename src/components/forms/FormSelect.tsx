import { ComponentProps } from 'react';
import Select from 'react-select';

import { useCustomMedia } from '../../hooks/useCustomMedia';
import {
  selectStyles,
  smallMediaSelectStyles,
} from '../../lib/reactSelect/react-select-style';
import { cn } from '../../lib/twMerge/cn';
import { OverrideProp as OverrideProperty } from '../../types';

type SelectProps = OverrideProperty<
  ComponentProps<typeof Select>,
  { name: string; containerClassName?: string }
>;
interface Props extends SelectProps {}
export default function FormSelect({
  containerClassName = '',
  ...rest
}: Props) {
  const { screenSize } = useCustomMedia();
  return (
    <div className={cn(`mt-2 ${containerClassName}`)}>
      <Select
        styles={screenSize > 768 ? selectStyles : smallMediaSelectStyles}
        isSearchable
        menuPortalTarget={document.body}
        menuPosition={'absolute'}
        menuPlacement={'auto'}
        menuShouldScrollIntoView={false}
        {...rest}
      />
    </div>
  );
}
