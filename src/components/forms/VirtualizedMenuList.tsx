import { memo, useEffect, useRef } from 'react';
import { GroupBase, MenuListProps } from 'react-select';
import { useVirtualizer } from '@tanstack/react-virtual';

export const VirtualizedMenuList = memo(
  (props: MenuListProps<unknown, boolean, GroupBase<unknown>>) => {
    const { options, getValue, children } = props;

    const typedOptions = options as Array<{
      value: string | number;
      label: string;
    }>;
    const [value] = getValue() as Array<{
      value: string | number;
      label: string;
    }>;

    const selectedIndex = typedOptions.findIndex(
      option => option.value === value?.value,
    );

    const parentRef = useRef<HTMLDivElement>(null);

    const virtualizer = useVirtualizer({
      getScrollElement: () => parentRef.current,
      count: (children as React.ReactNode[]).length,
      overscan: 5,
      estimateSize: () => 35,
    });

    useEffect(() => {
      if (selectedIndex >= 0) {
        virtualizer.scrollToIndex(selectedIndex + 1);
      }
    }, [selectedIndex, virtualizer]);

    const items = virtualizer.getVirtualItems();

    return (
      <div ref={parentRef} style={{ maxHeight: '300px', overflow: 'auto' }}>
        <div
          className="scroller relative w-full"
          style={{
            height: virtualizer.getTotalSize(),
          }}
        >
          {items.map(virtualRow => (
            <div
              key={virtualRow.key}
              data-index={virtualRow.index}
              ref={virtualizer.measureElement}
              className="absolute left-0 top-0 w-full"
              style={{
                transform: `translateY(${virtualRow.start}px)`,
              }}
            >
              {(children as React.ReactNode[])[virtualRow.index]}
            </div>
          ))}
        </div>
      </div>
    );
  },
);

VirtualizedMenuList.displayName = 'VirtualizedMenuList';
