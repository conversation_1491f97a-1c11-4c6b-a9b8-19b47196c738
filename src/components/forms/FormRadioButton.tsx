import { ComponentProps, ReactNode } from 'react'
import { FieldErrors, UseFormRegisterReturn } from 'react-hook-form'

import { FormDataType } from '../../types'
import RadioButton from './RadioButton'

type InputProps = ComponentProps<typeof RadioButton>

interface Props<T extends FormDataType> extends InputProps {
  registerHanlder: () => UseFormRegisterReturn<string>
  errors: FieldErrors<T>
  name: keyof T
  labelName: any
}

export default function FormRadioButton<T extends FormDataType>({
  registerHanlder,
  errors,
  name,
  labelName,
  ...rest
}: Props<T>) {
  return (
    <>
      <label className="flex items-center gap-2 cursor-pointer">
        <RadioButton
          id={name as string}
          registerHanlder={() => registerHanlder()}
          {...rest}
        />
        {labelName}
      </label>
      {errors[name as keyof FieldErrors<T>] && (
        <p className="text-red-800 mt-2 text-[14px]">
          {errors[name as keyof FieldErrors<T>]?.message as ReactNode}
        </p>
      )}
    </>
  )
}
