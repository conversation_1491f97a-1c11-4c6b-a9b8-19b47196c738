import { ComponentP<PERSON>, ForwardedRef, forwardRef, ReactNode } from 'react';
import { FieldErrors, UseFormRegisterReturn } from 'react-hook-form';

import { FormDataType } from '../../types';
import { FormInput } from './FormInput';
import FormLabel from './FormLabel';

type InputProps = ComponentProps<typeof FormInput>;

interface Props<T extends FormDataType> extends InputProps {
  registerHanlder: () => UseFormRegisterReturn<string>;
  errors: FieldErrors<T>;
  name: string;
  labelName?: string;
  formLabel?: ReactNode;
  maxChar?: number;
}

function FormInputBox<T extends FormDataType>(
  {
    registerHanlder,
    errors,
    name,
    labelName,
    formLabel: AltFormLabel,
    maxChar,
    ...rest
  }: Props<T>,
  ref: ForwardedRef<HTMLInputElement>,
) {
  const chars = maxChar
    ? maxChar - ((rest.value as string)?.length || 0)
    : null;
  return (
    <>
      {AltFormLabel ? (
        AltFormLabel
      ) : (
        <FormLabel
          name={name as string}
          labelName={labelName || ''}
          className={`after:text-[16px] after:text-primary after:content-["*"] `}
        />
      )}
      <FormInput
        id={name as string}
        registerHanlder={() => registerHanlder()}
        ref={ref}
        {...rest}
      />
      <div className="flex justify-between">
        {errors[name as keyof FieldErrors<T>] && (
          <p className="mt-2 text-[12px] text-red-800 sm:text-[14px]">
            {errors[name as keyof FieldErrors<T>]?.message as ReactNode}
          </p>
        )}
        {maxChar && (
          <small
            className={`${chars && chars >= 0 ? 'text-[#8083A3]' : 'text-red-800'} ml-auto mt-[2px] text-[10px] font-semibold`}
          >
            Characters remaining: {chars}
          </small>
        )}
      </div>
    </>
  );
}

const FormInputBoxWithForwardRef = forwardRef(FormInputBox) as <
  T extends FormDataType,
>(
  props: Props<T> & { ref?: React.Ref<HTMLInputElement> },
) => JSX.Element;
export default FormInputBoxWithForwardRef;
