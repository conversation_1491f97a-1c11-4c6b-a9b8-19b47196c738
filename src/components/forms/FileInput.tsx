import { ComponentProps, ReactNode } from 'react'
import { Controller, FieldErrors } from 'react-hook-form'

import { UploadIcon } from '../../assets/icons'
import { FormDataType } from '../../types'

interface Props<T extends FormDataType> extends ComponentProps<'input'> {
  errors: FieldErrors<T>
  name: string
  control: any
  label: any
}

export default function FileInput<T extends FormDataType>({
  name,
  errors,
  control,
  label = 'Uplaod a File',
  ...rest
}: Props<T>) {
  return (
    <>
      <Controller
        control={control}
        name={name}
        render={({ field: { onChange, onBlur, name, ref, value } }) => (
          <>
            <div className="bg-white border border-dashed border-grayThirteen mt-1 flex justify-between p-3 rounded-md">
              <input
                id={name}
                type="file"
                name={name}
                ref={ref}
                onBlur={onBlur}
                onChange={(e) => {
                  onChange(e.target.files)
                }} // React Hook Form expects the file list, not the event
                className="sr-only"
                {...rest}
              />
              <div className="flex items-center gap-2 text-left">
                <UploadIcon width={24} height={24} />
                <div>
                  <p className="mt-1 text-sm text-gray-600 font-medium">
                    {label}
                  </p>
                  <p className="mt-1 text-xs text-gray-500">
                    SVG, PNG, JPG, GIF | 10MB max.
                  </p>
                </div>
              </div>
              <div className="flex items-center">
                <button
                  type="button"
                  className="font-medium text-white bg-grayThirteen py-2.5 px-4 rounded-sm text-[14px] leading-none"
                  onClick={() => document.getElementById(name)?.click()}
                >
                  Upload
                </button>
              </div>
            </div>
            {value?.[0]?.name && <p>{value?.[0]?.name}</p>}
          </>
        )}
      />
      {errors[name as keyof FieldErrors<T>] && (
        <p className="text-red-800 mt-2 text-[14px]">
          {errors[name as keyof FieldErrors<T>]?.message as ReactNode}
        </p>
      )}
    </>
  )
}
