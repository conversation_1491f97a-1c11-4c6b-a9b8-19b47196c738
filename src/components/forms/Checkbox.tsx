import { ComponentProps } from 'react';
import { UseFormRegisterReturn } from 'react-hook-form';

import { OverrideProp as OverrideProperty } from '../../types';

type InputProps = OverrideProperty<
  ComponentProps<'input'>,
  {
    name: string;
  }
>;
interface Props extends InputProps {
  registerHanlder: () => UseFormRegisterReturn<string>;
}

export default function Checkbox({
  registerHanlder,
  ...rest
}: Omit<Props, 'name' | 'type'>) {
  return (
    <>
      <input type="checkbox" {...rest} {...registerHanlder()} />
    </>
  );
}
