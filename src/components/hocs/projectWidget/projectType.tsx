import { AllProjectByTypeResourceLoader } from '@/features/Categories';
import AllAvailableProjectResourceLoader from '@/features/Categories/components/resourceLoaders/AllAvailableProjectResourceLoader';
import {
  useGetAvailableProjects,
  useGetExternalProjects,
  useGetProjects,
} from '@/features/Categories/hooks/apiQueryHooks/eduQueryHooks';
import AllProjectsLayout from '@/features/Categories/layout/AllProjectsLayout';

export const projectType =
  (project: string | null) => (Component: React.FC) => {
    switch (project) {
      case 'my_internal_projects':
        return (props: any) => (
          <AllProjectsLayout>
            <AllProjectByTypeResourceLoader
              {...props}
              useGetAllProjectsByType={useGetProjects}
            />
          </AllProjectsLayout>
        );
      case 'available_projects':
        return (props: any) => (
          <AllProjectsLayout>
            <AllAvailableProjectResourceLoader
              {...props}
              useGetAllProjectsByType={useGetAvailableProjects}
            />
          </AllProjectsLayout>
        );
      case 'my_external_projects':
        return (props: any) => (
          <AllProjectsLayout>
            <AllProjectByTypeResourceLoader
              {...props}
              useGetAllProjectsByType={useGetExternalProjects}
            />
          </AllProjectsLayout>
        );
      default:
        return (props: any) => <Component {...props} />;
    }
  };
