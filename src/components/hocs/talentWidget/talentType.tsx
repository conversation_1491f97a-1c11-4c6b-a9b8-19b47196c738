import AllTalentsByTypeResourceLoader from '@/features/Talent/components/resourceLoaders/AllTalentsByTypeResourceLoader';
import { useGetTalents } from '@/features/Talent/hooks/apiQueryhooks/talentQueryHook';
import AllTalentsLayout from '@/features/Talent/layout/AllTalentsLayout';

export const talentType = (talent: string | null) => (Component: React.FC) => {
  switch (talent) {
    case 'all_talents':
      return (props: any) => (
        <AllTalentsLayout>
          <AllTalentsByTypeResourceLoader
            {...props}
            useGetAllTalentsByType={useGetTalents}
          />
        </AllTalentsLayout>
      );

    default:
      return (props: any) => <Component {...props} />;
  }
};
