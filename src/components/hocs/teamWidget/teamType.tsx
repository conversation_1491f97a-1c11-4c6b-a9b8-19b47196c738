import AllTeamsByTypeResourceLoader from '@/features/Teams/components/resourceLoaders/AllTeamsByTypeResourceLoader';
import {
  useGetAvailableTeams,
  useGetMyTeams,
} from '../../../features/Teams/hooks/apiQueryhooks/eduQueryHooks';
import AllTeamsLayout from '@/features/Teams/layout/AllTeamsLayout';
import AllAvailableTeamResourceLoader from '@/features/Teams/components/resourceLoaders/AllAvailableTeamResourceLoader';

export const teamType = (team: string | null) => (Component: React.FC) => {
  switch (team) {
    case 'my_teams':
      return (props: any) => (
        <AllTeamsLayout>
          <AllTeamsByTypeResourceLoader
            {...props}
            useGetAllTeamsByType={useGetMyTeams}
          />
        </AllTeamsLayout>
      );
    case 'available_teams':
      return (props: any) => (
        <AllTeamsLayout>
          <AllAvailableTeamResourceLoader
            {...props}
            useGetAllTeamsByType={useGetAvailableTeams}
          />
        </AllTeamsLayout>
      );
    default:
      return (props: any) => <Component {...props} />;
  }
};
