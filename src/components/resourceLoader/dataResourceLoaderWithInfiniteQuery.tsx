import { InfiniteData, UseInfiniteQueryResult } from '@tanstack/react-query';
import React, { ReactNode } from 'react';
import { useInView } from 'react-intersection-observer';
import { Spinner } from '../ui/CommonWidget/Loader';
import useFetchNextPage from '@/hooks/useFetchNextPage';

export const DataResourceLoaderWithInfiniteQuery = <
  T extends Record<string, any>,
>({
  useApiInfiniteQueryHook,
  render: Render,
  loader = (
    <div className="flex h-[300px] w-full items-center justify-center">
      <Spinner className="" />
    </div>
  ),
}: {
  useApiInfiniteQueryHook: (
    parameter?: {},
    options?: {},
  ) => UseInfiniteQueryResult<T, any>;
  render: React.ForwardRefExoticComponent<
    {
      resource: InfiniteData<T>['pages'];
    } & React.RefAttributes<HTMLDivElement>
  >;
  loader?: ReactNode;
}) => {
  const {
    data: resource,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    isError,
    refetch,
  } = useApiInfiniteQueryHook();
  const { ref, inView } = useInView();
  useFetchNextPage({ fetchNextPage, hasNextPage: !!hasNextPage, inView });
  if (isLoading) return <>{loader}</>;
  if (isError)
    return (
      <p className="h-[50px] cursor-pointer px-6 py-6 text-center text-[12px]">
        Something went wrong. <span onClick={() => refetch()}>refetch</span>
      </p>
    );
  if (resource)
    return (
      <section className="flex">
        <div className="w-full">
          <Render resource={resource.pages} ref={ref} />
        </div>
        {isFetchingNextPage && (
          <div className="relative flex items-center justify-center">
            <div className="absolute right-[30px] top-[40%] z-[5] w-full max-w-[100px] -translate-y-[50%]">
              <Spinner />
            </div>
          </div>
        )}
      </section>
    );
};
export const DataResourceLoaderWithInfiniteQueryNew = <
  T extends Record<string, any>,
>({
  useApiInfiniteQueryHook,
  render: Render,
  loader = (
    <div className="flex h-[300px] w-full items-center justify-center">
      <Spinner className="" />
    </div>
  ),
}: {
  useApiInfiniteQueryHook: (
    parameter?: {},
    options?: {},
  ) => UseInfiniteQueryResult<T, any>;
  render: React.ForwardRefExoticComponent<
    {
      resource: InfiniteData<T>['pages'];
      isFetchingNextPage: boolean;
    } & React.RefAttributes<HTMLDivElement>
  >;
  loader?: ReactNode;
}) => {
  const {
    data: resource,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    isError,
    refetch,
  } = useApiInfiniteQueryHook();
  const { ref, inView } = useInView();
  useFetchNextPage({ fetchNextPage, hasNextPage: !!hasNextPage, inView });
  if (isLoading) return <>{loader}</>;
  if (isError)
    return (
      <p className="h-[50px] cursor-pointer px-6 py-6 text-center text-[12px]">
        Something went wrong. <span onClick={() => refetch()}>refetch</span>
      </p>
    );
  if (resource)
    return (
      <Render
        resource={resource.pages}
        ref={ref}
        isFetchingNextPage={isFetchingNextPage}
      />
    );
};
