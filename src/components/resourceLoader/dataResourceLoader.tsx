import { UseQueryResult } from '@tanstack/react-query';
import { ReactNode } from 'react';

import { Spinner } from '../ui/CommonWidget/Loader';

export const DataResourceLoader = <T extends Record<string, any>>({
  useApiQueryHook,
  render: Render,
  loader = (
    <div className="flex h-[300px] w-full items-center justify-center">
      <Spinner className="" />
    </div>
  ),
}: {
  useApiQueryHook: (parameter?: {}, options?: {}) => UseQueryResult<T, any>;
  render: React.FC<{
    resource: T;
  }>;
  loader?: ReactNode;
}) => {
  const { isLoading, isError, data: resource, refetch } = useApiQueryHook();
  if (isLoading) return <>{loader}</>;
  if (isError)
    return (
      <p className="h-[50px] cursor-pointer px-6 py-6 text-center text-[12px]">
        Something went wrong. <span onClick={() => refetch()}>refetch</span>
      </p>
    );
  if (resource)
    return (
      <>
        <Render resource={resource} />
      </>
    );
};
