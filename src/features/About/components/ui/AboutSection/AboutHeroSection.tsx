import { useNavigate } from 'react-router-dom';

import Button from '../../../../../components/ui/ButtonComponent';
import { useCustomMedia } from '../../../../../hooks/useCustomMedia';
import { AboutHeroBg, AboutHeroBgMobile } from '../../../assets/images';

export const AboutHeroSection = () => {
  const navigate = useNavigate();
  const { screenSize } = useCustomMedia();

  return (
    <div className="relative">
      <div
        className="bg-cover px-[22.5px] pb-[40px] pt-[40px] md:px-[120px] md:py-[138px]"
        style={{
          backgroundImage: `url(${
            screenSize < 768 ? AboutHeroBgMobile : AboutHeroBg
          })`,
        }}
      >
        <div className="container_max_width">
          <div className="flex w-full flex-col gap-6 text-left text-white sm:max-w-sm md:max-w-[760px]">
            <p className="text-[20px] font-bold leading-[30px] md:text-[40px] md:leading-[60px]">
              Empowering Transformation. <br /> Igniting Innovation. <br />{' '}
              Unleashing Boundless Potential.
            </p>
            <p className="text-sm leading-[21px] sm:text-base md:text-lg md:leading-9 lg:text-xl">
              Across industries, campuses, and communities, we help innovators,
              operators, and learners execute bold ideas with the intelligence,
              speed, and clarity of Agentic AI.
            </p>
            <Button className="w-full max-w-[181px] rounded bg-primary py-6 text-base text-white hover:border-black hover:bg-black">
              <p onClick={() => navigate('/signup')}>Get Started</p>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
