import LazyLoadImageContainer from '../../../../../components/ui/CommonWidget/LazyLoadImageContainer';
import useLazyLoadingHandler from '../../../../../hooks/useLazyLoadingHandler';
import { goalsAndObjectives } from '../../../helpers/data';

export const GoalsObjectiveSection = () => {
  const imageRefs = goalsAndObjectives.map(() => useLazyLoadingHandler());
  return (
    <div className="py-5">
      <div className="mx-auto mb-14 w-full max-w-[1050px] px-5 text-center">
        <p className="mb-3 text-[16px] font-semibold sm:text-[24px] md:mb-6">
          Applied Learning, Innovation & Entrepreneurship
        </p>
        <p className="text-[16px] leading-[30px] sm:text-[16px] md:text-[18px] md:leading-[27px]">
          uPivotal integrates project-based problem-solving into university
          curricula, connecting students, industry, startups, and creatives to
          transform ideas into ventures that deliver lasting, real-world impact.
        </p>
      </div>
      <div className="flex flex-col gap-9 md:gap-16">
        {goalsAndObjectives.map((goal, index) => (
          <div
            key={goal.title}
            className={`grid grid-cols-1 gap-3 px-5 md:grid-cols-2 ${
              index % 2 === 0
                ? 'md:gap-[50px] md:pr-[165px]'
                : 'md:gap-3 md:px-[120px]'
            }`}
          >
            <LazyLoadImageContainer
              loaded={imageRefs[index].loaded}
              className={`z-[2] ${
                index % 2 === 0 ? 'order-last md:order-first' : 'order-last'
              }`}
            >
              <img
                loading="lazy"
                ref={element =>
                  (imageRefs[index].imgRefs.current[0] = element!)
                }
                src={goal.image}
                className={`top-0 z-[2] h-full w-full object-contain object-center ${
                  imageRefs[index].loaded ? 'opacity-100' : 'opacity-0'
                } duration-500 ease-in`}
                alt="Image description"
              />
            </LazyLoadImageContainer>

            <div className="flex flex-col justify-center">
              <p className="mb-3 text-[20px] font-semibold leading-[27px] md:text-[32px]">
                {index + 1}
              </p>
              <p className="text-justify text-[16px] leading-[30px] sm:text-[16px] md:text-[18px] md:leading-[27px]">
                {goal.title}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
