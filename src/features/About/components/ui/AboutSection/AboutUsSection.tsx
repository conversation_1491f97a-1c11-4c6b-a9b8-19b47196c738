import LazyLoadImageContainer from '../../../../../components/ui/CommonWidget/LazyLoadImageContainer';
import useLazyLoadingHandler from '../../../../../hooks/useLazyLoadingHandler';
import { AboutUsImage } from '../../../assets/images';

export const AboutUsSection = () => {
  const { imgRefs, loaded } = useLazyLoadingHandler();

  return (
    <div className="hidden pt-[17px] md:pt-[127px]">
      <div className="flex flex-col items-center gap-[50px]  md:flex-row md:gap-[87px]">
        <div className="px-5 md:pl-[117px]">
          <p className="mb-4 mt-3 font-semibold sm:text-[24px] md:leading-10">
            About Us
          </p>
          <LazyLoadImageContainer
            loaded={loaded}
            className="z-[2] mb-8 flex w-auto flex-grow justify-center md:hidden"
          >
            <img
              loading="lazy"
              ref={element => (imgRefs.current[1] = element!)}
              src={AboutUsImage}
              className={`top-0 z-[2] h-full w-4/5 object-contain object-center ${
                loaded ? 'opacity-100' : 'opacity-0'
              } duration-500 ease-in`}
              alt="Image description"
            />
          </LazyLoadImageContainer>
          <p className=" w-full text-justify font-spartan text-sm font-normal leading-[30px] text-subText md:max-w-[646px] md:text-base">
            We are building the intelligence layer for global transformation
            empowering innovation, industry advancement, and sustainable
            development. Our mission is to equip innovators, operators, and
            learners with the tools they need to drive meaningful change at
            scale. With Agentic AI, we compress what traditionally takes years
            into weeks delivering insight, structure, and coordinated execution
            in one seamless experience. Whether scaling an acquired company,
            launching a student-led venture, or starting a neighborhood
            business, we help people move from idea to impact without friction,
            guesswork, or delay.
          </p>
        </div>
        <LazyLoadImageContainer
          loaded={loaded}
          className="z-[2] hidden w-auto flex-grow rounded-[6.1px] md:block"
        >
          <img
            loading="lazy"
            ref={element => (imgRefs.current[0] = element!)}
            src={AboutUsImage}
            className={`top-0 z-[2] h-full w-full max-w-[594px] rounded-[6.1px] object-cover object-center ${
              loaded ? 'opacity-100' : 'opacity-0'
            } duration-500 ease-in`}
            alt="Image description"
          />
        </LazyLoadImageContainer>
      </div>
    </div>
  );
};
