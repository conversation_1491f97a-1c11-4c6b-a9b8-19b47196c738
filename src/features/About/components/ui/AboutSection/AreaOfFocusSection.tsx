import { useNavigate } from 'react-router-dom';

import { ArrowRightIcon } from '../../../../../assets/icons';
import Button from '../../../../../components/ui/ButtonComponent';
import { areasOfFocus } from '../../../helpers/data';

export const AreaOfFocusSection = () => {
  const navigate = useNavigate();
  return (
    <div className="mb-28 mt-20 px-5 md:px-[120px]">
      <p className="mb-3.5 font-semibold leading-10 sm:text-[24px] md:mb-8">
        Areas of <span className="gradient-text">Focus</span>
      </p>
      <div className="flex flex-col gap-5 pb-16 md:flex-row md:gap-10">
        {areasOfFocus.map((focus, index) => (
          <div
            key={focus}
            className={`font-medium leading-[25px] md:text-[16px] md:leading-8 ${
              index === 0 ? '' : 'whitespace-pre-line'
            } md:whitespace-pre-line`}
          >
            <div className="gradient-bg mb-5 h-6 w-6 rounded-full"></div>
            {focus}
          </div>
        ))}
      </div>
      <Button
        onClick={() => navigate('/statements')}
        className="mx-auto hidden flex-nowrap bg-[#F7C19326] px-2.5 py-1 md:flex"
      >
        View problem statements under these categories &nbsp;&nbsp;
        <ArrowRightIcon stroke="var(--dark-gray)" />
      </Button>
    </div>
  );
};
