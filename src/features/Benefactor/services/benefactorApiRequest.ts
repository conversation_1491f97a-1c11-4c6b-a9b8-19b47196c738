import { publicRequest } from '../../../lib/axios/publicRequest'
import { IBenefactorPayload } from '../../../types'
import { uPivotalBenefactor } from '../../../utils/apiServiceControllersRoute'
import { BASE_URL } from '../../../utils/apiUrls'

export const useBenefactorApi = () => {
  const contactSales = async (payload: IBenefactorPayload): Promise<any> => {
    const res = await publicRequest(BASE_URL)?.post(
      `${uPivotalBenefactor}/public`,
      payload
    )
    return res?.data
  }

  return contactSales
}
