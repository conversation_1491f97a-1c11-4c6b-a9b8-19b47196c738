import * as yup from 'yup';

export const benefactorSchema = yup
  .object()
  .shape({
    firstName: yup.string().required('First Name is a required field'),
    lastName: yup.string().required('Last Name is a required field'),
    nameOfGrant: yup.string().required('Grant Name is a required field'),
    grantAmount: yup
      .number()
      .typeError('Grant Amount must be a number')
      .required('Grant Amount is a required field'),
    description: yup.string().required('Description is a required field'),
    email: yup
      .string()
      .email('Email is invalid')
      .required('Email is a required field'),
    applicationRequirements: yup
      .string()
      .required('Application requirements are required'),
    regionFocus: yup
      .array()
      .of(yup.string().required('Pick one or more Region Focus')),
    sustainableDevelopmentCategory: yup
      .string()
      .required('Goals Category is a required field'),
    captcha: yup.string().required(),
  })
  .required();
