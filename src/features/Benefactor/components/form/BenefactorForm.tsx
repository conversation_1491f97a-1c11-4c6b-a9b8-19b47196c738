import 'react-phone-input-2/lib/bootstrap.css';

import { uniqueId } from 'lodash';
import { useState } from 'react';
import ReCAPTCHA from 'react-google-recaptcha';
import { Controller, SubmitHandler } from 'react-hook-form';

import Form from '../../../../components/forms/Form';
import FormInputBox from '../../../../components/forms/FormInputBox';
import FormSelectBox from '../../../../components/forms/FormSelectBox';
import FormTextAreaBox from '../../../../components/forms/FormTextAreaBox';
import Button from '../../../../components/ui/ButtonComponent';
import { Spinner } from '../../../../components/ui/CommonWidget/Loader';
import { useGetSDGs } from '../../../../hooks/apiQueryHooks/eduQueryHooks';
import { useGetCountries } from '../../../../hooks/apiQueryHooks/userQueryHooks';
import { useFormDataAndApiMutateHandler } from '../../../../hooks/useFormDataAndApiMutateHandler';
import { IBenefactorPayload } from '../../../../types';
import { GOOGLE_SITE_KEY } from '../../../../utils/apiUrls';
import { useBenefactor } from '../../hooks/apiQueryHooks/benefactorQueryHooks';
import useCreateSdgCategoriesArray from '../../hooks/useCreateSdgCategoriesArr';
import { benefactorSchema } from '../../lib/yup/validations';
import { Helper } from '@/utils/helpers';

export const BenefactorForm = () => {
  const [key, setKey] = useState<string>();
  const next = () => {
    resetField('firstName');
    resetField('lastName');
    resetField('email');
    resetField('description');
    resetField('applicationRequirements');
    resetField('nameOfGrant');
    resetField('grantAmount');
    resetField('captcha');
    setKey(uniqueId());
    reset({});
  };

  const { data: sdg, isLoading: isLoadingSdg } = useGetSDGs({
    staleTime: 1000 * 60 * 60 * 24,
    cacheTime: 1000 * 60 * 60 * 25,
  });

  const manipulatedSDGs = useCreateSdgCategoriesArray(sdg?.data || []);

  const { data: countries, isLoading: isLoadingCountries } = useGetCountries();

  const manipulatedCountries = Helper.createCountriesOptionsArray(
    countries?.data
      ? {
          countries: [
            ...(countries?.data?.countries || []),
            { shortName: 'Africa' },
            { shortName: 'Asia' },
            { shortName: 'North America' },
            { shortName: 'South America' },
            { shortName: 'Europe' },
            { shortName: 'Antartica' },
          ],
        }
      : { countries: [] },
  );

  const {
    reset,
    register,
    resetField,
    handleSubmit,
    isLoading,
    formState: { errors, isDirty, isValid },
    mutate,
    control,
  } = useFormDataAndApiMutateHandler<IBenefactorPayload, any>(
    benefactorSchema,
    useBenefactor,
    {
      next,
    },
  );

  const onSubmit: SubmitHandler<IBenefactorPayload> = data => {
    mutate({ ...data });
  };

  return (
    <Form
      id="benefactor-form"
      onSubmit={handleSubmit(onSubmit)}
      className="benefactor-form w-full"
    >
      <div className="flex flex-col gap-6">
        <div>
          <FormInputBox<IBenefactorPayload>
            name="firstName"
            labelName="First Name"
            className="input bg-gradient-white-gray"
            placeholder="First Name"
            errors={errors}
            type="text"
            autoComplete="first-name"
            registerHanlder={() => register('firstName')}
          />
        </div>
        <div>
          <FormInputBox<IBenefactorPayload>
            name="lastName"
            labelName="Last Name"
            className="input bg-gradient-white-gray"
            placeholder="Last Name"
            errors={errors}
            type="text"
            autoComplete="last-name"
            registerHanlder={() => register('lastName')}
          />
        </div>
        <div>
          <FormInputBox<IBenefactorPayload>
            name="email"
            labelName="Email"
            className="input bg-gradient-white-gray"
            placeholder="Email"
            errors={errors}
            type="email"
            autoComplete="email"
            registerHanlder={() => register('email')}
          />
        </div>
        <div>
          <FormInputBox<IBenefactorPayload>
            name="nameOfGrant"
            labelName="Name of Grant"
            className="input bg-gradient-white-gray"
            placeholder="Name of Grant"
            errors={errors}
            type="text"
            autoComplete="grant-name"
            registerHanlder={() => register('nameOfGrant')}
          />
        </div>
        <div>
          <FormSelectBox<IBenefactorPayload>
            key={key}
            labelName="Goals Category"
            options={manipulatedSDGs}
            optionsArr={manipulatedSDGs}
            control={control}
            name="sustainableDevelopmentCategory"
            errors={errors}
            placeholder="Goals Category"
            isLoading={isLoadingSdg}
            className="select bg-gradient-white-gray !h-auto"
            classNamePrefix="benefactor-select"
          />
        </div>
        <div>
          <FormInputBox<IBenefactorPayload>
            name="grantAmount"
            labelName="Grant Amount (USD)"
            className="input bg-gradient-white-gray"
            placeholder="Grant Amount"
            errors={errors}
            type="number"
            autoComplete="grant-name"
            registerHanlder={() => register('grantAmount')}
          />
        </div>
        <div>
          <FormSelectBox<IBenefactorPayload>
            key={key}
            labelName="Region Focus"
            options={manipulatedCountries}
            optionsArr={manipulatedCountries}
            control={control}
            name="regionFocus"
            errors={errors}
            placeholder="Region Focus"
            isLoading={isLoadingCountries}
            className="select bg-gradient-white-gray !h-auto"
            isMulti
          />
        </div>
        <div>
          <FormTextAreaBox<IBenefactorPayload>
            labelName="Description"
            className="input bg-gradient-white-gray !h-auto"
            name="description"
            errors={errors}
            placeholder="Description"
            autoComplete="description"
            registerHanlder={() => register('description')}
            rows={4}
          />
        </div>
        <div>
          <FormTextAreaBox<IBenefactorPayload>
            labelName="Application Requirements"
            className="input bg-gradient-white-gray !h-auto"
            name="applicationRequirements"
            errors={errors}
            placeholder="Application Requirements"
            autoComplete="applicationRequirements"
            registerHanlder={() => register('applicationRequirements')}
            rows={4}
          />
        </div>
        <div>
          <Controller
            key={key}
            name="captcha"
            control={control}
            render={({ field }) => (
              <ReCAPTCHA
                sitekey={GOOGLE_SITE_KEY}
                onChange={v => field.onChange(v)}
              />
            )}
          />
        </div>
        <div className={`flex justify-center`}>
          <Button
            type="submit"
            disabled={!isDirty || !isValid || isLoading}
            className="w-full max-w-[220px] rounded-none bg-primary py-3 text-white hover:border-black hover:bg-black disabled:cursor-not-allowed disabled:bg-disabled disabled:text-opacity-70"
          >
            {isLoading ? (
              <Spinner className="border-white border-b-transparent" />
            ) : (
              <p className=" text-[14px] font-[700] sm:text-[16px]">Submit</p>
            )}
          </Button>
        </div>
      </div>
    </Form>
  );
};
