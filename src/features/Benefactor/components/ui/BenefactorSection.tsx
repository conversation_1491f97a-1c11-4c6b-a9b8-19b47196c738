import bgImage from '../../../../assets/images/rectangleThree.png'
import LazyLoadImageContainer from '../../../../components/ui/CommonWidget/LazyLoadImageContainer'
import useLazyLoadingHandler from '../../../../hooks/useLazyLoadingHandler'
import { becomeBenefactorImage } from '../../assets/images'
import { BenefactorForm } from '../form/BenefactorForm'

export const BenefactorSection = () => {
  const { imgRefs, loaded } = useLazyLoadingHandler()

  return (
    <div className="pt-10 bg-transparent flex flex-col items-center">
      <LazyLoadImageContainer
        loaded={loaded}
        className="z-[2] max-h-[500px] h-full w-full"
      >
        <img
          loading="lazy"
          ref={(element) => (imgRefs.current[0] = element!)}
          src={becomeBenefactorImage}
          className={`z-[2] top-0 object-contain object-center w-full h-full ${
            loaded ? 'opacity-100' : 'opacity-0'
          } duration-500 ease-in`}
          alt="Image description"
        />
      </LazyLoadImageContainer>
      <section
        style={{ backgroundImage: `url(${bgImage})` }}
        className="flex justify-center overflow-hidden w-full bg-cover"
      >
        <div className="max-w-[650px] w-full py-14 bg-transparent">
          <p className="text-[24px] font-semibold text-center mb-8">
            Become A Student Team Benefactor
          </p>
          <BenefactorForm />
        </div>
      </section>
    </div>
  )
}
