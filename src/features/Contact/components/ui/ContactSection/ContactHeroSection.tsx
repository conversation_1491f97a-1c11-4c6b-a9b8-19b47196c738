// import { EmailIcon } from "../../../assets/icons";
// import Button from "../../../../../components/ui/Button";
import LazyLoadImageContainer from '../../../../../components/ui/CommonWidget/LazyLoadImageContainer'
// import CopyToClipboard from "react-copy-to-clipboard";
import useLazyLoadingHandler from '../../../../../hooks/useLazyLoadingHandler'
import { ContactUsImage } from '../../../assets/images'
// import { UPIVOTAL_CONTACT_EMAIL } from "../../../../../utils/generalUrl";

export const ContactHeroSection = () => {
  const { imgRefs, loaded } = useLazyLoadingHandler()

  return (
    <div className="pt-5 lg:pt-[86px] px-[18px] sm:pl-6 md:px-[120px]">
      <div className="flex justify-between">
        <div className="sm:pt-11 flex flex-col items-center sm:items-start gap-6 max-w-[400px] md:max-w-[480px]">
          <p className="text-[20px] md:text-[37px] text-center sm:text-left font-semibold">
            Connect with us
          </p>
          <p className="text-[14px] sm:text-[16px] text-center sm:text-justify lg:text-[18px] leading-7">
            Want to chat? We'd love to hear from you! Get in touch with our
            Customer Success Team to inquire about partnership opportunities,
            speaking events or to just say hello.
          </p>
          {/* <CopyToClipboard text={UPIVOTAL_CONTACT_EMAIL}>
            <Button className="bg-primary hover:bg-black hover:border-black text-white w-full max-w-[181px]   rounded-none">
              <EmailIcon />
              &nbsp;&nbsp;&nbsp;Copy Email
            </Button>
          </CopyToClipboard> */}
        </div>

        <LazyLoadImageContainer
          loaded={loaded}
          className="hidden sm:block z-[2] max-w-[400px] w-full"
        >
          <img
            loading="lazy"
            ref={(element) => (imgRefs.current[0] = element!)}
            src={ContactUsImage}
            className={`z-[2] top-0 object-contain object-center w-full h-full ${
              loaded ? 'opacity-100' : 'opacity-0'
            } duration-500 ease-in`}
            alt="Image description"
          />
        </LazyLoadImageContainer>
      </div>
    </div>
  )
}
