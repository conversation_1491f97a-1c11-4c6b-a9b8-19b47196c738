import { RectangleOneImage } from '../../../../../assets/images'
import { useCustomMedia } from '../../../../../hooks/useCustomMedia'
import { ContactUsForm } from '../../forms/ContactUsForm'
import { ContactHeroSection } from './ContactHeroSection'
import { ContactUsSection } from './ContactUsSection'

export const ContactSection = () => {
  const { screenSize } = useCustomMedia()

  return (
    <section
      className="bg-center bg-no-repeat mt-20 bg-[#fff] lg:bg-white bg-siz"
      style={{
        backgroundImage: `url(${RectangleOneImage})`,
        backgroundSize: screenSize < 768 ? '200% 80%' : '100% auto',
        backgroundPositionY: screenSize < 768 ? '45%' : '0%',
      }}
    >
      <div className="container_max_width">
        <ContactHeroSection />
        <ContactUsSection>
          <ContactUsForm />
        </ContactUsSection>
      </div>
    </section>
  )
}
