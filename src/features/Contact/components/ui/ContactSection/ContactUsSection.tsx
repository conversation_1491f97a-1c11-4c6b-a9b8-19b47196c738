import { ReactNode } from 'react'

import LazyLoadImageContainer from '../../../../../components/ui/CommonWidget/LazyLoadImageContainer'
import useLazyLoadingHandler from '../../../../../hooks/useLazyLoadingHandler'
import { ContactUsImage } from '../../../assets/images'

export const ContactUsSection = ({ children }: { children: ReactNode }) => {
  const { imgRefs, loaded } = useLazyLoadingHandler()

  return (
    <div className="py-10 bg-transparent md:bg-[#FFFAF7] flex flex-col items-center">
      <p className="hidden sm:block text-[20px] md:text-[40px] font-semibold text-center">
        Contact Us
      </p>
      <div className="flex sm:hidden justify-between items-center w-full pt-[22px]">
        <p className="pl-5 font-semibold">Contact Us</p>
        <LazyLoadImageContainer
          loaded={loaded}
          className="z-[2] max-w-[200px] w-full"
        >
          <img
            loading="lazy"
            ref={(element) => (imgRefs.current[0] = element!)}
            src={ContactUsImage}
            className={`z-[2] top-0 object-contain object-center w-full h-full ${
              loaded ? 'opacity-100' : 'opacity-0'
            } duration-500 ease-in`}
            alt="Image description"
          />
        </LazyLoadImageContainer>
      </div>
      <div className="max-w-[650px] w-full p-5 md:p-10 bg-transparent md:bg-white">
        <p className="text-center md:text-left md:text-[24px] mb-3 md:mb-6">
          Get In touch
        </p>
        <p className="text-center md:text-left text-grayNine mb-8">
          Feel free contact with us, we love to make new partners & friends
        </p>
        {children}
      </div>
    </div>
  )
}
