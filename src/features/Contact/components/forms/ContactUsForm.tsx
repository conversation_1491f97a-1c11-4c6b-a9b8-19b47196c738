import 'react-phone-input-2/lib/bootstrap.css';

import { ReactNode } from 'react';
import ReCAPTCHA from 'react-google-recaptcha';
import { Controller, SubmitHandler } from 'react-hook-form';

import Form from '../../../../components/forms/Form';
import FormInputBox from '../../../../components/forms/FormInputBox';
import FormRadioButton from '../../../../components/forms/FormRadioButton';
import FormTextAreaBox from '../../../../components/forms/FormTextAreaBox';
import PhoneInputBox from '../../../../components/forms/PhoneInputBox';
import Button from '../../../../components/ui/ButtonComponent';
import { Spinner } from '../../../../components/ui/CommonWidget/Loader';
import { useFormDataAndApiMutateHandler } from '../../../../hooks/useFormDataAndApiMutateHandler';
import { GOOGLE_SITE_KEY } from '../../../../utils/apiUrls';
import { PaperPlaneIcon } from '../../assets/icons';
import { useContact } from '../../hooks/apiQueryHooks/contactQueryHooks';
import { contactSalesSchema } from '../../lib/yup/validations';
import { IContactPayload } from '../../type';
import FileInput from '../../../../components/forms/FileInput';

export const ContactUsForm = () => {
  const next = () => {
    resetField('firstname');
    resetField('lastname');
    resetField('email');
    resetField('phoneNumber');
    resetField('contactUsSubject');
    resetField('message');
    resetField('supportImage');
    resetField('captcha');
    reset({});
  };
  const inputCls = 'w-full bg-white md:bg-transparent input mt-2';

  const {
    reset,
    control,
    register,
    resetField,
    handleSubmit,
    isLoading,
    formState: { errors, isDirty, isValid },
    mutate,
  } = useFormDataAndApiMutateHandler<IContactPayload, any>(
    contactSalesSchema,
    useContact,
    {
      next,
    },
  );
  const onSubmit: SubmitHandler<IContactPayload> = data => {
    mutate(data);
  };
  return (
    <Form onSubmit={handleSubmit(onSubmit)} className="w-full px-6 md:px-0">
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        <div className="col-span-1">
          <FormInputBox<IContactPayload>
            className={inputCls}
            name="firstname"
            errors={errors}
            labelName="First Name"
            type="text"
            autoComplete="first-name"
            registerHanlder={() => register('firstname')}
            placeholder="First Name"
          />
        </div>
        <div className="col-span-1">
          <FormInputBox<IContactPayload>
            className={inputCls}
            name="lastname"
            errors={errors}
            labelName="Last Name"
            type="text"
            autoComplete="last-name"
            registerHanlder={() => register('lastname')}
            placeholder="Last Name"
          />
        </div>
        <div className="col-span-1">
          <FormInputBox<IContactPayload>
            className={`${inputCls}`}
            name="email"
            errors={errors}
            labelName="Email"
            type="email"
            autoComplete="email"
            registerHanlder={() => register('email')}
            placeholder="Email"
          />
        </div>
        <div className="col-span-1">
          <PhoneInputBox<IContactPayload>
            placeholder="  "
            name="phoneNumber"
            errors={errors}
            labelName="Phone Number"
            control={control}
          />
        </div>
        <div className="col-span-1 md:col-span-2">
          <p className="mb-[15px] text-[14px] font-semibold text-primary">
            Select Subject
          </p>
          <div className="mb-2 flex flex-col flex-wrap gap-[15px] md:flex-row">
            <FormRadioButton<IContactPayload>
              labelName={<p className="text-primary">General Inquiry</p>}
              name="contactUsSubject"
              errors={{}}
              registerHanlder={() => register('contactUsSubject')}
              value="GENERAL_INQUIRY"
            />
          </div>
          <div className="flex flex-col flex-wrap gap-[15px] md:flex-row">
            <FormRadioButton<IContactPayload>
              labelName={
                <p className="text-primary">Account and Data Delete Request</p>
              }
              name="contactUsSubject"
              errors={{}}
              registerHanlder={() => register('contactUsSubject')}
              value="ACCOUNT_DELETION_REQUEST"
            />
          </div>
          {errors.contactUsSubject && (
            <p className="mt-2 text-[14px] text-red-800">
              {errors.contactUsSubject?.message as ReactNode}
            </p>
          )}
        </div>
        <div className="col-span-full">
          <FormTextAreaBox<IContactPayload>
            className={`${inputCls} min-h-[100px]`}
            name="message"
            errors={errors}
            labelName="Message"
            placeholder="Write your message"
            autoComplete="message"
            registerHanlder={() => register('message')}
            rows={4}
          />
        </div>
        <div className="col-span-full">
          <FileInput<IContactPayload>
            name="supportImage"
            control={control}
            errors={{}}
            label="Tap to Upload"
            accept="image/*"
          />
        </div>
        <div className="col-span-full md:col-span-1">
          <Controller
            name="captcha"
            control={control}
            render={({ field }) => (
              <ReCAPTCHA
                sitekey={GOOGLE_SITE_KEY}
                onChange={v => field.onChange(v)}
              />
            )}
          />
        </div>
        <div className="col-span-full">
          <Button
            disabled={!isDirty || !isValid || isLoading}
            className="w-full max-w-[240px] rounded-none bg-primary py-3 text-white hover:border-black hover:bg-black disabled:cursor-not-allowed disabled:bg-disabled disabled:text-opacity-70"
          >
            {isLoading ? (
              <Spinner className="border-[3px] border-white border-b-[transparent]" />
            ) : (
              <>
                Send Message &nbsp;&nbsp;
                <span className="h-6 w-6">
                  <PaperPlaneIcon />
                </span>
              </>
            )}
          </Button>
        </div>
      </div>
    </Form>
  );
};
