import * as yup from 'yup'

import { Helper } from '../../../../utils/helpers'

export const contactSalesSchema = yup
  .object()
  .shape({
    firstname: yup.string().required('First Name is a required field'),
    lastname: yup.string().required('Last Name is a required field'),
    contactUsSubject: yup.string().required('Subject is a required field'),
    phoneNumber: yup
      .string()
      .required('Phone Number is a required field')
      .matches(Helper.phoneRegExp, 'Please enter valid phone number'),
    email: yup
      .string()
      .email('Email is invalid')
      .required('Email is a required field'),
    message: yup.string().required('Message is a required field'),
    supportImage: yup
      .mixed<FileList>()
      .test('fileSize', 'The file is too large (max 10MB)', value => {
        const file = value as FileList;
        if (!file || !file[0]) return true;
        return file[0].size <= 10_000_000;
      }),
    captcha: yup.string().required(),
  })
  .required();
