import { publicRequest } from '../../../lib/axios/publicRequest'
import { contact } from '../../../utils/apiServiceControllersRoute'
import { BASE_URL } from '../../../utils/apiUrls'
import { Helper } from '../../../utils/helpers'
import { IContactPayload } from '../type'

export const useContactApi = () => {
  const contactSales = async (payload: IContactPayload): Promise<any> => {
    const formData = Helper.createFormData(
      Helper.clearEmptyField({
        ...payload,
        supportImage: payload?.supportImage?.[0],
      })
    )
    const res = await publicRequest(BASE_URL)?.post(
      `${contact}/upivotal`,
      formData,
      {
        headers: { 'Content-Type': 'multipart/form-data' },
      }
    )
    return res?.data
  }

  return contactSales
}
