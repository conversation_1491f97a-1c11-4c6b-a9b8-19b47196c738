import { usePrivateRequest } from '../../../lib/axios/usePrivateRequest';
import {
  ServerChatMessages,
  ServerChatRoom,
  ServerChatRooms,
  ServerCommonTeamMembers,
} from '../../../types';
import {
  chatMessage,
  chatRoom,
  teamMember,
} from '../../../utils/apiServiceControllersRoute';
import { BASE_URL, SOCKET_URL } from '../../../utils/apiUrls';
import { Helper } from '../../../utils/helpers';

export const useGetChatMessagesApi = () => {
  const axiosInstance = usePrivateRequest(SOCKET_URL);
  const getChatMessages = async ({
    pageParam: pageParameter = 1,
    queryKey = [''],
  }): Promise<ServerChatMessages> => {
    const [, chatRoomId] = queryKey;
    const cleanParams = Helper.clearEmptyField({ page: pageParameter });
    const res = await axiosInstance.current?.get(
      `/api/v1${chatMessage}/${chatRoomId}`,
      {
        params: { ...cleanParams },
      },
    );
    return res?.data;
  };
  return getChatMessages;
};

export const useGetChatRoomsApi = () => {
  const axiosInstance = usePrivateRequest(SOCKET_URL);
  return async (): Promise<ServerChatRooms> => {
    const res = await axiosInstance.current?.get(`/api/v1${chatRoom}`);
    return res?.data;
  };
};
export const useGetChatRoomApi = () => {
  const axiosInstance = usePrivateRequest(SOCKET_URL);
  return async (id: string): Promise<ServerChatRoom> => {
    const res = await axiosInstance.current?.get(`/api/v1${chatRoom}/${id}`);
    return res?.data;
  };
};
export const useGetUnReadMessagesApi = () => {
  const axiosInstance = usePrivateRequest(SOCKET_URL);
  return async (): Promise<any> => {
    const res = await axiosInstance.current?.get(
      `/api/v1${chatMessage}/unread/count`,
    );
    return res?.data;
  };
};

export const useAddChatMessagesApi = () => {
  const axiosInstance = usePrivateRequest(SOCKET_URL);
  return async (payload: any): Promise<any> => {
    const data = Helper.createFormData(Helper.clearEmptyField(payload));
    const res = await axiosInstance.current?.post(
      `/api/v1${chatMessage}`,
      data,
      {
        headers: {
          'Content-Type': 'multipart/form-data, */*',
        },
      },
    );
    return res?.data;
  };
};
export const useConfirmReadStatusApi = () => {
  const axiosInstance = usePrivateRequest(SOCKET_URL);
  return async (chatRoomId: string): Promise<any> => {
    const res = await axiosInstance.current?.patch(
      `/api/v1${chatMessage}/${chatRoomId}/readStatus`,
    );
    return res?.data;
  };
};

export const useGetCommonTeamMembersApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);
  return async (): Promise<ServerCommonTeamMembers> => {
    const res = await axiosInstance.current?.get(
      `${teamMember}/common-team-members`,
    );
    return res?.data;
  };
};
export const useGetCommonTeamMembersInfiniteQueryApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);
  return async ({ pageParam = 1 }): Promise<ServerCommonTeamMembers> => {
    const cleanParams = Helper.clearEmptyField({
      page: pageParam,
    });
    const res = await axiosInstance.current?.get(
      `${teamMember}/common-team-members`,
      { params: { ...cleanParams } },
    );
    return res?.data;
  };
};

export const useUpdateMessageApi = () => {
  const axiosInstance = usePrivateRequest(SOCKET_URL);
  return async (payload: any): Promise<any> => {
    const data = {
      chatRoomId: payload?.chatRoomId,
      content: payload?.content,
    };
    const res = await axiosInstance.current?.patch(
      `/api/v1${chatMessage}/${payload?.chatMessageId}`,
      data,
    );
    return res?.data;
  };
};
