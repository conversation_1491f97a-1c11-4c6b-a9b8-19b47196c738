import { SetStateAction, useCallback, useState } from 'react'

import { MessageType } from '../../../../types'

export default function useChatContextStateAndActions() {
  const [messageType, setMessageType] = useState<MessageType>('All')
  const [newChatOptions, showNewChatOptions] = useState(false)
  const [files, setFiles] = useState<File[]>([])
  const [showDropzone, setShowDropzone] = useState(false)

  const setMessageTypeHandler = useCallback(
    (type: MessageType) => setMessageType(type),
    []
  )
  const showNewChatOptionsHandler = useCallback(
    (type: SetStateAction<boolean>) => showNewChatOptions(type),
    []
  )
  const setShowDropzoneHandler = useCallback(
    (type: SetStateAction<boolean>) => setShowDropzone(type),
    []
  )
  const setFilesHandler = useCallback(
    (value: React.SetStateAction<File[]>) => setFiles(value),
    []
  )

  return {
    messageType,
    setMessageTypeHandler,
    newChatOptions,
    showNewChatOptionsHandler,
    setFilesHandler,
    files,
    showDropzone,
    setShowDropzoneHandler,
  }
}
