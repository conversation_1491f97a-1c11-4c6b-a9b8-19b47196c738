import { createContext, useContext } from 'react'

import useChatContextStateAndActions from './useChatContextStateAndActions'

type ChatContextType = ReturnType<typeof useChatContextStateAndActions>

export const ChatContext = createContext({} as ChatContextType)

export const useChatContext = () => {
  const context = useContext(ChatContext)
  if (!context) {
    throw new Error(`Out of context`)
  }
  return context
}

export const ChatProvider = ({ children }: { children: React.ReactNode }) => {
  return (
    <ChatContext.Provider value={useChatContextStateAndActions()}>
      {children}
    </ChatContext.Provider>
  )
}
