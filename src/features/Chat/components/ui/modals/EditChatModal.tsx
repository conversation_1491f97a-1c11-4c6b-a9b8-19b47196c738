import ModalCase from '@/components/ui/Modals/ModalCase';
import { AnimatePresence } from 'framer-motion';
import EditChatForm from '../../forms/EditChatForm';
import { useAppContext } from '@/context/event/AppEventContext';

export default function EditChatModal() {
  const { showModal } = useAppContext();

  return (
    <AnimatePresence>
      {showModal === 'EditChatModal' && (
        <ModalCase
          className={`mx-auto max-h-[90%] w-[95%] overflow-y-auto  bg-white  px-0 py-0 sm:max-w-[600px]`}
          hideCancelButton={true}
        >
          <div className="mt-8">
            <EditChatForm />
          </div>
        </ModalCase>
      )}
    </AnimatePresence>
  );
}
