import { useNavigate } from 'react-router-dom';

import { useAppContext } from '../../../../../context/event/AppEventContext';
import { useCreateChatMessage } from '../../../../../hooks/apiQueryHooks/chatQueryHooks';
import useHandleApiFeebackWithToast from '../../../../../hooks/useHandleApiFeebackWithToast';
import { DataResponse } from '../../../../../types';
import ContactThread from './ContactThread';
import { forwardRef, Ref } from 'react';

type Props = {
  name: string;
  image: string;
  userId: string;
};

export default forwardRef(function ContactThreadOneOnOne(
  { image, name, userId }: Props,
  ref: Ref<HTMLDivElement>,
) {
  const { currentAccountType } = useAppContext();
  const navigate = useNavigate();
  const next = (res: DataResponse<any>) => {
    navigate(
      `/${currentAccountType}/chat?chat_room_id=${res?.data?._id}&c_c=${name}`,
    );
  };
  const { mutate: createChatRoom } = useCreateChatMessage({
    ...useHandleApiFeebackWithToast({ next, noSuccessToast: true }),
  });
  return (
    <ContactThread
      image={image}
      name={name}
      startChat={() => createChatRoom({ receiverId: userId })}
      ref={ref}
    />
  );
});
