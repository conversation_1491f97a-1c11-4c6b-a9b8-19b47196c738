import { DownloadIconTwo } from '@/features/WatchVideo/assets/icons';

type Props = { url: string; isSender: boolean };

export default function ImageChatBubble({ url, isSender }: Props) {
  return (
    <div
      className={`group relative h-auto w-full  max-w-[243px] rounded-[16px] p-[10px] max-xs:aspect-square max-xs:h-[150px] ${
        isSender
          ? 'ml-auto !rounded-tr-none bg-[#A2E0FF]'
          : '!rounded-tl-none bg-white'
      }`}
    >
      <img
        src={url}
        alt="conversation-channel-image"
        className="h-full max-h-full w-full max-w-full rounded-[16px_0px_16px_16px] object-cover max-xs:aspect-square max-xs:h-[150px]"
      />
      <div
        className={`absolute inset-0 flex items-center 
      justify-center rounded-[16px]  ${
        isSender ? '!rounded-tr-none' : '!rounded-tl-none '
      } opacity-0 transition duration-200 group-hover:bg-[rgba(0,0,0,0.2)] group-hover:opacity-100`}
      >
        <a href={url} download={url.split('/')[-1]} rel={'noopener noreferrer'}>
          <DownloadIconTwo className="h-10 w-10 fill-white max-xs:h-5 max-xs:w-5 xs:cursor-pointer" />
        </a>
      </div>
    </div>
  );
}
