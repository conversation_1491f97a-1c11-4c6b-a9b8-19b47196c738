type Props = { url: string; isSender: boolean };

export default function VideoChatBubble({ url, isSender }: Props) {
  return (
    <div
      className={`group relative h-auto w-full  max-w-[243px] rounded-[16px] p-[10px] max-xs:aspect-square max-xs:h-[150px] ${
        isSender
          ? 'ml-auto !rounded-tr-none bg-[#A2E0FF]'
          : '!rounded-tl-none bg-white'
      }`}
    >
      <video
        loop
        autoPlay
        controls
        src={url}
        className=" h-full max-h-full w-full max-w-full rounded-[16px_0px_16px_16px] object-cover max-xs:aspect-square max-xs:h-[150px]"
      />
    </div>
  );
}
