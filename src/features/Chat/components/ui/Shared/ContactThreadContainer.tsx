import { Spinner } from '../../../../../components/ui/CommonWidget/Loader';
import useHandleApiFeebackWithToast from '../../../../../hooks/useHandleApiFeebackWithToast';
import { useHandleQueryParams } from '../../../../../hooks/useHandleQueryParams';
import { useGetMyTeamsInfiniteQuery } from '../../../../Teams';
import { useChatContext } from '../../../context/chat/ChatContext';
import { useGetCommonTeamMembers } from '../../../hooks/apiQueryHooks/chatQueryHooks';
import ChatSearch from '../../forms/ChatSearch';
import ContactThreadGroup from './ContactThreadGroup';
import ContactThreadOneOnOne from './ContactThreadOneOnOne';
import { useInView } from 'react-intersection-observer';
import useFetchNextPage from '@/hooks/useFetchNextPage';
import { useUserContext } from '@/context/user/UserContext';
import { useGetProjectsUserMemberOfInfiniteQuery } from '@/features/Categories/hooks/apiQueryHooks/eduQueryHooks';

export default function ContactThreadContainer() {
  const { query } = useHandleQueryParams();
  const { showNewChatOptionsHandler } = useChatContext();
  const { data } = useUserContext();

  const next = () => {
    showNewChatOptionsHandler(false);
  };

  const {
    data: resource,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useGetMyTeamsInfiniteQuery({
    ...useHandleApiFeebackWithToast({ next, noSuccessToast: true }),
    enabled: query.get('new_chat') === 'teams',
  });
  const {
    data: projectResource,
    fetchNextPage: fetchNextPageProjects,
    hasNextPage: hasNextPageProjects,
    isFetchingNextPage: isFetchingNextPageProjects,
  } = useGetProjectsUserMemberOfInfiniteQuery({
    ...useHandleApiFeebackWithToast({ next, noSuccessToast: true }),
    enabled: query.get('new_chat') === 'projects',
  });
  const { data: commonTeamMembers } = useGetCommonTeamMembers({
    ...useHandleApiFeebackWithToast({ next, noSuccessToast: true }),
    enabled: query.get('new_chat') === 'one-on-one',
  });

  const { ref, inView } = useInView();
  useFetchNextPage({ fetchNextPage, hasNextPage: !!hasNextPage, inView });
  useFetchNextPage({
    fetchNextPage: fetchNextPageProjects,
    hasNextPage: !!hasNextPageProjects,
    inView,
  });

  return (
    <div className="flex h-[calc(100%-24px)] flex-col">
      <div className="p-6 pb-4">
        <ChatSearch />
      </div>
      <div className="flex flex-col overflow-auto">
        {query.get('new_chat') === 'teams' &&
          (resource && resource.pages?.[0]?.data?.teams.length > 0 ? (
            resource.pages.map((teams, index) => (
              <div key={index}>
                {teams.data.teams.map((contact, i) => {
                  if (teams.data.teams?.length == i + 1) {
                    return (
                      <ContactThreadGroup
                        key={contact.teamRef}
                        name={contact?.teamName}
                        image={contact.image}
                        roomRef={contact.teamRef}
                        ref={ref}
                      />
                    );
                  }
                  return (
                    <ContactThreadGroup
                      key={contact.teamRef}
                      name={contact?.teamName}
                      image={contact.image}
                      roomRef={contact.teamRef}
                    />
                  );
                })}
              </div>
            ))
          ) : (
            <p className="mx-auto pt-20">No team found!</p>
          ))}
        {query.get('new_chat') === 'projects' &&
          (projectResource &&
          projectResource?.pages?.[0].data.projects.length > 0 ? (
            projectResource.pages.map((contacts, index) => (
              <div key={index}>
                {contacts.data.projects.map((contact, i) => {
                  if (contacts.data.projects?.length == i + 1) {
                    return (
                      <ContactThreadGroup
                        key={contact.projectRef}
                        name={contact?.projectName}
                        image={contact.projectImageUrl}
                        roomRef={contact.projectRef}
                        ref={ref}
                      />
                    );
                  }
                  return (
                    <ContactThreadGroup
                      key={contact.projectRef}
                      name={contact?.projectName}
                      image={contact.projectImageUrl}
                      roomRef={contact.projectRef}
                    />
                  );
                })}
              </div>
            ))
          ) : (
            <p className="mx-auto pt-20">No project found!</p>
          ))}
        {query.get('new_chat') === 'one-on-one' &&
          (commonTeamMembers &&
          commonTeamMembers.data.length > 0 &&
          commonTeamMembers.data.filter(
            member => member.userId !== data?.user.userId,
          ).length > 0 ? (
            commonTeamMembers.data
              .filter(member => member.userId !== data?.user.userId)
              .map(({ firstName, lastName, profilePicture, userId }) => (
                <ContactThreadOneOnOne
                  key={userId}
                  name={`${firstName} ${lastName}`}
                  image={profilePicture || ''}
                  userId={userId}
                  ref={ref}
                />
              ))
          ) : (
            <p className="mx-auto pt-20">No team members found!</p>
          ))}
      </div>
      {(isFetchingNextPage || isFetchingNextPageProjects) && (
        <div className="h-[50px] py-4">
          <Spinner />
        </div>
      )}
    </div>
  );
}
