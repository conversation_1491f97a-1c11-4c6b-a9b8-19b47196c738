import { DocIcon } from '@/features/Chat/assets/icons';
import { ChatHelper } from '@/features/Chat/utils/helper';

type Props = { file: string; isSender: boolean };

export default function DocumentChatBubble({ file, isSender }: Props) {
  return (
    <div
      className={`relative flex max-w-[343px] items-center gap-x-2 rounded-[16px] border p-2 ${
        isSender ? '!rounded-tr-none bg-[#A2E0FF]' : '!rounded-tl-none bg-white'
      }`}
    >
      <DocIcon className="h-20 w-20" />
      <a
        href={file}
        target="_blank"
        rel="noopener noreferrer"
        className={`ml-2 break-all text-[14px] font-medium hover:underline xs:text-sm`}
      >
        {ChatHelper.truncateAndKeepExtension(file, 50)}
      </a>
    </div>
  );
}
