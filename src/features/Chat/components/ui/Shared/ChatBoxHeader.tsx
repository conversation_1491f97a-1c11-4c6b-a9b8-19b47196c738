import { profilePlaceholder } from '../../../../../assets/images';
import { useHandleQueryParams } from '../../../../../hooks/useHandleQueryParams';
import { useGetChatRoom } from '../../../hooks/apiQueryHooks/chatQueryHooks';

export const ChatBoxHeader = () => {
  const { query } = useHandleQueryParams();
  const { data } = useGetChatRoom(
    {
      id: query.get('chat_room_id') || '',
    },
    { enabled: !!query.get('chat_room_id') },
  );
  return (
    <div className="flex h-[12%] items-center justify-between">
      <div className="flex items-center gap-4 border-b border-grayTwo bg-white p-6">
        {query.get('chat_room_id') ? (
          <>
            <div className="h-10 w-10">
              <img
                loading="lazy"
                src={data?.data?.conversationImage || profilePlaceholder}
                alt="profile"
                className={`h-full w-full ${!data?.data?.isGroupChat ? (data?.data?.online ? 'border border-green' : 'border border-gray-30') : ''}  rounded-full object-cover transition duration-300 ease-in-out ${'opacity-100'}`}
              />
            </div>
            <div className="flex flex-col gap-[5px]">
              <p className="text-[14px] font-semibold">
                {data?.data?.roomName}
              </p>
              {data?.data?.isGroupChat ? null : (
                <p className="max-w-[190px] truncate text-[10px] text-grayNine">
                  {data?.data && (data?.data?.online ? 'Online' : 'Offline')}
                </p>
              )}
            </div>
          </>
        ) : null}
      </div>
    </div>
  );
};
