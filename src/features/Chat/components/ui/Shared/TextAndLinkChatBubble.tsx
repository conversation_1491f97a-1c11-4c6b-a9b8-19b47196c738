import parse from 'html-react-parser';
import moment, { MomentInput } from 'moment';

import { PencilIcon } from '@/features/Profile/assets';

import { useCustomMedia } from '@/hooks/useCustomMedia';
import { Helper } from '@/utils/helpers';
import { useAppContext } from '@/context/event/AppEventContext';
import { useHandleQueryParams } from '@/hooks/useHandleQueryParams';
import { useState } from 'react';

type Props = {
  edited: boolean;
  message: string;
  isSender: boolean;
  messageId: string;
  dateSent: MomentInput;
  dateUpdated: MomentInput;
};

export default function TextAndLinkChatBubble({
  edited,
  message,
  isSender,
  messageId,
  dateSent,
  dateUpdated,
}: Props) {
  const { handleQuery } = useHandleQueryParams();
  const { setShowModalHandler } = useAppContext();
  const { screenSize } = useCustomMedia();
  const [allDescription, showAllDescription] = useState(false);
  const minutesPassed = Helper.getTimeDifferenceInMinutes(dateSent, undefined);

  return (
    <div
      className={`group relative w-full flex-1 rounded-[16px] px-2 py-4 text-[12px] font-semibold xs:px-5 [&_a]:w-full [&_a]:cursor-pointer ${isSender ? '[&_a]:text-black' : '[&_a]:text-primary'} [&_a]:underline ${
        isSender ? '!rounded-tr-none bg-[#A2E0FF]' : '!rounded-tl-none bg-white'
      }`}
    >
      <div className="text-[14px]">
        {allDescription ? (
          <>
            {parse(Helper.wrapLongWordsWithBreakAll(Helper.linkify(message)))}

            <span
              onClick={() => showAllDescription(false)}
              className="cursor-pointer text-[12px] font-semibold text-primary"
            >
              See less
            </span>
          </>
        ) : (
          <>
            {message && message?.length > 200 ? (
              <>
                {parse(
                  Helper.wrapLongWordsWithBreakAll(
                    Helper.linkify(message?.slice(0, 200)),
                  ),
                )}
                ...
                <span
                  onClick={() => showAllDescription(true)}
                  className="cursor-pointer text-[12px] font-semibold text-primary"
                >
                  See more
                </span>
              </>
            ) : (
              <>
                {parse(
                  Helper.wrapLongWordsWithBreakAll(Helper.linkify(message)),
                )}
              </>
            )}
          </>
        )}
      </div>
      {edited && (
        <p
          className={`mb-0 mt-5 text-[8px] font-bold ${isSender ? 'text-right' : 'text-left'}`}
        >
          Edited {moment(dateUpdated).format('h:mma')}
        </p>
      )}

      {isSender && minutesPassed < 5 && (
        <div
          className={`absolute flex cursor-pointer items-center transition-all duration-150 ${screenSize > 768 ? 'right-0 top-0 w-0 !rounded-bl-[16px] bg-primary p-2 opacity-0 backdrop-blur-md group-hover:w-8 group-hover:opacity-100' : '-right-4 bottom-0 w-4'}`}
          onClick={() => {
            setShowModalHandler('EditChatModal');
            handleQuery({ message_id: messageId, msg: message });
          }}
        >
          <PencilIcon className="sm:stroke-white" />
        </div>
      )}
    </div>
  );
}
