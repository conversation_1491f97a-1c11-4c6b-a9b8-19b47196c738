import { useNavigate } from 'react-router-dom';

import { useAppContext } from '../../../../../context/event/AppEventContext';
import { useCreateGroupChatMessage } from '../../../../../hooks/apiQueryHooks/chatQueryHooks';
import useHandleApiFeebackWithToast from '../../../../../hooks/useHandleApiFeebackWithToast';
import { DataResponse } from '../../../../../types';
import ContactThread from './ContactThread';
import { forwardRef, Ref } from 'react';

type Props = {
  name: string;
  image: string;
  roomRef: string;
};

export default forwardRef(function ContactThreadGroup(
  { image, name, roomRef }: Props,
  ref: Ref<HTMLDivElement>,
) {
  const { currentAccountType } = useAppContext();
  const navigate = useNavigate();
  const next = (res: DataResponse<any>) => {
    navigate(
      `/${currentAccountType}/chat?chat_room_id=${res?.data?._id}&c_c=${name}`,
    );
  };
  const { mutate: createGroupChatRoom } = useCreateGroupChatMessage({
    ...useHandleApiFeebackWithToast({ next, noSuccessToast: true }),
  });
  return (
    <ContactThread
      image={image}
      name={name}
      startChat={() => createGroupChatRoom(roomRef)}
      ref={ref}
    />
  );
});
