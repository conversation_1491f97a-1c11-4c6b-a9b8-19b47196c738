import { forwardRef, Ref } from 'react';
import { profilePlaceholder } from '../../../../../assets/images';

export default forwardRef(function ContactThread(
  {
    image,
    name,
    startChat,
  }: {
    name: string;
    image: string;
    startChat: () => void;
  },
  ref: Ref<HTMLDivElement>,
) {
  return (
    <div ref={ref} onClick={() => startChat()} className="group cursor-pointer">
      <div className="flex items-center gap-5 p-4 group-hover:bg-[#F7C19326]">
        <div className="relative h-12 w-12 flex-shrink-0">
          <img
            loading="lazy"
            src={image || profilePlaceholder}
            alt="profile"
            className={`h-full w-full rounded-full object-cover transition duration-300 ease-in-out ${'opacity-100'}`}
          />
        </div>
        <div className="flex w-full flex-col gap-[5px]">
          <div className="flex items-center justify-between">
            <p className="truncate text-[14px] font-semibold">
              {' '}
              {name.length > 20 ? `${name.slice(0, 20)}...` : name}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
});
