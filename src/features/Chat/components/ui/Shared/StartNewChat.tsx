import { ArrowRightTailless } from '../../../../../assets/icons';

import { ChatIcon } from '../../../assets/icons';

import ToolTip from '../../../../../components/ui/CommonWidget/ToolTip';

import { useChatContext } from '../../../context/chat/ChatContext';
import { useHandleQueryParams } from '../../../../../hooks/useHandleQueryParams';

export default function StartNewChat({
  hideTooltip = false,
}: {
  hideTooltip?: boolean;
}) {
  const { showNewChatOptionsHandler } = useChatContext();
  const { handleQuery, query } = useHandleQueryParams();
  return (
    <div className="flex h-[24px] items-center justify-between px-6">
      {query.get('new_chat') && (
        <ArrowRightTailless
          onClick={() => handleQuery({ new_chat: undefined })}
          className="h-[30px] w-[30px] rotate-180 cursor-pointer fill-black duration-300 ease-in-out hover:fill-primary"
        />
      )}
      <div
        id="start-chat"
        onClick={() => {
          showNewChatOptionsHandler(previous => !previous);
        }}
        className="group relative ml-auto flex cursor-pointer items-center gap-x-[5px] rounded border border-grayNine px-2 py-1.5 duration-200 ease-in-out hover:border-primary"
      >
        <ChatIcon className="ml-auto h-5 w-5 cursor-pointer fill-grayNine duration-200 ease-in-out group-hover:fill-primary" />
        <span className="mt-1 text-[14px] font-[500] leading-[22.4px] text-grayNine duration-200 ease-in-out group-hover:text-primary">
          Start a chat
        </span>
        {!hideTooltip && (
          <div className="absolute -right-[87px] -top-[34px] rounded-lg max-xs:hidden">
            <ToolTip text="Start new chat" />
          </div>
        )}
      </div>
    </div>
  );
}
