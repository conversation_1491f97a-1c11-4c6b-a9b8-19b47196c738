import { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';

import { CancelIcon, UploadIcon } from '@/assets/icons';
import { useChatContext } from '@/features/Chat/context/chat/ChatContext';

export default function ChatDropZone() {
  const { setFilesHandler, setShowDropzoneHandler } = useChatContext();
  const [error, setError] = useState('');
  const [invalidFiles, setInvalidFiles] = useState<File[]>([]);
  const onDrop = useCallback((acceptedFiles: File[]) => {
    const allowedFormats = new Set([
      'jpg',
      'jpeg',
      'png',
      'pdf',
      'gif',
      'doc',
      'docx',
      'mp4',
    ]);
    const invalidFiles = acceptedFiles.filter(file => {
      const fileExtension = file.name.split('.').pop()?.toLowerCase();
      return !allowedFormats.has(fileExtension!);
    });

    if (invalidFiles.length > 0) {
      setInvalidFiles(invalidFiles);
      setError('Invalid file included');
    } else {
      setFilesHandler(acceptedFiles);
    }
  }, []);
  const { isDragActive, getRootProps, getInputProps } = useDropzone({
    onDrop,
  });

  return (
    <>
      <label
        htmlFor="image"
        {...getRootProps()}
        className="relative m-8 mx-auto flex h-full w-[90%]
        cursor-pointer flex-col items-center justify-center rounded-xl border border-[red] bg-white px-20 py-12 max-md:px-5"
      >
        <div className="flex w-full flex-col items-center justify-center rounded-xl border-[1.2px] border-dashed border-primary bg-lightOrangeTwo p-4">
          <UploadIcon />
          <header className="mt-5 text-base font-semibold capitalize leading-6 text-black max-xs:text-center max-xs:text-[14px] xs:whitespace-nowrap">
            {isDragActive ? (
              <p>Drop the files here ...</p>
            ) : (
              <p>Drag and drop or browse to choose a file</p>
            )}
          </header>
          <div className="mt-3 text-[13px] leading-5 text-primary max-xs:text-center xs:whitespace-nowrap">
            File accepted - jpeg , jpg, png , gif, doc, docx or pdf,
          </div>
          <div className="mb-3 mt-1 text-[13px] leading-5 text-primary max-xs:text-center xs:whitespace-nowrap">
            File size - 5mb
          </div>
          {invalidFiles.length > 0 && (
            <small className="text-red-800">{error}</small>
          )}
          <ul className="mt-2">
            {invalidFiles.map((file, index) => (
              <li key={file.name + index} className="text-[12px]">
                {file.name}
              </li>
            ))}
          </ul>
          <input {...getInputProps()} />
        </div>
        <span
          onClick={e => {
            e.stopPropagation();
            setShowDropzoneHandler(false);
          }}
          className="group absolute right-4 top-3 cursor-pointer rounded-[50%] border-[0.3px] 
              bg-[#EAEBEC] p-[6px] duration-300 ease-in-out hover:scale-110 hover:border-primary"
        >
          <CancelIcon className="h-3 w-3 stroke-black duration-300 ease-in-out group-hover:stroke-primary sm:h-6 sm:w-6" />
        </span>
      </label>
    </>
  );
}
