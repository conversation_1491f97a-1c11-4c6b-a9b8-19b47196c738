import useHandleApiFeebackWithToast from '@/hooks/useHandleApiFeebackWithToast';
import { useChatContext } from '../../../context/chat/ChatContext';
import { useGetChatRooms } from '../../../hooks/apiQueryHooks/chatQueryHooks';
import ChatSearch from '../../forms/ChatSearch';
import { ChatThread } from './ChatThread';
import { GET_UNREAD_MESSAGES_QUERY } from '@/utils/queryKeys';
import { Spinner } from '@/components/ui/CommonWidget/Loader';

const commonCls = 'cursor-pointer py-[7px] px-3';
const activeCls = 'border-b-2 border-primary text-primary';

export const ChatThreadContainer = () => {
  const { messageType, setMessageTypeHandler } = useChatContext();
  const { data, isLoading } = useGetChatRooms({
    ...useHandleApiFeebackWithToast({
      queryKey: [GET_UNREAD_MESSAGES_QUERY],
      noSuccessToast: true,
    }),
  });
  const rooms = data?.data;
  return (
    <div className="flex h-[calc(100%-24px)] flex-col">
      <div className="p-6 pb-4">
        <ChatSearch />
      </div>
      <div className="flex border-b border-b-grayTwo px-6">
        <ul className="flex font-semibold">
          <li
            className={`${commonCls} ${messageType === 'All' ? activeCls : ''}`}
            onClick={() => setMessageTypeHandler('All')}
          >
            All
          </li>
          <li
            className={`${commonCls} ${
              messageType === 'Unread' ? activeCls : ''
            }`}
            onClick={() => setMessageTypeHandler('Unread')}
          >
            Unread
          </li>
        </ul>
      </div>
      <div className="flex flex-col overflow-auto">
        {messageType === 'All' && (
          <>
            {isLoading ? (
              <div className="flex h-[150px] items-center justify-center">
                <Spinner />
              </div>
            ) : (
              <>
                {(rooms || [])?.length > 0 ? (
                  rooms
                    ?.sort(
                      (a, b) =>
                        new Date(b.updatedAt).getTime() -
                        new Date(a.updatedAt).getTime(),
                    )
                    .map(room => <ChatThread key={room._id} {...room} />)
                ) : (
                  <p className="pt-20 text-center text-[12px]">No chat found</p>
                )}
              </>
            )}
          </>
        )}
        {messageType === 'Unread' && (
          <>
            {(rooms?.filter(room => room.unreadMessages > 0) || [])?.length >
            0 ? (
              rooms
                ?.filter(room => room.unreadMessages > 0)
                ?.map(room => <ChatThread key={room._id} {...room} />)
            ) : (
              <p className="pt-20 text-center text-[12px]">
                No unread chat found
              </p>
            )}
          </>
        )}
      </div>
    </div>
  );
};
