import { FileIcon, X } from 'lucide-react';

import { useChatContext } from '@/features/Chat/context/chat/ChatContext';
import { ChatHelper } from '@/features/Chat/utils/helper';

import ChatDropZone from './ChatDropZone';
import { useAppContext } from '@/context/event/AppEventContext';
import { useMemo } from 'react';

export const FileUpload = () => {
  const { files, setFilesHandler } = useChatContext();
  const { uploadProgress } = useAppContext();

  const file = files[0];

  const fileUrl = useMemo(
    () => (file ? URL.createObjectURL(file) : ''),
    [file],
  );
  const fileName = useMemo(() => (file ? file.name : ''), [file]);

  if (files?.length > 0 && ChatHelper.isImage(fileName)) {
    return (
      <>
        <div className="relative mx-auto h-auto max-h-[300px] w-[90%] max-w-[400px] p-[10px]">
          <img
            src={fileUrl}
            alt="Upload"
            className="h-full w-full rounded-md object-contain"
          />
          <button
            onClick={() => setFilesHandler([])}
            className="absolute -right-[4px] -top-[4px] rounded-full bg-rose-500 p-1 text-white shadow-sm"
            type="button"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
        <div className="w-full max-w-[300px] gap-1 bg-gray-400 bg-opacity-10">
          <div className="relative rounded-sm">
            <div
              style={{ width: `${uploadProgress}%` }}
              className={` ${
                uploadProgress === 100 ? 'bg-[green]' : 'bg-gray-400'
              } h-5 w-full rounded-sm duration-1000 ease-in-out`}
            />
            <div className="absolute inset-0 flex h-full items-center justify-center ">
              {uploadProgress > 0 && (
                <p
                  className={`${uploadProgress === 100 ? 'text-white' : ''} w-full text-center text-[10px]`}
                >
                  {uploadProgress}%
                </p>
              )}
            </div>
          </div>
        </div>
      </>
    );
  }

  if (
    files?.length > 0 &&
    (ChatHelper.isPdf(fileName) || ChatHelper.isDoc(fileName))
  ) {
    return (
      <>
        <div className="relative m-8 mx-auto mt-2 flex w-[90%] max-w-[400px] items-center rounded-md border p-2">
          <FileIcon className="h-10 w-10 fill-indigo-200 stroke-indigo-400" />
          <a
            href={fileUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="ml-2 truncate text-sm text-indigo-500 hover:underline"
          >
            {fileName}
          </a>
          <button
            onClick={() => setFilesHandler([])}
            className="absolute -right-2 -top-2 rounded-full bg-rose-500 p-1 text-white shadow-sm"
            type="button"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
        <div className="w-[300px]">
          <div className=" rounded-sm">
            <div
              style={{ width: `${uploadProgress}%` }}
              className={` ${
                uploadProgress === 100 ? 'bg-[green]' : 'bg-gray-400'
              } h-4 rounded-sm duration-1000 ease-in-out`}
            />
          </div>
        </div>
      </>
    );
  }

  if (files?.length > 0 && ChatHelper.isVideo(fileName)) {
    return (
      <>
        <div className="relative mx-auto h-auto max-h-[300px] w-[90%] max-w-[400px] p-[10px]">
          <video
            src={fileUrl}
            controls
            className="h-full w-full rounded-md object-contain"
          />
          <button
            onClick={() => setFilesHandler([])}
            className="absolute -right-[4px] -top-[4px] rounded-full bg-rose-500 p-1 text-white shadow-sm"
            type="button"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
        <div className="w-full max-w-[300px] gap-1 bg-gray-400 bg-opacity-10">
          <div className="relative rounded-sm">
            <div
              style={{ width: `${uploadProgress}%` }}
              className={` ${
                uploadProgress === 100 ? 'bg-[green]' : 'bg-gray-400'
              } h-5 w-full rounded-sm duration-1000 ease-in-out`}
            />
            <div className="absolute inset-0 flex h-full items-center justify-center ">
              {uploadProgress > 0 && (
                <p
                  className={`${uploadProgress === 100 ? 'text-white' : ''} w-full text-center text-[10px]`}
                >
                  {uploadProgress}%
                </p>
              )}
            </div>
          </div>
        </div>
      </>
    );
  }

  return <ChatDropZone />;
};
