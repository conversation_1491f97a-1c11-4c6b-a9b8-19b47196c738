import { useState } from 'react'

import { ArrowDownIcon } from '../../../../../assets/icons'
import { useHandleQueryParams } from '../../../../../hooks/useHandleQueryParams'
import { GroupIcon, OneOnOneIcon } from '../../../assets/icons'

export default function NewChatOptions() {
  const [showGroupOptions, setShowGroupOptions] = useState(false)

  const { handleQuery } = useHandleQueryParams()
  return (
    <div className="bg-white cursor-pointer shadow-lg text-black py-4 w-full min-h-[50px] h-auto duration-200 ease-out">
      <ul className="text-[12px]">
        <li
          onClick={() => setShowGroupOptions((previous) => !previous)}
          className="relative group py-1 border-b-black hover:text-primary hover:bg-gray-5 px-4 mb-2 flex justify-between items-center"
        >
          <span className="flex gap-x-2 items-center">
            <GroupIcon className="w-5 h-5 stroke-black group-hover:stroke-primary" />
            <span>Group</span>
            <ArrowDownIcon
              className={`${'w-[12px] h-[12px] group-hover:stroke-primary stroke-black'} ease-in-out duration-500 cursor-pointer`}
            />
          </span>
        </li>
        {showGroupOptions && (
          <ul>
            <li
              onClick={() => handleQuery({ new_chat: 'projects' })}
              className="group py-1 border-b-black hover:text-primary hover:bg-gray-5 px-4 mb-2"
            >
              <span>Projects</span>
            </li>
            <li
              onClick={() => handleQuery({ new_chat: 'teams' })}
              className="group py-1 border-b-black hover:text-primary hover:bg-gray-5 px-4 mb-2"
            >
              <span>Teams</span>
            </li>
          </ul>
        )}
        <li
          onClick={() => handleQuery({ new_chat: 'one-on-one' })}
          className="group py-1 border-b-black hover:text-primary hover:bg-gray-5 px-4 mb-2 flex gap-x-2 items-center"
        >
          <OneOnOneIcon className="w-5 h-5 stroke-black group-hover:stroke-primary" />
          <span>One-on-One</span>
        </li>
      </ul>
    </div>
  )
}
