import { useMemo } from 'react';
import { useNavigate } from 'react-router-dom';

import { profilePlaceholder } from '../../../../../assets/images';

import { useHandleQueryParams } from '@/hooks/useHandleQueryParams';
import { useAppContext } from '../../../../../context/event/AppEventContext';

import { chatRoom } from '../../../types';
import {
  GET_CHAT_ROOM_QUERY,
  GET_CHAT_ROOMS_QUERY,
  GET_UNREAD_MESSAGES_QUERY,
} from '../../../../../utils/queryKeys';
import { useConfirmReadStatus } from '@/features/Chat/hooks/apiQueryHooks/chatQueryHooks';
import useHandleApiFeebackWithToast from '@/hooks/useHandleApiFeebackWithToast';

export const ChatThread = ({
  conversationImage,
  roomName,
  _id,
  lastMessage,
  online,
  unreadMessages,
  isGroupChat,
  phoneNumberVerified,
}: chatRoom) => {
  const navigate = useNavigate();
  const { query } = useHandleQueryParams();
  const { currentAccountType } = useAppContext();

  const { mutate: confirmReadStatus } = useConfirmReadStatus({
    ...useHandleApiFeebackWithToast({
      queryKey: [
        GET_CHAT_ROOM_QUERY,
        GET_UNREAD_MESSAGES_QUERY,
        GET_CHAT_ROOMS_QUERY,
      ],
      noSuccessToast: true,
    }),
  });

  const selectedChatRoomId = query.get('chat_room_id');

  const startChat = () => {
    confirmReadStatus(_id);
    navigate(`/${currentAccountType}/chat?chat_room_id=${_id}`);
  };

  const isChatSelected = useMemo(
    () => selectedChatRoomId === _id,
    [selectedChatRoomId, _id],
  );

  return (
    <div onClick={() => startChat()} className="group cursor-pointer">
      <div
        className={`flex items-center gap-5 p-4 group-hover:bg-[#F7C1931A] ${isChatSelected ? 'bg-[#F7C1931A]' : ''}`}
      >
        <div className="relative h-12 w-12 flex-shrink-0">
          <img
            loading="lazy"
            src={conversationImage || profilePlaceholder}
            alt="profile"
            className={`h-full w-full rounded-full ${!isGroupChat ? (online ? 'border border-green' : 'border border-gray-30') : ''}  object-cover transition duration-300 ease-in-out ${'opacity-100'}`}
          />
          {!isGroupChat && (
            <div
              className={`${
                online ? 'bg-green' : 'bg-gray-30'
              }  absolute bottom-0 right-0 z-[2] h-4 w-4 rounded-full border-[3px] border-white`}
            />
          )}
        </div>
        <div className="flex w-full flex-col gap-[5px]">
          <div className="flex items-center justify-between">
            <p className="text-[14px] font-semibold ">
              {roomName.length > 20 ? `${roomName.slice(0, 20)}...` : roomName}
            </p>
            {!isGroupChat && !phoneNumberVerified ? (
              <div className="ml-1 whitespace-normal text-wrap break-words rounded bg-[#EAEAEA] px-2 py-[2px] text-[9px] font-semibold capitalize text-subText">
                {'Unverified user'}
              </div>
            ) : null}
          </div>
          <div className="flex items-center justify-between gap-2.5">
            <p className="max-w-[190px] truncate text-[12px] text-grayNine">
              {lastMessage?.content}
            </p>
            {unreadMessages > 0 && (
              <p className="flex h-5 w-5 items-center justify-center rounded-[50%] bg-alert p-1  text-[10px] font-bold text-white">
                <span>{unreadMessages}</span>
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
