// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`renders Cha<PERSON> correctly 1`] = `
<div>
  <div
    class="flex h-[80vh] overflow-y-hidden rounded-2xl bg-white pt-8"
  >
    <div
      class="block relative h-full flex-[30%] flex-shrink-0 border-r border-r-grayTwo pt-4 md:block"
    >
      <div
        class="flex h-[24px] items-center justify-between px-6"
      >
        <div
          class="group relative ml-auto flex cursor-pointer items-center gap-x-[5px] rounded border border-grayNine px-2 py-1.5 duration-200 ease-in-out hover:border-primary"
          id="start-chat"
        >
          <svg
            class="ml-auto h-5 w-5 cursor-pointer fill-grayNine duration-200 ease-in-out group-hover:fill-primary"
            fill="none"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M8.00001 10.9997C8.00001 10.7345 8.10537 10.4801 8.2929 10.2926C8.48044 10.105 8.7348 9.99967 9.00001 9.99967H15C15.2652 9.99967 15.5196 10.105 15.7071 10.2926C15.8947 10.4801 16 10.7345 16 10.9997C16 11.2649 15.8947 11.5192 15.7071 11.7068C15.5196 11.8943 15.2652 11.9997 15 11.9997H9.00001C8.7348 11.9997 8.48044 11.8943 8.2929 11.7068C8.10537 11.5192 8.00001 11.2649 8.00001 10.9997ZM9.00001 13.9997C8.7348 13.9997 8.48044 14.105 8.2929 14.2926C8.10537 14.4801 8.00001 14.7345 8.00001 14.9997C8.00001 15.2649 8.10537 15.5192 8.2929 15.7068C8.48044 15.8943 8.7348 15.9997 9.00001 15.9997H13C13.2652 15.9997 13.5196 15.8943 13.7071 15.7068C13.8947 15.5192 14 15.2649 14 14.9997C14 14.7345 13.8947 14.4801 13.7071 14.2926C13.5196 14.105 13.2652 13.9997 13 13.9997H9.00001ZM2.00001 11.9997C2.00051 9.80727 2.72149 7.67579 4.052 5.93326C5.3825 4.19074 7.24878 2.93375 9.36361 2.35574C11.4784 1.77774 13.7246 1.91076 15.7565 2.73432C17.7883 3.55789 19.4932 5.02636 20.6087 6.91374C21.7243 8.80111 22.1886 11.0028 21.9304 13.1799C21.6721 15.3571 20.7056 17.389 19.1794 18.963C17.6533 20.537 15.6521 21.5659 13.484 21.8912C11.3159 22.2166 9.10093 21.8204 7.18001 20.7637L3.29201 21.9477C3.11859 22.0005 2.93408 22.0052 2.7582 21.9612C2.58232 21.9173 2.42169 21.8264 2.29351 21.6982C2.16532 21.57 2.07439 21.4094 2.03044 21.2335C1.98649 21.0576 1.99118 20.8731 2.04401 20.6997L3.22801 16.8057C2.41992 15.3329 1.99749 13.6796 2.00001 11.9997ZM12 3.99967C10.5799 3.99955 9.18533 4.37746 7.95957 5.09458C6.73381 5.8117 5.72103 6.84219 5.02527 8.08019C4.3295 9.3182 3.97582 10.7191 4.00055 12.139C4.02529 13.5589 4.42755 14.9466 5.16601 16.1597C5.23932 16.2802 5.2863 16.4148 5.30388 16.5548C5.32146 16.6947 5.30924 16.8368 5.26801 16.9717L4.50201 19.4897L7.01601 18.7237C7.15133 18.6824 7.29385 18.6704 7.43417 18.6883C7.57449 18.7063 7.70941 18.7537 7.83001 18.8277C8.88437 19.4714 10.0735 19.8622 11.3041 19.9695C12.5348 20.0767 13.7736 19.8975 14.9234 19.4459C16.0733 18.9943 17.1029 18.2825 17.9316 17.3664C18.7603 16.4503 19.3656 15.3547 19.7 14.1655C20.0344 12.9762 20.0889 11.7258 19.8592 10.512C19.6295 9.29818 19.1218 8.15408 18.3759 7.16935C17.63 6.18461 16.6662 5.38598 15.56 4.83606C14.4539 4.28614 13.2353 3.99986 12 3.99967Z"
              fill=""
              fill-opacity="0.82"
            />
          </svg>
          <span
            class="mt-1 text-[14px] font-[500] leading-[22.4px] text-grayNine duration-200 ease-in-out group-hover:text-primary"
          >
            Start a chat
          </span>
          <div
            class="absolute -right-[87px] -top-[34px] rounded-lg max-xs:hidden"
          >
            <div
              class="rounded-lg bg-primary opacity-0 h-[30px] 
    duration-300 ease-out relative group-hover:opacity-100 py-2 px-3"
            >
              <p
                class="text-white text-center text-xs font-medium 
        leading-4 tracking-normal whitespace-nowrap
        "
              >
                Start new chat
              </p>
              <span
                class="w-[8px] h-[8px] rotate-45 absolute bottom-0 left-[16px] 
         bg-primary translate-y-[50%]"
              />
            </div>
          </div>
        </div>
      </div>
      <div
        class="flex h-[calc(100%-24px)] flex-col"
      >
        <div
          class="p-6 pb-4"
        >
          <div
            class="relative h-[38px] w-full"
          >
            <input
              class="h-full w-full flex-1 rounded-[10px] border-[1px] border-graySeven bg-transparent
   pl-5 pr-[40px] text-blackFive outline-none transition duration-500 ease-in-out"
              placeholder="Search message"
              type="input"
            />
            <svg
              class="absolute right-[20px] top-[50%] h-[20px] w-[20px] -translate-y-[50%]"
              fill="none"
              height="24"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10.875 18.75C15.2242 18.75 18.75 15.2242 18.75 10.875C18.75 6.52576 15.2242 3 10.875 3C6.52576 3 3 6.52576 3 10.875C3 15.2242 6.52576 18.75 10.875 18.75Z"
                stroke="#0F0006"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="1.5"
              />
              <path
                d="M16.4414 16.4434L20.9977 20.9997"
                stroke="#0F0006"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="1.5"
              />
            </svg>
          </div>
        </div>
        <div
          class="flex border-b border-b-grayTwo px-6"
        >
          <ul
            class="flex font-semibold"
          >
            <li
              class="cursor-pointer py-[7px] px-3 "
            >
              All
            </li>
            <li
              class="cursor-pointer py-[7px] px-3 "
            >
              Unread
            </li>
          </ul>
        </div>
        <div
          class="flex flex-col overflow-auto"
        />
      </div>
    </div>
    <div
      class="hidden h-full w-full max-w-[70%] flex-col max-md:max-w-full md:flex md:w-auto md:flex-[70%]"
    >
      <div
        class="block px-6 md:hidden"
      >
        <svg
          class="cursor-pointer"
          fill="none"
          height="24"
          viewBox="0 0 24 24"
          width="24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M19 12H5"
            stroke="#FF3E00"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
          />
          <path
            d="M12 19L5 12L12 5"
            stroke="#FF3E00"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
          />
        </svg>
      </div>
      <div
        class="flex h-[10%] items-center justify-between"
      >
        <div
          class="flex items-center gap-4 border-b border-grayTwo bg-white p-6"
        />
      </div>
      <div
        class="flex h-[90%] flex-col bg-cover"
        style="background-image: url(/src/features/Chat/assets/images/chat-bg.png);"
      >
        <div
          class="flex h-auto flex-1 flex-grow  flex-col items-center justify-center gap-4 overflow-auto bg-[#F7C1931A] px-9 py-6"
        >
          <p
            class="text-xs text-zinc-500 dark:text-zinc-400"
          >
            No conversation yet
          </p>
        </div>
        <form
          class="mx-auto my-2 h-[50px] w-full max-w-[95%] rounded-[24px]"
        >
          <div
            class="flex h-full items-center gap-2.5 rounded-[24px] border-t border-t-grayTwo bg-white px-6 py-4"
          >
            <input
              class="text-primary h-full w-full flex-1
        bg-transparent py-2 text-[14px] outline-none transition duration-500 ease-in-out"
              disabled=""
              name="content"
              placeholder="Type to add your message"
              type=""
            />
            <button
              disabled=""
              type="submit"
            />
            <svg
              class="cursor-pointer"
              fill="none"
              height="25"
              viewBox="0 0 25 25"
              width="25"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g
                clip-path="url(#clip0_13156_106018)"
              >
                <path
                  d="M21.6249 11.2013L12.4349 20.3913C11.309 21.5171 9.78204 22.1496 8.18986 22.1496C6.59768 22.1496 5.0707 21.5171 3.94486 20.3913C2.81902 19.2654 2.18652 17.7385 2.18652 16.1463C2.18652 14.5541 2.81902 13.0271 3.94486 11.9013L13.1349 2.71129C13.8854 1.96072 14.9034 1.53906 15.9649 1.53906C17.0263 1.53906 18.0443 1.96072 18.7949 2.71129C19.5454 3.46185 19.9671 4.47983 19.9671 5.54129C19.9671 6.60274 19.5454 7.62072 18.7949 8.37129L9.59486 17.5613C9.21958 17.9366 8.71059 18.1474 8.17986 18.1474C7.64913 18.1474 7.14014 17.9366 6.76486 17.5613C6.38958 17.186 6.17875 16.677 6.17875 16.1463C6.17875 15.6156 6.38958 15.1066 6.76486 14.7313L15.2549 6.25129"
                  stroke="#8890A1"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                />
              </g>
              <defs>
                <clippath
                  id="clip0_13156_106018"
                >
                  <rect
                    fill="white"
                    height="24"
                    transform="translate(0.185059 0.148438)"
                    width="24"
                  />
                </clippath>
              </defs>
            </svg>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
`;
