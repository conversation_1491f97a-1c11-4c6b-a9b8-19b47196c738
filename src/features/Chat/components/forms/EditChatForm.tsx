import { useFormDataAndApiMutateHandler } from '@/hooks/useFormDataAndApiMutateHandler';
import { IAddMessage } from '../../types';
import { useHandleQueryParams } from '@/hooks/useHandleQueryParams';
import { addMessageSchema } from '../../lib/yup/validation';
import { useUpdateMessage } from '../../hooks/apiQueryHooks/chatQueryHooks';
import { useAppContext } from '@/context/event/AppEventContext';
import { SubmitHandler } from 'react-hook-form';
import Form from '@/components/forms/Form';
import { CloseIcon } from '@/assets/icons';
import { SendIcon } from '../../assets/icons';
import { Helper } from '@/utils/helpers';
import bgImage from '../../assets/images/chat-bg.png';
import FormTextArea from '@/components/forms/FormTextArea';
import { cn } from '@/lib/twMerge/cn';
import { useRef } from 'react';
import invariant from 'tiny-invariant';

export default function EditChatForm() {
  const buttonRef = useRef<HTMLButtonElement>(null);
  const next = () => {
    setShowModalHandler('');
  };
  const { setShowModalHandler } = useAppContext();
  const { query } = useHandleQueryParams();
  const chatRoomId = query.get('chat_room_id');
  const messageId = query.get('message_id');
  const msg = query.get('msg');
  const { register, isLoading, handleSubmit, mutate, watch, getValues } =
    useFormDataAndApiMutateHandler<IAddMessage, any>(
      addMessageSchema,
      useUpdateMessage,
      {
        next,
        noSuccessToast: true,
        queryKey: [`chat:${chatRoomId}`],
        defaultValues: {
          content: msg,
        },
      },
    );

  const onSubmit: SubmitHandler<IAddMessage> = data => {
    const newData = {
      ...data,
      chatRoomId,
      chatMessageId: messageId,
    };
    mutate(newData);
  };
  return (
    <div
      style={{ backgroundImage: `url(${bgImage})` }}
      className="-mt-8 w-full bg-orange-100 bg-cover pb-4"
    >
      <div className="flex w-full items-center justify-between gap-3 bg-peach-5 p-4 px-5 sm:px-8">
        <h4 className="text-[16px] font-semibold text-black">Edit Chat</h4>
        <div className="flex items-center gap-5 sm:gap-20">
          <CloseIcon
            className="h-6 w-6 cursor-pointer"
            onClick={e => {
              e.stopPropagation();
              setShowModalHandler('');
            }}
          />
        </div>
      </div>
      <Form
        className="mx-auto mt-16 w-full max-w-[95%] rounded-[24px]"
        onSubmit={handleSubmit(onSubmit)}
      >
        <div className="flex h-full items-center gap-2.5 rounded-[24px] border-t border-t-grayTwo bg-white px-6 py-1">
          <FormTextArea
            onKeyDown={event => {
              if (event.key === 'Enter') {
                event.preventDefault();
                invariant(buttonRef?.current, 'expected button ref');
                buttonRef?.current?.click();
              }
              if (event.key === 'Escape') {
                setShowModalHandler('');
              }
            }}
            rows={3}
            placeholder="Edit message..."
            registerHanlder={() => register('content')}
            className={cn(`${
              getValues('content') ===
              Helper.linkify(getValues('content') ?? '')
                ? 'text-blackFive'
                : 'text-primary'
            } input !h-auto w-full
      flex-1 border-none py-2 text-[14px] focus:border-none`)}
          />
          {watch('content') && (
            <button ref={buttonRef} disabled={isLoading} type={'submit'}>
              <SendIcon className="h-6 w-6 cursor-pointer" />
            </button>
          )}
        </div>
      </Form>
    </div>
  );
}
