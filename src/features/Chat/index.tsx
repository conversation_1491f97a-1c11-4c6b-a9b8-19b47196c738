import DynamicRoutingShell from '../../components/ui/template/DynamicRoutingShell';
import { SocketProvider } from '../../context/socket/socketContext';
import EditChatModal from './components/ui/modals/EditChatModal';
import ChatSection from './components/ui/Shared';
import { ChatProvider } from './context/chat/ChatContext';

export const Chat = () => {
  return (
    <SocketProvider>
      <ChatProvider>
        <DynamicRoutingShell shared={<ChatSection />} />
        <EditChatModal />
      </ChatProvider>
    </SocketProvider>
  );
};
