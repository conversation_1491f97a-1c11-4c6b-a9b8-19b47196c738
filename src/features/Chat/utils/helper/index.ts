import moment from 'moment';

import { ExtractData, ServerChatMessages } from '../../../../types';
import { Helper } from '../../../../utils/helpers';

export class ChatHelper extends Helper {
  public static groupChatMessagesByDay(
    messages: ExtractData<ServerChatMessages>,
  ): Record<string, ExtractData<ServerChatMessages>> {
    const groupedMessages: Record<string, ExtractData<ServerChatMessages>> = {};

    messages.forEach(message => {
      const date = moment(message.createdAt);

      const dateString = moment().isSame(date, 'year')
        ? date.format('ddd, MMM D')
        : date.format('D MMM YYYY');

      if (!groupedMessages[dateString]) {
        groupedMessages[dateString] = [];
      }
      groupedMessages[dateString].push(message);
    });

    return groupedMessages;
  }

  public static isImage(file: string): boolean {
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif'];
    return imageExtensions.includes(this.getFileType(file));
  }

  public static isVideo(fileName: string): boolean {
    const videoExtensions = ['mp4', 'webm', 'ogg'];
    return videoExtensions.includes(this.getFileType(fileName));
  }
  public static isPdf(file: string): boolean {
    return this.getFileType(file) === 'pdf';
  }
  public static isDoc(file: string): boolean {
    const documentExtensions = ['doc', 'docx'];
    return documentExtensions.includes(this.getFileType(file));
  }
  public static truncateAndKeepExtension(
    file: string,
    maxLength: number,
  ): string {
    if (file.length <= maxLength) {
      return file;
    }
    const extension = this.getFileType(file);
    const truncatedName = file.slice(
      0,
      Math.max(0, maxLength - extension.length - 1),
    );
    return truncatedName + '.' + extension;
  }
}
