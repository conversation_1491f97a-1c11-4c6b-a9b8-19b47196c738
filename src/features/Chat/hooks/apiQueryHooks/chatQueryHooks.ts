import { useInfiniteQuery, useMutation, useQuery } from '@tanstack/react-query';

import { useSocketContext } from '../../../../context/socket/socketContext';
import {
  GET_CHAT_ROOM_QUERY,
  GET_CHAT_ROOMS_QUERY,
  GET_COMMON_TEAM_MEMBERS_INFINITE_QUERY,
  GET_COMMON_TEAM_MEMBERS_QUERY,
  GET_UNREAD_MESSAGES_QUERY,
  UPDATE_MESSAGE_QUERY,
} from '../../../../utils/queryKeys';
import {
  useAddChatMessagesApi,
  useConfirmReadStatusApi,
  useGetChatMessagesApi,
  useGetChatRoomApi,
  useGetChatRoomsApi,
  useGetCommonTeamMembersApi,
  useGetCommonTeamMembersInfiniteQueryApi,
  useGetUnReadMessagesApi,
  useUpdateMessageApi,
} from '../../services/chatApiRequests';

interface ChatQueryProps {
  queryKey: string;
  chatRoomId: string;
}

export const useGetChatMessages = (
  { queryKey, chatRoomId }: ChatQueryProps,
  options = {},
) => {
  const getChatMessages = useGetChatMessagesApi();
  const { isConnected } = useSocketContext();
  return useInfiniteQuery({
    queryKey: [queryKey, chatRoomId],
    queryFn: getChatMessages,
    getNextPageParam: (lastPage, allPages) => {
      const nextPage = lastPage?.data?.length ? allPages.length + 1 : undefined;
      return nextPage;
    },
    refetchInterval: isConnected ? false : 1000,
    ...options,
  });
};

export const useGetChatRooms = (options = {}) => {
  const getChatRooms = useGetChatRoomsApi();
  return useQuery([GET_CHAT_ROOMS_QUERY], () => getChatRooms(), {
    ...options,
  });
};
export const useGetChatRoom = (params: { id: string }, options = {}) => {
  const getChatRoom = useGetChatRoomApi();
  return useQuery(
    [GET_CHAT_ROOM_QUERY, params.id],
    () => getChatRoom(params.id),
    {
      ...options,
    },
  );
};
export const useGetUnReadMessages = (options = {}) => {
  const getUnReadMessages = useGetUnReadMessagesApi();
  return useQuery([GET_UNREAD_MESSAGES_QUERY], () => getUnReadMessages(), {
    ...options,
  });
};
export const useGetCommonTeamMembers = (options = {}) => {
  const getCommonTeamMembers = useGetCommonTeamMembersApi();
  return useQuery(
    [GET_COMMON_TEAM_MEMBERS_QUERY],
    () => getCommonTeamMembers(),
    {
      ...options,
    },
  );
};

export const useGetCommonTeamMembersInfiniteQuery = (options = {}) => {
  const getCommonTeamMembers = useGetCommonTeamMembersInfiniteQueryApi();
  return useInfiniteQuery({
    queryKey: [GET_COMMON_TEAM_MEMBERS_INFINITE_QUERY],
    queryFn: getCommonTeamMembers,
    getNextPageParam: (lastPage, allPages) => {
      const nextPage = lastPage?.data?.length ? allPages.length + 1 : undefined;
      return nextPage;
    },
    ...options,
  });
};

export const useAddChatMessage = (options = {}) => {
  const addChatMessage = useAddChatMessagesApi();
  return useMutation(addChatMessage, {
    mutationKey: ['ADD_CHAT_MESSAGE_QUERY'],
    ...options,
  });
};
export const useConfirmReadStatus = (options = {}) => {
  const confirmReadStatus = useConfirmReadStatusApi();
  return useMutation(confirmReadStatus, {
    mutationKey: ['CONFIRM_STATUS_QUERY'],
    ...options,
  });
};

export const useUpdateMessage = (options = {}) => {
  const updateMessage = useUpdateMessageApi();
  return useMutation(updateMessage, {
    mutationKey: [UPDATE_MESSAGE_QUERY],
    ...options,
  });
};
