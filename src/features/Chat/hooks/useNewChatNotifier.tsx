import { useNavigate } from 'react-router-dom';
import { useCallback, useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { profilePlaceholder } from '@/assets/images';
import { useAppContext } from '@/context/event/AppEventContext';
import { useCustomToast } from '@/hooks/useToast';
import { useHandleQueryParams } from '@/hooks/useHandleQueryParams';
import {
  ExtractData,
  ArrayIndexElement,
  ServerChatMessages,
} from '../../../types';
import { NOTIFICATION_STORAGE_KEY, chatEvents } from '../data/constant';
import { useSocketContext } from '../../../context/socket/socketContext';

export const useNewChatNotifier = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { socket } = useSocketContext();
  const { infoToast } = useCustomToast();
  const { query } = useHandleQueryParams();
  const { currentAccountType } = useAppContext();

  const chatRoomId = query.get('chat_room_id');

  const goToChatUrl = useCallback(
    (chatRoom: string) =>
      navigate(`/${currentAccountType}/chat?chat_room_id=${chatRoom}`),
    [currentAccountType, navigate],
  );

  useEffect(() => {
    if (Notification.permission === 'default') {
      Notification.requestPermission();
    }
  }, []);

  useEffect(() => {
    if (!socket) {
      return;
    }

    const notifyOncePerMessage = (
      message: ArrayIndexElement<ExtractData<ServerChatMessages>>,
    ) => {
      const messageId = `${message.chatRoom}:${message.createdAt}`;
      const storedNotifications = JSON.parse(
        localStorage.getItem(NOTIFICATION_STORAGE_KEY) || '[]',
      );

      if (storedNotifications.includes(messageId)) return;

      const updatedNotifications = [...storedNotifications, messageId];
      localStorage.setItem(
        NOTIFICATION_STORAGE_KEY,
        JSON.stringify(updatedNotifications),
      );

      if (document.visibilityState === 'hidden') {
        const createNotification = () => {
          const notification = new Notification('New Message', {
            body: `${message.senderName}: ${message.content}`,
            icon: message.senderImage || profilePlaceholder,
          });
          notification.onclick = () =>
            window.open(
              `/${currentAccountType}/chat?chat_room_id=${message.chatRoom}`,
              '_blank',
            );
        };

        if (Notification.permission === 'granted') {
          createNotification();
        } else if (Notification.permission === 'default') {
          Notification.requestPermission().then(permission => {
            if (permission === 'granted') createNotification();
          });
        }
      }
    };

    socket.on(
      chatEvents.NEW_CHAT_MESSAGE,
      (message: ArrayIndexElement<ExtractData<ServerChatMessages>>) => {
        notifyOncePerMessage(message);

        if (message?.chatRoom?._id !== chatRoomId) {
          queryClient.invalidateQueries([`chat:${message.chatRoom}`]);
          const messageContent =
            message.content.length > 80
              ? `${message.content.slice(0, 80)}...`
              : message.content;

          infoToast(
            <div
              onClick={() => goToChatUrl(message.chatRoom?._id)}
              className="flex flex-col gap-0.5"
            >
              <p className="mb-0 text-sm font-semibold">
                Message from {message.senderName}
              </p>
              <span className="text-xs">{messageContent}</span>
            </div>,
          );
        }
      },
    );

    return () => {
      socket.off(chatEvents.NEW_CHAT_MESSAGE);
    };
  }, [socket, queryClient, goToChatUrl, chatRoomId]);

  return null;
};
