import { useQueryClient } from '@tanstack/react-query';
import { useEffect } from 'react';

import { useSocketContext } from '../../../context/socket/socketContext';
import {
  ArrayIndexElement,
  ChatEventsValues,
  ExtractData,
  ServerChatMessages,
} from '../../../types';

export const useChatQuery = ({ key }: { key: ChatEventsValues }) => {
  const { socket } = useSocketContext();
  const queryClient = useQueryClient();
  useEffect(() => {
    if (!socket) {
      return;
    }
    socket.on(
      key,
      (message: ArrayIndexElement<ExtractData<ServerChatMessages>>) => {
        queryClient.setQueryData(
          [`chat:${message?.chatRoom?._id}`, message?.chatRoom?._id],
          (oldData: any) => {
            if (!oldData || !oldData.pages || oldData.pages.length === 0) {
              return {
                pages: [
                  {
                    ...oldData?.pages[0],
                    data: [message],
                  },
                ],
              };
            }
            const newData = [...oldData.pages];
            newData[0] = {
              ...newData[0],
              data: [message, ...newData[0].data],
            };
            return {
              ...oldData,
              pages: newData,
            };
          },
        );
      },
    );
    return () => {
      socket.off(key);
    };
  }, [queryClient, key, socket]);
};
