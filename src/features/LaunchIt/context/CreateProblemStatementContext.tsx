import { createContext, useContext, useState, ReactNode } from 'react';
import { CreateProblemStatementFormData } from '../types';

interface FileMetadata {
  name: string;
  size: number;
  type: string;
}

interface CreateProblemStatementContextType {
  formData: Partial<CreateProblemStatementFormData>;
  fileMetadata: FileMetadata | null;
  setFormData: (data: Partial<CreateProblemStatementFormData>) => void;
  updateFormData: (data: Partial<CreateProblemStatementFormData>) => void;
  setFileMetadata: (metadata: FileMetadata | null) => void;
  resetFormData: () => void;
}

const CreateProblemStatementContext = createContext<
  CreateProblemStatementContextType | undefined
>(undefined);

export const useCreateProblemStatementContext = () => {
  const context = useContext(CreateProblemStatementContext);
  if (!context) {
    throw new Error(
      'useCreateProblemStatementContext must be used within a CreateProblemStatementProvider',
    );
  }
  return context;
};

interface CreateProblemStatementProviderProps {
  children: ReactNode;
}

export const CreateProblemStatementProvider = ({
  children,
}: CreateProblemStatementProviderProps) => {
  const [formData, setFormDataState] = useState<
    Partial<CreateProblemStatementFormData>
  >({});
  const [fileMetadata, setFileMetadataState] = useState<FileMetadata | null>(
    null,
  );

  const setFormData = (data: Partial<CreateProblemStatementFormData>) => {
    setFormDataState(data);
  };

  const updateFormData = (data: Partial<CreateProblemStatementFormData>) => {
    setFormDataState(prev => ({ ...prev, ...data }));
  };

  const setFileMetadata = (metadata: FileMetadata | null) => {
    setFileMetadataState(metadata);
  };

  const resetFormData = () => {
    setFormDataState({});
    setFileMetadataState(null);
  };

  return (
    <CreateProblemStatementContext.Provider
      value={{
        formData,
        fileMetadata,
        setFormData,
        updateFormData,
        setFileMetadata,
        resetFormData,
      }}
    >
      {children}
    </CreateProblemStatementContext.Provider>
  );
};
