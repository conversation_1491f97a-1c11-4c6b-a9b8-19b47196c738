import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft, PencilLine, Plus, ChevronDown, X } from 'lucide-react';
import moment from 'moment';
import Button from '@/components/ui/ButtonComponent';
import { ProposedProject } from '../../../services/launchItApiRequests';
import { useCustomToast } from '@/hooks/useToast';
import { useAppContext } from '@/context/event/AppEventContext';
import {
  useGenerateAutomationSummary,
  useUpdateProposedGoal,
  useUpdateProposedProject,
} from '../../../hooks/apiQueryHooks/launchItQueryHooks';
import solutionThumbnail from '../../../assets/images/solutionThumbnail.png';
import { useState, useEffect, useRef } from 'react';
import { Spinner } from '@/components/ui/CommonWidget/Loader';
import { useGetCountries } from '@/hooks/apiQueryHooks/userQueryHooks';
import { Helper } from '@/utils/helpers';
import { countryFlags } from '@/utils/helpers/country-flags-data';
import { sdgGoalsCategories } from '../../../data/constants';
import { projectLevels } from '@/features/Categories/data/data';

export default function ProposedProjectDetails() {
  const location = useLocation();
  const navigate = useNavigate();
  const { projectRef } = useParams();
  const { currentAccountType } = useAppContext();
  const { successToast, errorToast } = useCustomToast();

  // Edit state management
  const [isEditingGoal, setIsEditingGoal] = useState(false);
  const [isEditingProject, setIsEditingProject] = useState(false);
  const [editedGoalDescription, setEditedGoalDescription] = useState('');
  const [editedKeyResults, setEditedKeyResults] = useState<
    Array<{
      referenceId: string;
      keyResultDescription: string;
    }>
  >([]);
  const [newKeyResult, setNewKeyResult] = useState('');
  const [isAddingKeyResult, setIsAddingKeyResult] = useState(false);

  // Project edit state
  const [editedProjectName, setEditedProjectName] = useState('');
  const [editedProjectDescription, setEditedProjectDescription] = useState('');
  const [selectedCountries, setSelectedCountries] = useState<
    Array<{ code: string; name: string }>
  >([]);
  const [selectedGoalsCategories, setSelectedGoalsCategories] = useState<
    Array<{ categoryRef: string; categoryName: string }>
  >([]);
  const [selectedProjectLevel, setSelectedProjectLevel] = useState('');
  const [selectedStartDate, setSelectedStartDate] = useState('');

  // Dropdown states
  const [showCountryDropdown, setShowCountryDropdown] = useState(false);
  const [showGoalsCategoriesDropdown, setShowGoalsCategoriesDropdown] =
    useState(false);
  const [showProjectLevelDropdown, setShowProjectLevelDropdown] =
    useState(false);
  const [countryError, setCountryError] = useState('');

  // Refs for click outside handling
  const dropdownRef = useRef<HTMLDivElement>(null);
  const goalsCategoriesDropdownRef = useRef<HTMLDivElement>(null);
  const projectLevelDropdownRef = useRef<HTMLDivElement>(null);

  // Local state for current project to persist updates
  const [localCurrentProject, setLocalCurrentProject] =
    useState<ProposedProject | null>(null);

  // Initialization flags to prevent infinite loops
  const [isInitialized, setIsInitialized] = useState(false);

  // Get the proposed projects, current project, and selected solution from location state
  const { proposedProjects, selectedSolution } = location.state || {};
  const originalCurrentProject = proposedProjects?.find(
    (project: ProposedProject) => project.referenceId === projectRef,
  );

  // Use local state if available, otherwise use original
  const currentProject = localCurrentProject || originalCurrentProject;

  // Function to update current project data
  const updateCurrentProjectData = (updates: Partial<ProposedProject>) => {
    if (currentProject) {
      setLocalCurrentProject({
        ...currentProject,
        ...updates,
      });
    }
  };

  // API data fetching
  const { data: countries } = useGetCountries();
  const countryOptions = Helper.createCountriesOptionsArray(
    countries?.data || { countries: [] },
  );

  // API mutations
  const { mutate: updateProposedGoal, isPending: isUpdatingGoal } =
    useUpdateProposedGoal({
      onSuccess: () => {
        successToast('Goal and key results updated successfully!');
        updateCurrentProjectData({
          goals: [
            {
              ...currentProject!.goals[0],
              description: editedGoalDescription,
              goalsKeyResults: editedKeyResults,
            },
          ],
        });
        setIsEditingGoal(false);
      },
      onError: (error: any) => {
        errorToast(
          error?.response?.data?.message ||
            'Failed to update goal and key results. Please try again.',
        );
      },
    });

  const { mutate: updateProposedProject, isPending: isUpdatingProject } =
    useUpdateProposedProject({
      onSuccess: () => {
        successToast('Project updated successfully!');
        updateCurrentProjectData({
          name: editedProjectName,
          description: editedProjectDescription,
          focusCountries: selectedCountries.map(country => country.name),
          focusGoalRefs: selectedGoalsCategories.map(
            category => category.categoryRef,
          ),
          minimumProjectLevel: selectedProjectLevel,
          ...(selectedStartDate && { startDate: selectedStartDate }),
        });
        setIsEditingProject(false);
      },
      onError: (error: any) => {
        errorToast(
          error?.response?.data?.message ||
            'Failed to update project. Please try again.',
        );
      },
    });

  const handleBack = () => {
    navigate(-1);
  };

  // Automation Summary API integration
  const {
    mutate: generateAutomationSummary,
    isPending: isGeneratingAutomationSummary,
  } = useGenerateAutomationSummary({
    onSuccess: (response: any) => {
      successToast('Automation summary generated successfully!');
      navigate(`/${currentAccountType}/launch/automation-summary`, {
        state: {
          automationData: response.data,
          selectedStartDate: selectedStartDate || currentProject?.startDate,
          selectedProjectLevel,
          // Pass the updated project data and navigation state for proper back navigation
          proposedProjects: proposedProjects?.map((project: ProposedProject) =>
            project.referenceId === projectRef
              ? localCurrentProject || project
              : project,
          ),
          selectedSolution,
          projectRef,
        },
      });
    },
    onError: (error: any) => {
      errorToast(
        error?.response?.data?.message ||
          'Failed to generate automation summary. Please try again.',
      );
    },
  });

  // Validate countries selection
  useEffect(() => {
    if (selectedCountries.length === 0) {
      setCountryError('At least one country must be selected');
    } else {
      setCountryError('');
    }
  }, [selectedCountries]);

  // Initialize edit state when original project changes
  useEffect(() => {
    if (originalCurrentProject && !isInitialized) {
      // Initialize local state if not already set
      if (!localCurrentProject) {
        setLocalCurrentProject(originalCurrentProject);
      }

      // Use current project data (which includes any updates) for initialization
      const projectDataForInit = localCurrentProject || originalCurrentProject;

      // Goal edit state
      setEditedGoalDescription(projectDataForInit?.goals[0]?.description || '');
      setEditedKeyResults(projectDataForInit?.goals[0]?.goalsKeyResults || []);

      // Project edit state
      setEditedProjectName(projectDataForInit?.name || '');
      setEditedProjectDescription(projectDataForInit?.description || '');

      // Initialize countries from the project data (updated or original)
      if (
        projectDataForInit?.focusCountries &&
        projectDataForInit.focusCountries.length > 0
      ) {
        const initialCountries = projectDataForInit.focusCountries
          .map((countryName: string) => {
            const countryOption = countryOptions.find(
              option => option.label === countryName,
            );
            return {
              code: countryOption?.value || '',
              name: countryName,
            };
          })
          .filter((country: { code: string; name: string }) => country.code);
        setSelectedCountries(initialCountries);
      }

      // Initialize goals categories from the project data (updated or original)
      if (
        projectDataForInit?.focusGoalRefs &&
        projectDataForInit.focusGoalRefs.length > 0
      ) {
        const initialGoalsCategories = projectDataForInit.focusGoalRefs
          .map((goalRef: string) => {
            for (const category of sdgGoalsCategories) {
              if (category.categoryRef === goalRef) {
                return {
                  categoryRef: goalRef,
                  categoryName: category.categoryName,
                };
              }
            }
            return null;
          })
          .filter(Boolean) as Array<{
          categoryRef: string;
          categoryName: string;
        }>;
        setSelectedGoalsCategories(initialGoalsCategories);
      }

      setSelectedProjectLevel(projectDataForInit?.minimumProjectLevel || '');

      // Initialize start date from project data if available
      if (projectDataForInit?.startDate) {
        setSelectedStartDate(projectDataForInit.startDate);
      } else {
        setSelectedStartDate('');
      }

      setIsInitialized(true);
    }
  }, [
    originalCurrentProject,
    localCurrentProject,
    isInitialized,
    countryOptions,
  ]);

  // Handler functions for goal editing
  const handleEditGoal = () => {
    setIsEditingGoal(true);
  };

  const handleSaveGoal = () => {
    if (!currentProject) return;

    updateProposedGoal({
      proposedGoalRef: currentProject.goals[0]?.proposedGoalRef,
      description: editedGoalDescription,
      goalsKeyResults: editedKeyResults,
    });
  };

  const handleAddKeyResult = () => {
    if (!newKeyResult.trim()) return;

    const newKeyResultObj = {
      referenceId: Helper.getUniqueReference('KR_'),
      keyResultDescription: newKeyResult.trim(),
    };

    setEditedKeyResults(prev => [...prev, newKeyResultObj]);
    setNewKeyResult('');
    setIsAddingKeyResult(false);
  };

  const handleRemoveKeyResult = (referenceId: string) => {
    setEditedKeyResults(prev =>
      prev.filter(kr => kr.referenceId !== referenceId),
    );
  };

  const handleKeyResultChange = (referenceId: string, value: string) => {
    setEditedKeyResults(prev =>
      prev.map(kr =>
        kr.referenceId === referenceId
          ? { ...kr, keyResultDescription: value }
          : kr,
      ),
    );
  };

  // Handler functions for project editing
  const handleEditProject = () => {
    // Re-initialize edit state from current project data when entering edit mode
    if (currentProject) {
      setEditedProjectName(currentProject.name || '');
      setEditedProjectDescription(currentProject.description || '');

      // Re-initialize countries
      if (
        currentProject.focusCountries &&
        currentProject.focusCountries.length > 0
      ) {
        const currentCountries = currentProject.focusCountries
          .map((countryName: string) => {
            const countryOption = countryOptions.find(
              option => option.label === countryName,
            );
            return {
              code: countryOption?.value || '',
              name: countryName,
            };
          })
          .filter((country: { code: string; name: string }) => country.code);
        setSelectedCountries(currentCountries);
      }

      // Re-initialize goals categories
      if (
        currentProject.focusGoalRefs &&
        currentProject.focusGoalRefs.length > 0
      ) {
        const currentGoalsCategories = currentProject.focusGoalRefs
          .map((goalRef: string) => {
            for (const category of sdgGoalsCategories) {
              if (category.categoryRef === goalRef) {
                return {
                  categoryRef: goalRef,
                  categoryName: category.categoryName,
                };
              }
            }
            return null;
          })
          .filter(Boolean) as Array<{
          categoryRef: string;
          categoryName: string;
        }>;
        setSelectedGoalsCategories(currentGoalsCategories);
      }

      setSelectedProjectLevel(currentProject.minimumProjectLevel || '');

      // Re-initialize start date from current project data
      if (currentProject.startDate) {
        setSelectedStartDate(currentProject.startDate);
      } else {
        setSelectedStartDate('');
      }
    }

    setIsEditingProject(true);
  };

  const handleSaveProject = () => {
    if (!currentProject) return;

    // Validate required fields
    if (selectedCountries.length === 0) {
      setCountryError('At least one country must be selected');
      return;
    }

    updateProposedProject({
      proposedProjectRef: currentProject.referenceId,
      name: editedProjectName,
      description: editedProjectDescription,
      focusCountries: selectedCountries.map(country => country.name),
      focusGoalRefs: selectedGoalsCategories.map(
        category => category.categoryRef,
      ),
      minimumProjectLevel: selectedProjectLevel,
      ...(selectedStartDate && { startDate: selectedStartDate }),
    });
  };

  // Dropdown handlers for countries
  const handleCountrySelect = (country: { value: string; label: string }) => {
    if (selectedCountries.length >= 5) {
      setCountryError('Maximum 5 countries allowed');
      return;
    }

    const newCountry = { code: country.value, name: country.label };
    if (!selectedCountries.find(c => c.code === newCountry.code)) {
      setSelectedCountries(prev => [...prev, newCountry]);
      setCountryError('');
    }
    setShowCountryDropdown(false);
  };

  const handleRemoveCountry = (countryCode: string) => {
    setSelectedCountries(prev => prev.filter(c => c.code !== countryCode));
    setCountryError('');
  };

  // Dropdown handlers for goals categories
  const handleGoalsCategorySelect = (category: {
    categoryRef: string;
    categoryName: string;
  }) => {
    if (
      !selectedGoalsCategories.find(c => c.categoryRef === category.categoryRef)
    ) {
      setSelectedGoalsCategories(prev => [...prev, category]);
    }
    setShowGoalsCategoriesDropdown(false);
  };

  const handleRemoveGoalsCategory = (categoryRef: string) => {
    setSelectedGoalsCategories(prev =>
      prev.filter(c => c.categoryRef !== categoryRef),
    );
  };

  // Click outside handlers
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setShowCountryDropdown(false);
      }
      if (
        goalsCategoriesDropdownRef.current &&
        !goalsCategoriesDropdownRef.current.contains(event.target as Node)
      ) {
        setShowGoalsCategoriesDropdown(false);
      }
      if (
        projectLevelDropdownRef.current &&
        !projectLevelDropdownRef.current.contains(event.target as Node)
      ) {
        setShowProjectLevelDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleGenerateProposedProjects = () => {
    if (!currentProject?.referenceId) {
      errorToast('Project reference not found. Please try again.');
      return;
    }

    generateAutomationSummary(currentProject.referenceId);
  };

  if (!currentProject) {
    return (
      <div>
        <div className="flex items-center gap-2 px-3.5">
          <button
            onClick={handleBack}
            className="flex cursor-pointer items-center gap-2 transition-all duration-300 ease-in-out hover:scale-105"
          >
            <ArrowLeft size={24} className="-mt-0.5 text-primary" />
            <span className="text-lg font-medium">Go Back</span>
          </button>
        </div>

        <div className="-mt-48 flex h-screen items-center justify-center">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900">
              Project not available
            </h2>
            <p className="mt-2 text-gray-600">
              The requested project could not be found.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center gap-2 px-3.5">
          <button
            onClick={handleBack}
            className="flex cursor-pointer items-center gap-2 transition-all duration-300 ease-in-out hover:scale-105"
          >
            <ArrowLeft size={24} className="-mt-0.5 text-primary" />
            <span className="text-lg font-medium">Project Details</span>
          </button>
        </div>

        {/* Action Buttons */}
        <div className="mt-6 flex flex-col gap-4 sm:flex-row">
          <Button
            onClick={handleGenerateProposedProjects}
            disabled={
              isGeneratingAutomationSummary || isEditingGoal || isEditingProject
            }
            className="w-full border border-primary bg-primary px-6 py-3 text-white hover:bg-primary/80 disabled:cursor-not-allowed disabled:border-none disabled:bg-darkOrangeTwo sm:w-auto"
          >
            {isGeneratingAutomationSummary ? (
              <div className="flex items-center gap-2">
                <Spinner className="h-4 w-4 border-white border-b-[transparent]" />
                <span>Generating Summary...</span>
              </div>
            ) : (
              'Generate Automation Summary →'
            )}
          </Button>
        </div>
      </div>

      <div className="mx-auto max-w-4xl space-y-6 p-6">
        <div className="flex w-full items-center justify-center">
          <img
            src={solutionThumbnail}
            alt="Project Thumbnail"
            className="h-64 w-80 rounded-2xl object-cover"
          />
        </div>

        {/* Proposed Goal Section */}
        <div className="rounded-lg border border-grayFifteen bg-white shadow-sm">
          <div className="mb-4 flex items-center justify-between rounded-t-lg bg-darkGray p-2">
            <div className="text-xl font-semibold text-white">
              Proposed Goal
            </div>
            {!isEditingGoal ? (
              <button
                onClick={handleEditGoal}
                className="flex items-center gap-2 rounded bg-white p-2 hover:bg-gray-200"
              >
                Edit
                <PencilLine size={20} />
              </button>
            ) : (
              <Button
                onClick={handleSaveGoal}
                disabled={isUpdatingGoal}
                className="flex items-center gap-2 border border-primary bg-white px-4 py-2 text-primary hover:bg-primary hover:text-white disabled:cursor-not-allowed disabled:opacity-50"
              >
                {isUpdatingGoal ? (
                  <div className="flex items-center gap-2">
                    <Spinner className="h-4 w-4 border-primary border-b-[transparent]" />
                    <span>Saving...</span>
                  </div>
                ) : (
                  'Save'
                )}
              </Button>
            )}
          </div>

          {/* Goal Description */}
          <div className="px-2">
            {!isEditingGoal ? (
              <div className="text-xl font-bold capitalize text-gray-900">
                {currentProject.goals[0]?.description || ''}
              </div>
            ) : (
              <input
                type="text"
                value={editedGoalDescription}
                onChange={e => setEditedGoalDescription(e.target.value)}
                className="w-full rounded-md border border-gray-300 px-3 py-2 text-xl font-bold capitalize text-gray-900 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                placeholder="Enter goal description..."
              />
            )}
          </div>

          {/* Solution Statement */}
          {selectedSolution?.solutionStatement && (
            <p className="p-2 leading-relaxed text-gray-700">
              {selectedSolution.solutionStatement}
            </p>
          )}

          {/* Key Results */}
          <div className="px-2">
            {(isEditingGoal
              ? editedKeyResults
              : currentProject.goals[0]?.goalsKeyResults || []
            ).map((keyResult: any, index: number) => (
              <div
                key={keyResult.referenceId}
                className="mb-2 rounded-xl border border-gray-200 bg-peachOne p-2"
              >
                <div className="mb-4 flex items-center justify-between">
                  <div className="w-fit rounded border border-gray-200 bg-white px-4 py-1.5 font-medium">
                    Key Result {index + 1}
                  </div>
                  {isEditingGoal && (
                    <button
                      onClick={() =>
                        handleRemoveKeyResult(keyResult.referenceId)
                      }
                      className="text-red-600 hover:text-red-800"
                    >
                      Remove
                    </button>
                  )}
                </div>
                {!isEditingGoal ? (
                  <p className="leading-relaxed text-gray-700">
                    {keyResult.keyResultDescription}
                  </p>
                ) : (
                  <textarea
                    value={keyResult.keyResultDescription}
                    onChange={e =>
                      handleKeyResultChange(
                        keyResult.referenceId,
                        e.target.value,
                      )
                    }
                    rows={2}
                    className="w-full rounded-md border border-gray-300 px-3 py-2 leading-relaxed text-gray-700 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                    placeholder="Enter key result description..."
                  />
                )}
              </div>
            ))}

            {/* Add New Key Result */}
            {isEditingGoal && (
              <div className="mb-2 rounded-xl border border-gray-200 bg-peachOne p-2">
                {!isAddingKeyResult ? (
                  <button
                    onClick={() => setIsAddingKeyResult(true)}
                    className="flex items-center gap-2 rounded border border-gray-200 bg-white px-4 py-1.5 font-medium hover:bg-grayNineTeen"
                  >
                    Add
                    <Plus size={20} className="-mt-1" />
                  </button>
                ) : (
                  <div className="space-y-2">
                    <div className="w-fit rounded border border-gray-200 bg-white px-4 py-1.5 font-medium">
                      New Key Result
                    </div>
                    <textarea
                      value={newKeyResult}
                      onChange={e => setNewKeyResult(e.target.value)}
                      rows={2}
                      className="w-full rounded-md border border-gray-300 px-3 py-2 leading-relaxed text-gray-700 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                      placeholder="Enter new key result description..."
                      autoFocus
                    />
                    <div className="flex gap-2">
                      <Button
                        onClick={handleAddKeyResult}
                        disabled={!newKeyResult.trim()}
                        className="rounded bg-primary px-4 py-2 text-white hover:bg-primary/80 disabled:cursor-not-allowed disabled:opacity-50"
                      >
                        Save
                      </Button>
                      <Button
                        onClick={() => {
                          setIsAddingKeyResult(false);
                          setNewKeyResult('');
                        }}
                        className="rounded bg-gray-200 px-4 py-2 text-gray-700 hover:bg-gray-300"
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Proposed Project Section */}
        <div className="rounded-lg border border-grayFifteen bg-white shadow-sm">
          <div className="mb-4 flex items-center justify-between rounded-t-lg bg-darkGray p-2">
            <div className="text-xl font-semibold text-white">
              Proposed Project
            </div>
            {!isEditingProject ? (
              <button
                onClick={handleEditProject}
                className="flex items-center gap-2 rounded bg-white p-2 hover:bg-gray-200"
              >
                Edit
                <PencilLine size={20} />
              </button>
            ) : (
              <Button
                onClick={handleSaveProject}
                disabled={isUpdatingProject}
                className="flex items-center gap-2 border border-primary bg-white px-4 py-2 text-primary hover:bg-primary hover:text-white disabled:cursor-not-allowed disabled:opacity-50"
              >
                {isUpdatingProject ? (
                  <div className="flex items-center gap-2">
                    <Spinner className="h-4 w-4 border-primary border-b-[transparent]" />
                    <span>Saving...</span>
                  </div>
                ) : (
                  'Save'
                )}
              </Button>
            )}
          </div>

          {/* Project Name */}
          <div className="mb-4 px-2">
            {!isEditingProject ? (
              <div className="text-xl font-bold capitalize text-gray-900">
                {currentProject.name || ''}
              </div>
            ) : (
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  Project Name
                </label>
                <input
                  type="text"
                  value={editedProjectName}
                  onChange={e => setEditedProjectName(e.target.value)}
                  className="w-full rounded-md border border-gray-300 px-3 py-2 text-xl font-bold capitalize text-gray-900 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                  placeholder="Enter project name..."
                />
              </div>
            )}
          </div>

          {/* Project Description */}
          <div className="mb-4 px-2">
            {!isEditingProject ? (
              <p className="leading-relaxed text-gray-700">
                {currentProject.description || ''}
              </p>
            ) : (
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  Project Description
                </label>
                <textarea
                  value={editedProjectDescription}
                  onChange={e => setEditedProjectDescription(e.target.value)}
                  rows={4}
                  className="w-full rounded-md border border-gray-300 px-3 py-2 leading-relaxed text-gray-700 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                  placeholder="Enter project description..."
                />
              </div>
            )}
          </div>
        </div>

        {!isEditingProject && (
          <div className="flex flex-col gap-3">
            {/* Minimum Project Level */}
            <div className="flex items-center gap-2">
              <div className="mt-2 flex items-center justify-between rounded-xl bg-peachOne">
                <div className="mb-2 block h-fit whitespace-nowrap rounded bg-white p-2 text-sm font-medium text-gray-700">
                  Minimum Project Level
                </div>
              </div>

              <div className="relative">
                <div className="bg-peachOnepx-3 flex w-fit items-center justify-between gap-3 rounded-xl border border-gray-300 py-2 text-left text-sm">
                  <div className="px-2">
                    <div className="flex items-center gap-1 rounded-md border border-gray-300 bg-white px-2 py-0.5">
                      {selectedProjectLevel
                        ? projectLevels.find(
                            level => level.value === selectedProjectLevel,
                          )?.label || selectedProjectLevel
                        : 'Select project level...'}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Focus Countries */}
            <div className="flex items-center gap-2">
              <div className="mt-2 flex items-center justify-between rounded-xl bg-peachOne">
                <div className="mb-2 block h-fit whitespace-nowrap rounded bg-white p-2 text-sm font-medium text-gray-700">
                  Focus Countries
                </div>
              </div>

              <div className="relative">
                <div
                  className={`flex w-fit items-center justify-between gap-3 rounded-xl border border-gray-300 ${
                    selectedCountries.length >= 5
                      ? 'cursor-not-allowed bg-gray-100'
                      : 'bg-peachOne'
                  } px-3 py-2 text-left text-sm`}
                >
                  <div className="flex flex-wrap gap-2">
                    {selectedCountries.map(country => (
                      <div
                        key={country.code}
                        className="flex items-center gap-1 rounded-md border border-gray-300 bg-white px-2.5 py-0.5"
                      >
                        <span className="text-lg">
                          {countryFlags[country.name] || '🏳️'}
                        </span>
                        <span className="text-sm">{country.name}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Goals Categories */}
            <div className="flex items-center gap-2">
              <div className="mt-2 flex items-center justify-between rounded-xl bg-peachOne">
                <div className="mb-2 block h-fit whitespace-nowrap rounded bg-white p-2 text-sm font-medium text-gray-700">
                  Goals Categories
                </div>
              </div>

              {/* Goals Categories Dropdown */}
              <div className="relative">
                <div className="flex w-fit items-center justify-between gap-3 rounded-xl border border-gray-300 bg-peachOne px-3 py-2 text-left text-sm">
                  <div className="flex flex-wrap gap-2">
                    {selectedGoalsCategories.map(category => (
                      <div
                        key={category.categoryRef}
                        className="flex items-center gap-1 rounded-md border border-gray-300 bg-white px-2.5 py-0.5"
                      >
                        <span className="text-sm">{category.categoryName}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Start Date */}
            <div className="flex items-center gap-2">
              <div className="mt-2 flex items-center justify-between rounded-xl bg-peachOne">
                <div className="mb-2 block h-fit whitespace-nowrap rounded bg-white p-2 text-sm font-medium text-gray-700">
                  Start Date
                </div>
              </div>

              <div className="relative">
                <div className="bg-peachOnepx-3 flex w-fit items-center justify-between gap-3 rounded-xl border border-gray-300 py-2 text-left text-sm">
                  <div className="px-2">
                    <div className="flex items-center gap-1 rounded-md border border-gray-300 bg-white px-2 py-0.5">
                      {selectedStartDate && moment(selectedStartDate).isValid()
                        ? moment(selectedStartDate).format('D MMMM YYYY')
                        : 'Not set'}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Focus Countries Edit Section */}
        {isEditingProject && (
          <div className="rounded-lg border border-grayFifteen bg-white p-4 shadow-sm">
            <label className="mb-1 block text-sm font-medium text-gray-700">
              Focus Countries
            </label>
            <div className="relative" ref={dropdownRef}>
              {/* Selected Countries Display */}
              <div className="mb-2 flex flex-wrap gap-2">
                {selectedCountries.map(country => (
                  <div
                    key={country.code}
                    className="flex items-center gap-2 rounded-full border border-gray-300 bg-white px-3 py-1"
                  >
                    <span className="text-lg">
                      {countryFlags[
                        country.code as keyof typeof countryFlags
                      ] || '🏳️'}
                    </span>
                    <span className="text-sm font-medium">{country.name}</span>
                    <button
                      onClick={() => handleRemoveCountry(country.code)}
                      className="text-gray-500 hover:text-red-600"
                    >
                      <X size={14} />
                    </button>
                  </div>
                ))}
              </div>

              {/* Dropdown Button */}
              <button
                type="button"
                onClick={() => setShowCountryDropdown(!showCountryDropdown)}
                className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-left focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
              >
                <div className="flex items-center justify-between">
                  <span className="text-gray-500">
                    {selectedCountries.length === 0
                      ? 'Select countries...'
                      : 'Add more countries...'}
                  </span>
                  <ChevronDown
                    size={20}
                    className={`transform transition-transform ${
                      showCountryDropdown ? 'rotate-180' : ''
                    }`}
                  />
                </div>
              </button>

              {/* Dropdown Options */}
              {showCountryDropdown && (
                <div className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md border border-gray-300 bg-white shadow-lg">
                  {countryOptions
                    .filter(
                      option =>
                        !selectedCountries.find(c => c.code === option.value),
                    )
                    .map(option => (
                      <button
                        key={option.value}
                        onClick={() => handleCountrySelect(option)}
                        className="flex w-full items-center gap-2 px-3 py-2 text-left hover:bg-gray-100"
                      >
                        <span className="text-lg">
                          {countryFlags[
                            option.value as keyof typeof countryFlags
                          ] || '🏳️'}
                        </span>
                        <span>{option.label}</span>
                      </button>
                    ))}
                </div>
              )}
            </div>
            {countryError && (
              <p className="mt-1 text-sm text-red-600">{countryError}</p>
            )}
          </div>
        )}

        {/* Goals Categories Edit Section */}
        {isEditingProject && (
          <div className="rounded-lg border border-grayFifteen bg-white p-4 shadow-sm">
            <label className="mb-1 block text-sm font-medium text-gray-700">
              Goals Categories
            </label>
            <div className="relative" ref={goalsCategoriesDropdownRef}>
              {/* Selected Categories Display */}
              <div className="mb-2 flex flex-wrap gap-2">
                {selectedGoalsCategories.map(category => (
                  <div
                    key={category.categoryRef}
                    className="flex items-center gap-2 rounded-full border border-gray-300 bg-white px-3 py-1"
                  >
                    <span className="text-sm font-medium">
                      {category.categoryName}
                    </span>
                    <button
                      onClick={() =>
                        handleRemoveGoalsCategory(category.categoryRef)
                      }
                      className="text-gray-500 hover:text-red-600"
                    >
                      <X size={14} />
                    </button>
                  </div>
                ))}
              </div>

              {/* Dropdown Button */}
              <button
                type="button"
                onClick={() =>
                  setShowGoalsCategoriesDropdown(!showGoalsCategoriesDropdown)
                }
                className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-left focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
              >
                <div className="flex items-center justify-between">
                  <span className="text-gray-500">
                    {selectedGoalsCategories.length === 0
                      ? 'Select goals categories...'
                      : 'Add more categories...'}
                  </span>
                  <ChevronDown
                    size={20}
                    className={`transform transition-transform ${
                      showGoalsCategoriesDropdown ? 'rotate-180' : ''
                    }`}
                  />
                </div>
              </button>

              {/* Dropdown Options */}
              {showGoalsCategoriesDropdown && (
                <div className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md border border-gray-300 bg-white shadow-lg">
                  {sdgGoalsCategories
                    .filter(
                      category =>
                        !selectedGoalsCategories.find(
                          c => c.categoryRef === category.categoryRef,
                        ),
                    )
                    .map(category => (
                      <button
                        key={category.categoryRef}
                        onClick={() =>
                          handleGoalsCategorySelect({
                            categoryRef: category.categoryRef,
                            categoryName: category.categoryName,
                          })
                        }
                        className="flex w-full items-center px-3 py-2 text-left hover:bg-gray-100"
                      >
                        <span>{category.categoryName}</span>
                      </button>
                    ))}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Minimum Project Level Section */}
        {isEditingProject && (
          <div className="rounded-lg border border-grayFifteen bg-white p-4 shadow-sm">
            <label className="mb-1 block text-sm font-medium text-gray-700">
              Minimum Project Level
            </label>
            <div className="relative" ref={projectLevelDropdownRef}>
              <button
                type="button"
                onClick={() =>
                  setShowProjectLevelDropdown(!showProjectLevelDropdown)
                }
                className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-left focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
              >
                <div className="flex items-center justify-between">
                  <span
                    className={
                      selectedProjectLevel ? 'text-gray-900' : 'text-gray-500'
                    }
                  >
                    {selectedProjectLevel
                      ? projectLevels.find(
                          level => level.value === selectedProjectLevel,
                        )?.label || selectedProjectLevel
                      : 'Select project level...'}
                  </span>
                  <ChevronDown
                    size={20}
                    className={`transform transition-transform ${
                      showProjectLevelDropdown ? 'rotate-180' : ''
                    }`}
                  />
                </div>
              </button>

              {showProjectLevelDropdown && (
                <div className="absolute z-10 mt-1 w-full rounded-md border border-gray-300 bg-white shadow-lg">
                  {projectLevels.map(level => (
                    <button
                      key={level.value}
                      onClick={() => {
                        setSelectedProjectLevel(level.value);
                        setShowProjectLevelDropdown(false);
                      }}
                      className="flex w-full items-center px-3 py-2 text-left hover:bg-gray-100"
                    >
                      <span>{level.label}</span>
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Start Date Section */}
        {isEditingProject && (
          <div className="rounded-lg border border-grayFifteen bg-white p-4 shadow-sm">
            <label className="mb-1 block text-sm font-medium text-gray-700">
              Start Date
            </label>
            <input
              type="date"
              value={selectedStartDate ? selectedStartDate.split('T')[0] : ''}
              onChange={e => {
                const dateValue = e.target.value;
                if (dateValue) {
                  setSelectedStartDate(new Date(dateValue).toISOString());
                } else {
                  setSelectedStartDate('');
                }
              }}
              className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
            />
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end">
          <Button
            onClick={handleGenerateProposedProjects}
            disabled={
              isGeneratingAutomationSummary || isEditingGoal || isEditingProject
            }
            className="w-full border border-primary bg-primary px-6 py-3 text-white hover:bg-primary/80 disabled:cursor-not-allowed disabled:border-none disabled:bg-darkOrangeTwo sm:w-auto"
          >
            {isGeneratingAutomationSummary ? (
              <div className="flex items-center gap-2">
                <Spinner className="h-4 w-4 border-white border-b-[transparent]" />
                <span>Generating Summary...</span>
              </div>
            ) : (
              'Generate Automation Summary →'
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
