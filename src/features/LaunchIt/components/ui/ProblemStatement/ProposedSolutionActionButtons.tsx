import Button from '@/components/ui/ButtonComponent';
import { Spinner } from '@/components/ui/CommonWidget/Loader';

interface ProposedSolutionActionButtonsProps {
  onSaveForLater: () => void;
  onGenerateProposedProjects: () => void;
  isGeneratingProposedProjects?: boolean;
  isUpdating?: boolean;
}

export default function ProposedSolutionActionButtons({
  // onSaveForLater,
  onGenerateProposedProjects,
  isGeneratingProposedProjects = false,
  isUpdating = false,
}: ProposedSolutionActionButtonsProps) {
  return (
    <div className="flex flex-col gap-4 pt-6 sm:flex-row">
      {/* <Button
        onClick={onSaveForLater}
        className="w-full border border-black px-6 py-3 text-darkGray hover:border-primary hover:text-primary sm:w-auto"
      >
        Save For Later
      </Button> */}
      <Button
        onClick={onGenerateProposedProjects}
        disabled={isGeneratingProposedProjects || isUpdating}
        className="w-full border border-primary bg-primary px-6 py-3 text-white hover:bg-primary/80 disabled:cursor-not-allowed disabled:border-none disabled:bg-darkOrangeTwo sm:w-auto"
      >
        {isGeneratingProposedProjects ? (
          <div className="flex items-center gap-2">
            <Spinner className="h-4 w-4 border-white border-b-[transparent]" />
            <span>Generating Projects...</span>
          </div>
        ) : (
          'Generate Proposed Projects →'
        )}
      </Button>
    </div>
  );
}
