import { useNavigate } from 'react-router-dom';
import Button from '@/components/ui/ButtonComponent';
import ModalCase from '@/components/ui/Modals/ModalCase';
import { useAppContext } from '@/context/event/AppEventContext';
import { successIcon } from '@/assets/images';

interface ProjectPlanGenerationModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function ProjectPlanGenerationModal({
  isOpen,
  onClose,
}: ProjectPlanGenerationModalProps) {
  const navigate = useNavigate();
  const { currentAccountType } = useAppContext();

  const handleGotIt = () => {
    onClose();
    navigate(`/${currentAccountType}/dashboard-projects`);
  };

  if (!isOpen) return null;

  return (
    <ModalCase
      onClose={onClose}
      className="mx-auto w-[95%] bg-white px-4 py-8 text-black sm:max-w-[500px] sm:px-8 sm:py-12"
    >
      <div className="mt-4">
        <div className="max-w-[450px]">
          <div className="mb-6 flex items-center justify-center">
            <img
              className="h-[60px] w-[60px]"
              src={successIcon}
              alt="success icon"
            />
          </div>

          <div className="text-center">
            <h2 className="mb-4 text-[18px] font-[600] text-subText">
              Project Plan Generation Started
            </h2>

            <p className="mb-8 text-[14px] font-[400] leading-relaxed text-gray-600">
              Generating Project Plan in background. You will receive a
              notification and email when plan is ready.
            </p>
          </div>

          <div className="flex justify-center">
            <Button
              type="button"
              onClick={handleGotIt}
              className="w-full max-w-[200px] bg-primary text-white hover:bg-primary/80"
            >
              <p className="text-[14px] font-[500]">Got It</p>
            </Button>
          </div>
        </div>
      </div>
    </ModalCase>
  );
}
