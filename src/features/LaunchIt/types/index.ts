export interface CreateProblemStatementFormData {
  description: string;
  detailedDescriptionFile?: File | null;
  focusCountries: string[];
  budget: number | null;
  status: 'private' | 'public';
  videoLink?: string;
}

export interface BudgetOption {
  value: string;
  label: string;
}

export interface ProblemStatementResponse {
  id: string;
  title: string;
  description: string;
  problemStatementRef: string;
  focusCountries: Array<{
    code: string;
    name: string;
  }>;
  focusIndustries: Array<{
    id: string;
    name: string;
  }>;
  goalsCategories: Array<{
    id: string;
    name: string;
  }>;
  goalsSubcategories: Array<{
    id: string;
    name: string;
  }>;
  budget: string;
  status: 'private' | 'public';
  videoLink: string;
  attachedFiles: Array<{
    id: string;
    name: string;
    url: string;
    type: string;
    size: number;
  }>;
  createdBy: {
    firstName: string;
    lastName: string;
    avatar?: string;
  };
  createdAt: string;
}

export interface BudgetOption {
  value: string;
  label: string;
}

export interface CountryOption {
  value: string;
  label: string;
}

export interface ProblemStatementApiResponse {
  title: string;
  problemStatementRef: string;
  categoryRefList: string[];
  subcategoryRefList: string[];
  descVidUrl?: string;
  descDocUrl?: string;
  description: string;
  refinedDescription?: string;
  createdBy: string;
  createdAt: string;
  createdByName: string;
  approvalStatus: 'PENDING' | 'APPROVED' | 'REJECTED';
  creatorCountry: string;
  countries: string[];
  visibility?: 'PUBLIC' | 'PRIVATE' | null;
  budget: number;
  isLaunchIt: boolean;
}

// File type for downloads
export interface AttachedFile {
  id: string;
  name: string;
  url: string;
  type: string;
  size: number;
}

export interface ApiError {
  response?: {
    data?: {
      message?: string;
    };
  };
  message?: string;
}

export interface IndustryOption {
  value: string;
  label: string;
}

export interface CategoryOption {
  value: string;
  label: string;
}

export interface SubcategoryOption {
  value: string;
  label: string;
}

export interface GoalsCategoryOption {
  value: string;
  label: string;
}
