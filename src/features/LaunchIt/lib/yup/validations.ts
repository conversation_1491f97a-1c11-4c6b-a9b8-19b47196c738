import * as yup from 'yup';
import { CreateProblemStatementFormData } from '../../types';

export const createProblemStatementSchema: yup.ObjectSchema<CreateProblemStatementFormData> =
  yup
    .object()
    .shape({
      description: yup
        .string()
        .required('Description is a required field')
        .max(5000, 'Description cannot exceed 5000 characters'),
      detailedDescriptionFile: yup.mixed<File>().nullable(),
      focusCountries: yup
        .array()
        .of(yup.string().required('Country is a required field'))
        .min(1, 'Please select at least one focus country')
        .max(5, 'You can select a maximum of 5 focus countries')
        .required('Focus countries is a required field'),
      budget: yup
        .number()
        .nullable()
        .transform((value, originalValue) => {
          // Allow empty string to be treated as null initially
          if (originalValue === '' || originalValue === undefined) {
            return null;
          }
          return value;
        })
        .typeError('Budget must be a valid number')
        .positive('Budget must be a positive number')
        .required('Budget is a required field') as yup.Schema<number | null>,
      status: yup
        .string()
        .oneOf(['private', 'public'], 'Status must be either private or public')
        .required('Status is a required field'),
      videoLink: yup.string().when('status', {
        is: 'public',
        then: schema =>
          schema
            .url('Please enter a valid YouTube URL')
            .required('Video link is a required field when status is public'),
        otherwise: schema => schema.notRequired(),
      }),
    })
    .required();
