import { useNavigate } from 'react-router-dom';
import { useState } from 'react';
import { Files, Search } from 'lucide-react';
import { overviewCards } from './data/constants';
import CardOption from './components/ui/CardOption';
import { useUserContext } from '@/context/user/UserContext';
import { useGetProposedSolutions } from './hooks/apiQueryHooks/launchItQueryHooks';
import Button from '@/components/ui/ButtonComponent';
import { Spinner } from '@/components/ui/CommonWidget/Loader';
import solutionThumbnail from './assets/images/solutionThumbnail.png';
import { useAppContext } from '@/context/event/AppEventContext';

export default function LaunchItOverview() {
  const { data } = useUserContext();
  const { currentAccountType } = useAppContext();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch proposed solutions
  const { data: proposedSolutionsData, isLoading: isLoadingProposedSolutions } =
    useGetProposedSolutions();

  // Filter proposed solutions based on search term
  const filteredProposedSolutions =
    proposedSolutionsData?.data?.filter(solution =>
      solution.goals[0]?.description
        ?.toLowerCase()
        .includes(searchTerm.toLowerCase()),
    ) || [];

  const handleViewProposedSolution = (solutionRef: string) => {
    navigate(
      `/${currentAccountType}/launch/proposed-solutions/${solutionRef}`,
      {
        state: { proposedSolutions: proposedSolutionsData?.data, solutionRef },
      },
    );
  };

  return (
    <div className="flex flex-col items-center px-12">
      <div className="my-8 w-3/4 max-w-screen-md rounded-md border border-dashed border-grayNine px-12 py-2.5 text-center">
        <p className="text-xl">Hi {`${data?.user?.firstName}`}!</p>
        <p className="mt-2 font-semibold text-darkGray">
          Ready to bring your idea to life?
        </p>
      </div>

      <div className="flex justify-center">
        <div className="flex flex-wrap items-center justify-center gap-6">
          {overviewCards.map((card, index) => (
            <CardOption
              key={index}
              {...card}
              onClick={() =>
                card.route
                  ? navigate(card.route)
                  : console.warn('No route defined for', card.title)
              }
            />
          ))}
        </div>
      </div>

      {/* Proposed Solutions Section */}
      <div className="mt-16 w-full max-w-7xl">
        {/* Section Header */}
        <div className="mb-8 flex items-center justify-between">
          <div className="text-xl font-semibold text-blackFive">
            Proposed Solutions
          </div>

          {/* Search Bar */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search solutions..."
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className="w-80 rounded-lg border border-gray-300 py-2 pl-10 pr-4 text-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary disabled:cursor-not-allowed"
              disabled={
                proposedSolutionsData?.data &&
                proposedSolutionsData.data.length === 0
              }
            />
          </div>
        </div>

        {/* Loading State */}
        {isLoadingProposedSolutions ? (
          <div className="flex h-64 flex-col items-center justify-center">
            <Spinner />
            <p className="py-4 text-gray-500">
              Fetching previous proposed solutions...
            </p>
          </div>
        ) : /* Solutions Grid */
        proposedSolutionsData?.data && proposedSolutionsData.data.length > 0 ? (
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
            {filteredProposedSolutions.map((solution, index) => (
              <div
                key={solution.proposedSolutionRef}
                className="relative flex flex-col overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition-shadow hover:shadow-md"
              >
                {/* Proposed Label */}
                <div className="absolute -top-0.5 right-0 z-[2]">
                  <span className="bg-greenOne px-2 py-1 text-xs font-medium text-white">
                    Proposed #{index + 1}
                  </span>
                </div>

                {/* Image */}
                <div className="aspect-video bg-gray-100">
                  <img
                    src={solutionThumbnail}
                    alt={solution.goals[0]?.description || 'Solution'}
                    className="h-full w-full object-cover"
                  />
                </div>

                {/* Content */}
                <div className="flex flex-1 flex-col p-4">
                  <h3 className="mb-2 text-sm font-bold text-gray-900">
                    {solution.goals[0]?.description ||
                      '[Proposed Project Name]'}
                  </h3>

                  <div className="mb-3 flex-1 space-y-2 text-[10px] font-semibold text-darkGray">
                    <p className="rounded bg-grayNineTeen p-2">
                      <span className="font-medium">Goals Categories:</span>{' '}
                      {solution.categories
                        .map(category => category.categoryName)
                        ?.join(', ') || 'Good Health & Well-Being'}
                    </p>
                    <p className="rounded bg-grayNineTeen p-2">
                      <span className="font-medium">Country Focus:</span>{' '}
                      {solution.focusCountries?.join(', ') || 'Nigeria'}
                    </p>
                  </div>

                  <Button
                    onClick={() =>
                      handleViewProposedSolution(solution.proposedSolutionRef)
                    }
                    className="w-full bg-primary text-sm text-white hover:bg-primary/80"
                  >
                    View Proposed Solution
                  </Button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="flex h-32 flex-col items-center justify-center">
            <Files className="mx-auto mb-4 h-16 w-16 text-gray-400" />
            <p className="text-gray-500">No proposed solutions available</p>
          </div>
        )}

        {/* No Results Message */}
        {!isLoadingProposedSolutions &&
          filteredProposedSolutions.length === 0 &&
          searchTerm && (
            <div className="flex h-32 items-center justify-center">
              <p className="text-gray-500">
                No solutions found matching "{searchTerm}"
              </p>
            </div>
          )}
      </div>
    </div>
  );
}
