import { useMutation, useQuery } from '@tanstack/react-query';
import {
  useCreateLaunchItProblemStatementApi,
  useGenerateProposedSolutionsApi,
  GenerateProposedSolutionsResponse,
  useGenerateAutomationSummaryApi,
  AutomationSummaryResponse,
  useGenerateProjectPlanApi,
  GenerateProjectPlanPayload,
  GenerateProjectPlanResponse,
  useGetProposedSolutionsApi,
  GetProposedSolutionsResponse,
  useUpdateProblemStatementApi,
  UpdateProblemStatementPayload,
  UpdateProblemStatementResponse,
  useUpdateProposedSolutionApi,
  UpdateProposedSolutionPayload,
  UpdateProposedSolutionResponse,
  useUpdateProposedGoalApi,
  UpdateProposedGoalPayload,
  UpdateProposedGoalResponse,
  useUpdateProposedProjectApi,
  UpdateProposedProjectPayload,
  UpdateProposedProjectResponse,
} from '../../services/launchItApiRequests';

export const useCreateLaunchItProblemStatement = (options: any = {}) => {
  const createProblemStatement = useCreateLaunchItProblemStatementApi();
  return useMutation(createProblemStatement, {
    mutationKey: ['CREATE_LAUNCH_IT_PROBLEM_STATEMENT'],
    ...options,
  });
};

export const useGenerateProposedSolutions = (options: any = {}) => {
  const generateProposedSolutions = useGenerateProposedSolutionsApi();
  return useMutation<GenerateProposedSolutionsResponse, Error, string>({
    mutationFn: generateProposedSolutions,
    mutationKey: ['GENERATE_PROPOSED_SOLUTIONS'],
    ...options,
  });
};

export const useGenerateAutomationSummary = (options: any = {}) => {
  const generateAutomationSummary = useGenerateAutomationSummaryApi();
  return useMutation<AutomationSummaryResponse, Error, string>({
    mutationFn: generateAutomationSummary,
    mutationKey: ['GENERATE_AUTOMATION_SUMMARY'],
    ...options,
  });
};

export const useGenerateProjectPlan = (options: any = {}) => {
  const generateProjectPlan = useGenerateProjectPlanApi();
  return useMutation<
    GenerateProjectPlanResponse,
    Error,
    GenerateProjectPlanPayload
  >({
    mutationFn: generateProjectPlan,
    mutationKey: ['GENERATE_PROJECT_PLAN'],
    ...options,
  });
};

export const useGetProposedSolutions = (options: any = {}) => {
  const getProposedSolutions = useGetProposedSolutionsApi();
  return useQuery<GetProposedSolutionsResponse, Error>({
    queryKey: ['GET_PROPOSED_SOLUTIONS'],
    queryFn: getProposedSolutions,
    ...options,
  });
};

export const useUpdateProblemStatement = (options: any = {}) => {
  const updateProblemStatement = useUpdateProblemStatementApi();
  return useMutation<
    UpdateProblemStatementResponse,
    Error,
    UpdateProblemStatementPayload
  >({
    mutationFn: updateProblemStatement,
    mutationKey: ['UPDATE_PROBLEM_STATEMENT'],
    ...options,
  });
};

export const useUpdateProposedSolution = (options: any = {}) => {
  const updateProposedSolution = useUpdateProposedSolutionApi();
  return useMutation<
    UpdateProposedSolutionResponse,
    Error,
    UpdateProposedSolutionPayload
  >({
    mutationFn: updateProposedSolution,
    mutationKey: ['UPDATE_PROPOSED_SOLUTION'],
    ...options,
  });
};

export const useUpdateProposedGoal = (options: any = {}) => {
  const updateProposedGoal = useUpdateProposedGoalApi();
  return useMutation<
    UpdateProposedGoalResponse,
    Error,
    UpdateProposedGoalPayload
  >({
    mutationFn: updateProposedGoal,
    mutationKey: ['UPDATE_PROPOSED_GOAL'],
    ...options,
  });
};

export const useUpdateProposedProject = (options: any = {}) => {
  const updateProposedProject = useUpdateProposedProjectApi();
  return useMutation<
    UpdateProposedProjectResponse,
    Error,
    UpdateProposedProjectPayload
  >({
    mutationFn: updateProposedProject,
    mutationKey: ['UPDATE_PROPOSED_PROJECT'],
    ...options,
  });
};
