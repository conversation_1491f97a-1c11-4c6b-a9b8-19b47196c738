import { ValuesUnion } from '../../../../types'
import { forbiddenEmails } from '../../data'

export class Authhelper {
  public static emailReplaceCharacterRegex(emailString: string) {
    if (emailString.trim() === '') {
      return emailString
    }
    const [username, domain] = emailString.split('@')
    const obfuscatedUsername =
      username.charAt(0) + '*'.repeat(username.length - 2) + username.slice(-1)
    const obfuscatedDomain =
      domain.charAt(0) + '*'.repeat(domain.length - 2) + domain.slice(-1)

    return obfuscatedUsername + '@' + obfuscatedDomain
  }

  public static isValidMailAddress(email: string) {
    const match = /^\w+[\w.-]*@(\w+[\w.-]*?\.\w{2,4})$/.exec(email!)
    if (!match) return false
    const forbiddenDomains = [
      forbiddenEmails.gmail,
      forbiddenEmails.yahoo,
      forbiddenEmails.aol,
      forbiddenEmails.hotmail,
    ]
    if (
      forbiddenDomains.includes(
        match[1].toLowerCase() as ValuesUnion<typeof forbiddenEmails>
      )
    )
      return false
    return true
  }
}
