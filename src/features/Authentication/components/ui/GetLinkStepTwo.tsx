import { motion } from 'framer-motion';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

import Button from '../../../../components/ui/ButtonComponent';
import { useCustomToast } from '../../../../hooks/useToast';
import { DataResponse } from '../../../../types';
import { Helper } from '../../../../utils/helpers';
import { useAuthEventContext } from '../../context/event/AuthEventContext';
import { useGetLinkForPasswordReset } from '../../hooks/apiQueryHooks/authQueryHooks';
import { Authhelper } from '../../utils/authHelper/authHelper';

export default function GetLinkStepTwo() {
  const navigate = useNavigate();
  useEffect(() => {
    navigate('/reset-password?n_t=2');
  }, []);
  const { successToast, errorToast } = useCustomToast();
  const { activateGetOtp, setActivateGetOtpHandler, email } =
    useAuthEventContext();
  const next = (res: DataResponse<any>) => {
    successToast(res?.message || Helper.successMessage);
    setActivateGetOtpHandler(false);
  };
  const onError = () => {
    errorToast('Try again!!!');
    setActivateGetOtpHandler(false);
  };
  useGetLinkForPasswordReset(email, {
    enabled: !!activateGetOtp,
    onSuccess: next,
    onError,
  });
  return (
    <motion.div
      exit={{ opacity: 0 }}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.2, delay: 0.5, ease: 'easeIn' }}
      className="flex-1 px-4 sm:px-0"
    >
      <div className="mx-auto h-full w-full max-w-[457px] py-8 sm:py-20">
        <h2 className="mb-3 pt-12 text-center text-[24px] font-[500] leading-[47px] sm:pt-24 sm:text-[32px]">
          We have sent an Email!
        </h2>
        <p className="pb-12 text-center text-[16px] leading-[27px]">
          We've sent a password reset link to your registered email address{' '}
          {Authhelper.emailReplaceCharacterRegex(email?.email || '')}.
        </p>
        <p className=" pb-12 text-center text-[16px] leading-[27px]">
          Didn't receive the email? Check spam or promotion folder or{' '}
          <span
            onClick={() => setActivateGetOtpHandler(true)}
            className="cursor-pointer text-primary underline"
          >
            Click to resend.
          </span>
        </p>
        <Button
          type="button"
          onClick={() => {
            window.open(
              `https://mail.google.com/mail/u/${email.email}`,
              '_blank',
            );
          }}
          className="mx-auto  mt-[32px]
          w-full border   
          border-primary bg-transparent text-primary hover:bg-primary hover:text-white"
        >
          {' '}
          <p className="text-[16px] font-[700]">Proceed to reset password</p>
        </Button>
      </div>
    </motion.div>
  );
}
