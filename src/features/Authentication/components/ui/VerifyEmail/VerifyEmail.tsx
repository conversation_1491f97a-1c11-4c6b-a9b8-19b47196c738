import { useKeycloak } from '@react-keycloak/web';
import { useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';

import Button from '../../../../../components/ui/ButtonComponent';
import { MainLoaderSkeleton } from '../../../../../components/hocs/suspense/withSuspense';
import { useAppContext } from '../../../../../context/event/AppEventContext';
import { ApiError } from '../../../../../types';
import { Helper } from '../../../../../utils/helpers';
import { useVerifyEmail } from '../../../hooks/apiQueryHooks/authQueryHooks';

export default function VerifyEmail() {
  const { currentAccountType } = useAppContext();
  const navigate = useNavigate();
  const [uiError, setUiError] = useState('');
  const { keycloak } = useKeycloak();
  const [searchParams] = useSearchParams();
  const token = searchParams.get('token');
  const { isLoading, isError } = useVerifyEmail(
    { token },
    {
      enabled: !!token,
      onSuccess: () => {
        keycloak?.login({
          redirectUri:
            window.location.origin +
            `/${currentAccountType}/dashboard-overview`,
        });
      },
      onError: (error: ApiError<any>) => {
        setUiError(Helper.getError(error));
      },
    },
  );
  if (isLoading) return <MainLoaderSkeleton />;
  if (isError)
    return (
      <div className="flex h-[400px] items-center justify-center">
        <div className="flex flex-col items-center justify-center ">
          <div>
            <h3 className="mb-4 text-center text-[18px] font-semibold sm:text-[28px]">
              Verification Link Expired
            </h3>
            <p className="mb-4 text-center text-[14px] sm:text-[18px]">
              {uiError}
            </p>
            <p className="text-center text-[14px] sm:text-[18px]">
              Proceed to home page.
            </p>
            <Button
              onClick={() => {
                navigate('/');
              }}
              type="button"
              className="mx-auto mt-8 w-full   max-w-[318px] bg-primary hover:bg-black"
            >
              {' '}
              <p className="text-[16px] font-[700] text-white">Home Page</p>
            </Button>
          </div>
        </div>
      </div>
    );
}
