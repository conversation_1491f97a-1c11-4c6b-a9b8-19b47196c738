import { <PERSON>actN<PERSON>, useState } from 'react';
import { SubmitHandler } from 'react-hook-form';

import { successIcon } from '../../../../../assets/images';
import Form from '../../../../../components/forms/Form';
import { FormInput } from '../../../../../components/forms/FormInput';
import Button from '../../../../../components/ui/ButtonComponent';
import { Spinner } from '../../../../../components/ui/CommonWidget/Loader';
import { useFormDataAndApiMutateHandler } from '../../../../../hooks/useFormDataAndApiMutateHandler';
import { PasswordHide, PasswordView } from '../../../assets/icons';
import { useAuthEventContext } from '../../../context/event/AuthEventContext';
import { passwordSchema } from '../../../lib/yup/validations';
import { IPasswordPayload } from '../../../types';
import { useSignUpUserForConversation } from '@/features/Authentication/hooks/apiQueryHooks/authQueryHooks';
import { useNavigate } from 'react-router-dom';
import { useHandleQueryParams } from '@/hooks/useHandleQueryParams';

export default function ConversationStepFourConfirmPasswordForm() {
  const { query } = useHandleQueryParams();
  const email = query.get('email');
  const intent = query.get('intent');
  const channelId = query.get('channel_id');
  const joinChannelRequestId = query.get('joinChannelRequestId') || '';
  const navigate = useNavigate();
  const [password, setPassword] = useState(false);
  const [confirmPassword, setConfirmPassword] = useState(false);
  const [showSuccessWidget, setShowSuccessWidget] = useState(false);
  const { conversationTemporaryUser } = useAuthEventContext();

  // TODO: Update this line to use the correct function name
  const next = () => {
    setShowSuccessWidget(true);
    setTimeout(() => {
      intent === 'conversation-channel-invite'
        ? navigate(
            `/conversations/channels/${channelId}/channel-members?notificationType=invite-others-channel-request&joinChannelRequestId=${joinChannelRequestId}`,
          ) // Redirect to conversation channel
        : navigate('/add-conversation');
    }, 2000);
  };
  const {
    register,
    handleSubmit,
    formState: { errors, isDirty, isValid },
    isLoading,
    mutate,
  } = useFormDataAndApiMutateHandler<IPasswordPayload, any>(
    passwordSchema,
    useSignUpUserForConversation,
    {
      next,
    },
  );
  const onSubmit: SubmitHandler<IPasswordPayload> = ({
    password,
    confirmPassword,
  }) => {
    const newPayload = {
      password: password.trim(),
      confirmPassword,
      phoneNumberVerified: false,
      emailVerified: true,
      email: conversationTemporaryUser.email || email || '',
      ...conversationTemporaryUser,
    };
    mutate(newPayload);
  };
  return (
    <>
      {showSuccessWidget ? (
        <div className="max-w-[571px]">
          <div className="mb-[24px] flex items-center justify-center pt-[10%]">
            <img
              className="max-h-full max-w-full"
              src={successIcon}
              alt="success icon display"
            />
          </div>
          <h2 className="mb-2 text-center text-[24px] font-[500] leading-[47px] text-black sm:text-[32px]">
            Account creation successful!
          </h2>
        </div>
      ) : (
        <div className="h-full w-full max-w-[400px]">
          <>
            <h2 className="mb-3 pt-8 text-[24px] font-[500] leading-[47px] sm:text-[32px]">
              Create password
            </h2>
            <p className="pb-12 text-[16px] leading-[27px]">
              Safeguard your account by generating a robust password that
              incorporates a combination of letters, numbers, and symbols.
            </p>
            <Form onSubmit={handleSubmit(onSubmit)} className="w-full">
              <div className="">
                <div className="">
                  <label htmlFor="email">Create Password</label>
                  <div className="relative mt-2">
                    <FormInput
                      className="input "
                      placeholder="Password"
                      autoComplete="password"
                      id="password"
                      registerHanlder={() => register('password')}
                      type={password ? 'text' : 'password'}
                    />
                    <div
                      className="absolute right-6 top-[50%] -translate-y-[50%] cursor-pointer text-black"
                      onClick={() => setPassword(previous => !previous)}
                    >
                      {password ? (
                        <img src={PasswordView} className="h-[24px] w-[24px]" />
                      ) : (
                        <img src={PasswordHide} className="h-[24px] w-[24px]" />
                      )}
                    </div>
                  </div>
                </div>
                <div className="mt-5 ">
                  <label htmlFor="email">Confirm Password</label>
                  <div className="relative mt-2">
                    <FormInput
                      className="input"
                      placeholder="Confirm Password"
                      autoComplete="confirm-password"
                      id="confirmPassword"
                      registerHanlder={() => register('confirmPassword')}
                      type={confirmPassword ? 'text' : 'password'}
                    />
                    <div
                      className="absolute right-6 top-[50%] -translate-y-[50%] cursor-pointer text-black"
                      onClick={() => setConfirmPassword(previous => !previous)}
                    >
                      {confirmPassword ? (
                        <img src={PasswordView} className="h-[24px] w-[24px]" />
                      ) : (
                        <img src={PasswordHide} className="h-[24px] w-[24px]" />
                      )}
                    </div>
                  </div>
                </div>
                {(errors?.confirmPassword || errors?.password) && (
                  <p className="mt-2 pr-8 text-[12px] text-red-800 sm:text-[14px]">
                    {(errors.confirmPassword?.message as ReactNode) ||
                      (errors.password?.message as ReactNode)}
                  </p>
                )}
                <Button
                  type="submit"
                  disabled={!isDirty || !isValid || isLoading}
                  className="mx-auto mt-[32px] w-full   
                max-w-[457px] border border-primary bg-primary text-white disabled:cursor-not-allowed disabled:bg-transparent disabled:text-primary"
                >
                  {isLoading ? (
                    <Spinner className="" />
                  ) : (
                    <p className="text-[14px] font-[700] sm:text-[16px]">
                      Next
                    </p>
                  )}
                </Button>
              </div>
            </Form>
          </>
        </div>
      )}
    </>
  );
}
