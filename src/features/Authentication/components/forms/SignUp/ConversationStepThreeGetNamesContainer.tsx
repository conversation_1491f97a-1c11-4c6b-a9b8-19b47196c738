import { motion } from 'framer-motion';
import { ReactNode } from 'react';

import BrandBanner from '../../../../../components/ui/BrandBanner';
import { useAuthEventContext } from '../../../context/event/AuthEventContext';
import Button from '@/components/ui/ButtonComponent';

type Props = {
  children: ReactNode;
};

export default function ConversationStepThreeGetNamesContainer({
  children,
}: Props) {
  const { conversationSignupLevel, setConversationSignupLevelHandler } =
    useAuthEventContext();
  return conversationSignupLevel === 3 ? (
    <motion.div
      exit={{ opacity: 0, transition: { duration: 0.2 } }}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.2, delay: 0.5, ease: 'easeIn' }}
      className="h-[calc(100%-40px)] overscroll-contain"
    >
      <div className="relative flex h-full flex-col sm:flex-row">
        <BrandBanner />
        <div className="h-full max-h-full flex-1 overflow-y-auto px-4 xs:px-16">
          {children}
        </div>
      </div>
      <div className="absolute bottom-[12%] right-[7%]">
        <Button
          onClick={() => setConversationSignupLevelHandler(2)}
          className="h-[20px] w-full max-w-[100px] bg-primary text-white disabled:bg-transparent disabled:text-primary"
        >
          <p className="text-[10px]">Back</p>
        </Button>
      </div>
    </motion.div>
  ) : null;
}
