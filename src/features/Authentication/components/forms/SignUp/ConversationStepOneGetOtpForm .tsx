import { <PERSON>, SubmitHandler } from 'react-hook-form';
import Checkbox from '../../../../../components/forms/Checkbox';
import Form from '../../../../../components/forms/Form';
import FormInputBox from '../../../../../components/forms/FormInputBox';
import Button from '../../../../../components/ui/ButtonComponent';
import { Spinner } from '../../../../../components/ui/CommonWidget/Loader';
import { useFormDataAndApiQueryHandler } from '../../../../../hooks/useFormDataAndApiQueryHandler';
import { useAuthEventContext } from '../../../context/event/AuthEventContext';
import {
  useGetUnregisteredEmailOTP,
  useVerifyEmailExistence,
} from '../../../hooks/apiQueryHooks/authQueryHooks';
import { conversationSignupStepOneSchema } from '../../../lib/yup/validations';
import {
  IConversationSignupStepOnePayload,
  ISignupPayload,
} from '../../../types';
import ReCAPTCHA from 'react-google-recaptcha';
import { GOOGLE_SITE_KEY } from '@/utils/apiUrls';
import { Helper } from '@/utils/helpers';
import { useEffect } from 'react';
import { DataResponse } from '@/types';
import { Link } from 'react-router-dom';
import { upivotalLogo } from '@/assets/icons';
import { useKeycloak } from '@react-keycloak/web';

export default function ConversationStepOneGetOtpForm() {
  const { keycloak } = useKeycloak();
  const {
    setConversationSignupLevelHandler,
    activateGetOtp,
    setActivateGetOtpHandler,
    setConversationTemporaryUserHandler,
  } = useAuthEventContext();

  const next = () => {
    setActivateGetOtpHandler(false);
    setTimeout(() => {
      setConversationSignupLevelHandler(2);
    }, 1000);
  };
  const onError = () => {
    setActivateGetOtpHandler(false);
  };
  const {
    handleSubmit,
    register,
    isFetching,
    watch,
    control,
    setError,
    formState: { errors, isDirty, isValid },
  } = useFormDataAndApiQueryHandler<IConversationSignupStepOnePayload, any>(
    activateGetOtp,
    conversationSignupStepOneSchema,
    useGetUnregisteredEmailOTP,
    {
      next,
      onError,
      defaultValues: {},
    },
  );

  const watchedEmail = watch('email');

  const { mutate } = useVerifyEmailExistence({
    onSuccess: (res: DataResponse<any>) => {
      if (res.data.valid) {
        setError('email', {
          type: 'manual',
          message:
            'Email already exists. Please sign in below or use another email address.',
        });
      }
    },
  });

  useEffect(() => {
    const delayTimer = setTimeout(() => {
      if (watchedEmail) {
        mutate(watchedEmail);
      }
    }, 1000);
    return () => clearTimeout(delayTimer);
  }, [mutate, watchedEmail]);

  const onSubmit: SubmitHandler<IConversationSignupStepOnePayload> = data => {
    setConversationTemporaryUserHandler(Helper.clearEmptyField(data));
    setActivateGetOtpHandler(true);
  };

  return (
    <div
      className="relative h-full pb-8 sm:pb-20
   "
    >
      <Link to="/">
        <div className="hidden w-full max-w-[117px]  items-center max-sm:inline">
          <img loading="lazy" src={upivotalLogo} className={`max-w-full`} />
        </div>
      </Link>
      <div className="flex flex-col pt-8 max-sm:h-[95%] sm:pt-4">
        <p className="mb-[24px] text-[24px] font-[500] sm:text-[32px]">
          Sign Up
        </p>
        <p className="max-w-[503px] text-[16px]">
          Get started with your innovation collaboration journey. Sign up today!
        </p>
        <Form
          onSubmit={handleSubmit(onSubmit)}
          className="w-full max-w-[400px]"
        >
          <div className="mt-5">
            <div className="mt-5">
              <FormInputBox<ISignupPayload>
                className="input mt-2"
                name="email"
                errors={errors}
                labelName="Email Address"
                placeholder="Email Address"
                type="email"
                autoComplete="email"
                registerHanlder={() => register('email')}
              />
            </div>
            <div className="mt-5 ">
              <Controller
                name="captcha"
                control={control}
                render={({ field }) => (
                  <ReCAPTCHA
                    sitekey={GOOGLE_SITE_KEY}
                    onChange={v => field.onChange(v)}
                  />
                )}
              />
            </div>
            <div className="mt-5">
              <div className="flex items-center gap-x-2">
                <Checkbox
                  className="checkbox"
                  id="terms"
                  registerHanlder={() => register('terms')}
                />
                <p className="text-[14px] font-[400]">
                  By creating an account, you agree to uPivotal's <br />
                  <a
                    href="/terms-and-conditions"
                    rel="noopener noreferrer"
                    target="_blank"
                  >
                    <span className="cursor-pointer text-primary underline">
                      Terms and Conditions
                    </span>
                  </a>
                </p>
              </div>
            </div>
            <Button
              type="submit"
              disabled={!isDirty || !isValid || isFetching || !!errors.email}
              className="mx-auto mt-[32px] w-full 
              max-w-[400px] border   
              border-primary bg-primary text-white disabled:cursor-not-allowed disabled:bg-transparent disabled:text-primary"
            >
              {isFetching ? (
                <Spinner className="" />
              ) : (
                <p className={` text-[14px] font-[700] sm:text-[16px]`}>Next</p>
              )}
            </Button>
          </div>
        </Form>
        <div className="mt-[32px] w-full max-w-[400px]">
          <p className="text-center text-[14px] font-[500] text-primary">
            Already have an account?
          </p>
          <Button
            type="button"
            onClick={() => {
              keycloak.login({
                redirectUri: `${window.location.origin}/conversations/feed`,
              });
            }}
            className="mx-auto
             mt-2 w-full max-w-[400px] bg-primary"
          >
            <p className="text-[16px] font-[700] text-white">Sign In</p>
          </Button>
        </div>
      </div>
    </div>
  );
}
