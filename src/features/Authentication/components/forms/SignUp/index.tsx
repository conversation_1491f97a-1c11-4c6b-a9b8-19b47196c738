import { AnimatePresence } from 'framer-motion';
import {
  AuthEventProvider,
  useAuthEventContext,
} from '../../../context/event/AuthEventContext';
import StepOneGetOtpContainer from './StepOneGetOtpContainer';
import StepOneGetOtpForm from './StepOneGetOtpForm';
import StepThreeConfirmPasswordContainer from './StepThreeConfirmPasswordContainer';
import { useHandleQueryParams } from '@/hooks/useHandleQueryParams';
import ConversationStepOneGetOtpContainer from './ConversationStepOneGetOtpContainer';
import ConversationStepOneGetOtpForm from './ConversationStepOneGetOtpForm ';
import ConversationStepTwoVerifyOtpContainer from './ConversationStepTwoVerifyOtpContainer';
import ConversationStepTwoVerifyOtpForm from './ConversationStepTwoVerifyOtpForm';
import ConversationStepThreeGetNamesContainer from './ConversationStepThreeGetNamesContainer';
import ConversationStepThreeGetNamesForm from './ConversationStepThreeGetNamesForm';
import ConversationStepFourConfirmPasswordContainer from './ConversationStepFourConfirmPasswordContainer';
import ConversationStepFourConfirmPasswordForm from './ConversationStepFourConfirmPasswordForm';
import StepTwoConfirmPasswordForm from './StepThreeConfirmPasswordForm';
import { useCallback, useEffect } from 'react';
import StepTwoVerifyOTPContainer from './StepTwoVerifyOtpContainer';
import StepTwoVerifyOTPForm from './StepTwoVerifyOtpForm';

export default function SignupForm() {
  return (
    <AuthEventProvider>
      <div
        className="mx-auto h-screen
        overflow-hidden py-10 sm:p-10"
      >
        <AnimatePresence>
          <SignupFormContainer />
        </AnimatePresence>
      </div>
    </AuthEventProvider>
  );
}

const SignupFormContainer = () => {
  const { query } = useHandleQueryParams();
  const intent = query.get('intent');
  const { setConversationSignupLevelHandler } = useAuthEventContext();
  useEffect(() => {
    if (intent === 'conversation-channel-invite')
      setConversationSignupLevelHandler(3);
  }, [intent]);
  const render = useCallback(() => {
    switch (intent) {
      case 'conversation-channel': {
        return (
          <>
            <ConversationStepOneGetOtpContainer key="stepOne">
              <ConversationStepOneGetOtpForm />
            </ConversationStepOneGetOtpContainer>
            <ConversationStepTwoVerifyOtpContainer key="stepTwo">
              <ConversationStepTwoVerifyOtpForm />
            </ConversationStepTwoVerifyOtpContainer>
            <ConversationStepThreeGetNamesContainer key="stepThree">
              <ConversationStepThreeGetNamesForm />
            </ConversationStepThreeGetNamesContainer>
            <ConversationStepFourConfirmPasswordContainer key="stepFour">
              <ConversationStepFourConfirmPasswordForm />
            </ConversationStepFourConfirmPasswordContainer>
          </>
        );
      }
      case 'conversation-channel-invite': {
        return (
          <>
            <ConversationStepThreeGetNamesContainer key="stepThree">
              <ConversationStepThreeGetNamesForm />
            </ConversationStepThreeGetNamesContainer>
            <ConversationStepFourConfirmPasswordContainer key="stepFour">
              <ConversationStepFourConfirmPasswordForm />
            </ConversationStepFourConfirmPasswordContainer>
          </>
        );
      }
      default: {
        return (
          <>
            <StepOneGetOtpContainer key="stepOne">
              <StepOneGetOtpForm />
            </StepOneGetOtpContainer>
            <StepTwoVerifyOTPContainer key="stepTwo">
              <StepTwoVerifyOTPForm />
            </StepTwoVerifyOTPContainer>
            <StepThreeConfirmPasswordContainer key="stepThree">
              <StepTwoConfirmPasswordForm />
            </StepThreeConfirmPasswordContainer>
          </>
        );
      }
    }
  }, [intent]);
  return <>{render()}</>;
};
