import { motion } from 'framer-motion';
import { ReactNode } from 'react';

import BrandBanner from '../../../../../components/ui/BrandBanner';
import Button from '../../../../../components/ui/ButtonComponent';
import useScrollToTop from '../../../../../hooks/useScrollToTop';
import { useAuthEventContext } from '../../../context/event/AuthEventContext';

type Props = {
  children: ReactNode;
};

export default function StepThreeConfirmPasswordContainer({ children }: Props) {
  useScrollToTop();
  const { signupLevel, setSignupLevelHandler } = useAuthEventContext();
  return signupLevel === 3 ? (
    <motion.div
      exit={{ opacity: 0, transition: { duration: 0.2 } }}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.2, delay: 0.5, ease: 'easeIn' }}
      className="h-full"
    >
      <div className="relative flex h-full flex-col sm:flex-row">
        {' '}
        <BrandBanner />
        <div className="h-full max-h-screen flex-1 px-4 xs:px-16">
          {children}
        </div>
      </div>
      <div className="absolute bottom-[12%] right-[7%]">
        <Button
          onClick={() => setSignupLevelHandler(2)}
          className="h-[20px] w-full max-w-[100px] bg-primary text-white disabled:bg-transparent disabled:text-primary"
        >
          <p className="text-[10px]">Back</p>
        </Button>
      </div>
    </motion.div>
  ) : null;
}
