import { SubmitHandler, useForm } from 'react-hook-form';
import Form from '../../../../../components/forms/Form';
import FormInputBox from '../../../../../components/forms/FormInputBox';
import Button from '../../../../../components/ui/ButtonComponent';
import { useAuthEventContext } from '../../../context/event/AuthEventContext';
import { conversationSignupStepThreeSchema } from '../../../lib/yup/validations';
import {
  IConversationSignupStepThreePayload,
  ISignupPayload,
} from '../../../types';
import { yupResolver } from '@hookform/resolvers/yup';

export default function ConversationStepThreeGetNamesForm() {
  const {
    setConversationSignupLevelHandler,
    setConversationTemporaryUserHandler,
  } = useAuthEventContext();

  const next = (data: IConversationSignupStepThreePayload) => {
    setConversationTemporaryUserHandler(prev => ({ ...prev, ...data }));
    setTimeout(() => {
      setConversationSignupLevelHandler(4);
    }, 1000);
  };

  const {
    handleSubmit,
    register,
    formState: { errors, isDirty, isValid },
  } = useForm({
    defaultValues: {},
    resolver: yupResolver(conversationSignupStepThreeSchema),
    mode: 'onChange',
  });

  const onSubmit: SubmitHandler<IConversationSignupStepThreePayload> = data => {
    next(data);
  };
  return (
    <div
      className="h-full pb-8 sm:pb-20
   "
    >
      <div className="pt-4">
        <p className="mb-[24px] text-[24px] font-[500] sm:text-[32px]">
          Sign Up
        </p>
        <p className="max-w-[503px] text-[16px]">
          Get started with your innovation collaboration journey. Sign up today!
        </p>
        <Form
          onSubmit={handleSubmit(onSubmit)}
          className="w-full max-w-[400px]"
        >
          <div className="mt-5">
            <div className="mt-5">
              <FormInputBox<ISignupPayload>
                className="input mt-2"
                name="firstName"
                errors={errors}
                labelName="First Name"
                placeholder="First Name"
                type="text"
                autoComplete="first-name"
                registerHanlder={() => register('firstName')}
              />
            </div>
            <div className="mt-5">
              <FormInputBox<ISignupPayload>
                className="input mt-2"
                name="lastName"
                errors={errors}
                labelName="Last Name"
                placeholder="Last Name"
                type="text"
                autoComplete="last-name"
                registerHanlder={() => register('lastName')}
              />
            </div>
            <Button
              type="submit"
              disabled={!isDirty || !isValid}
              className="mx-auto mt-[32px] w-full 
              max-w-[400px] border   
              border-primary bg-primary text-white disabled:cursor-not-allowed disabled:bg-transparent disabled:text-primary"
            >
              <p className={` text-[14px] font-[700] sm:text-[16px]`}>Next</p>
            </Button>
          </div>
        </Form>
      </div>
    </div>
  );
}
