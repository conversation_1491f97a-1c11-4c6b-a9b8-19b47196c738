import 'react-phone-input-2/lib/bootstrap.css';

import { useEffect } from 'react';
import <PERSON>CA<PERSON><PERSON>HA from 'react-google-recaptcha';
import { Controller, SubmitHandler } from 'react-hook-form';
import { <PERSON>, useNavigate } from 'react-router-dom';

import Checkbox from '../../../../../components/forms/Checkbox';
import Form from '../../../../../components/forms/Form';
import FormInputBox from '../../../../../components/forms/FormInputBox';
import FormLabel from '../../../../../components/forms/FormLabel';
import FormSelectBox from '../../../../../components/forms/FormSelectBox';
import PhoneInputBox from '../../../../../components/forms/PhoneInputBox';
import Button from '../../../../../components/ui/ButtonComponent';
import { Spinner } from '../../../../../components/ui/CommonWidget/Loader';
import { useFormDataAndApiQueryHandler } from '../../../../../hooks/useFormDataAndApiQueryHandler';
import { GOOGLE_SITE_KEY } from '../../../../../utils/apiUrls';
import { useAuthEventContext } from '../../../context/event/AuthEventContext';
import { titleOptionsArr as titleOptionsArray } from '../../../data';
import {
  useGetOTP,
  useVerifyEmailExistence,
} from '../../../hooks/apiQueryHooks/authQueryHooks';
import { signupStepOneSchema } from '../../../lib/yup/validations';
import { ISignupPayload } from '../../../types';
import useSubscriptionTypeHandler from '@/features/Authentication/hooks/useSubscriptionTypeHandler';
import { DataResponse } from '@/types';
import { upivotalLogo } from '@/assets/icons';
import { useKeycloak } from '@react-keycloak/web';
import { useAppContext } from '@/context/event/AppEventContext';
import { mapCurrentAccountTypeToRedirectUrlProp } from '@/utils/helpers/mapCurrentAccountTypeToRedirectUrlProp';

export default function StepOneGetOtpForm() {
  const { keycloak } = useKeycloak();
  const { currentAccountType } = useAppContext();
  const navigate = useNavigate();
  const { handleDefaultOrFromPriceRoute, s_type } =
    useSubscriptionTypeHandler();
  useEffect(() => {
    if (s_type) return;
    navigate('/signup');
  }, []);
  const {
    setSignupLevelHandler,
    activateGetOtp,
    setActivateGetOtpHandler,
    temporaryUser,
  } = useAuthEventContext();

  const next = () => {
    setActivateGetOtpHandler(false);
    setTimeout(() => {
      setSignupLevelHandler(2);
    }, 2000);
  };
  const onError = () => {
    setActivateGetOtpHandler(false);
  };
  const {
    handleSubmit,
    register,
    control,
    isFetching,
    watch,
    setError,
    formState: { errors, isDirty, isValid },
  } = useFormDataAndApiQueryHandler<ISignupPayload, any>(
    activateGetOtp,
    signupStepOneSchema,
    useGetOTP,
    {
      next,
      onError,
      defaultValues: {
        title: temporaryUser.title,
        firstName: temporaryUser.firstName,
        lastName: temporaryUser.lastName,
        email: temporaryUser.email,
        phoneNumber: temporaryUser.phoneNumber,
      },
    },
  );

  const watchedEmail = watch('email');

  const { mutate } = useVerifyEmailExistence({
    onSuccess: (res: DataResponse<any>) => {
      if (res.data.valid)
        setError('email', {
          type: 'manual',
          message:
            'Email already exists. Please sign in below or use another email address.',
        });
    },
  });

  useEffect(() => {
    const delayTimer = setTimeout(() => {
      if (watchedEmail) {
        mutate(watchedEmail);
      }
    }, 1000);
    return () => clearTimeout(delayTimer);
  }, [mutate, watchedEmail]);

  const onSubmit: SubmitHandler<ISignupPayload> = data => {
    handleDefaultOrFromPriceRoute(data, s_type);
  };
  return (
    <div className="pb-8 sm:pb-20">
      <Link to="/">
        <div className="hidden w-full max-w-[117px]  items-center max-sm:inline">
          <img loading="lazy" src={upivotalLogo} className={`max-w-full`} />
        </div>
      </Link>
      <div className="pt-4 max-sm:pt-8">
        <p className="mb-[24px] text-[24px] font-[500] sm:text-[32px]">
          Sign Up
        </p>
        <p className="max-w-[503px] text-[16px]">
          Don't miss out on this chance to enhance your learning journey! Sign
          up today
        </p>
        <Form
          onSubmit={handleSubmit(onSubmit)}
          className="w-full max-w-[400px]"
        >
          <div className="mt-5">
            <div className="">
              <FormSelectBox<ISignupPayload>
                options={titleOptionsArray}
                optionsArr={titleOptionsArray}
                control={control}
                name="title"
                errors={{}}
                placeholder="Select title"
                formLabel={<FormLabel labelName="Title" name="title" />}
              />
            </div>
            <div className="mt-5">
              <FormInputBox<ISignupPayload>
                className="input mt-2"
                name="firstName"
                errors={errors}
                labelName="First Name"
                placeholder="First Name"
                type="text"
                autoComplete="first-name"
                registerHanlder={() => register('firstName')}
              />
            </div>
            <div className="mt-5">
              <FormInputBox<ISignupPayload>
                className="input mt-2"
                name="lastName"
                errors={errors}
                labelName="Last Name"
                placeholder="Last Name"
                type="text"
                autoComplete="last-name"
                registerHanlder={() => register('lastName')}
              />
            </div>
            <div className="mt-5">
              <FormInputBox<ISignupPayload>
                className="input mt-2"
                name="email"
                errors={errors}
                labelName="Email Address"
                placeholder="Email Address"
                type="email"
                autoComplete="email"
                registerHanlder={() => register('email')}
              />
            </div>
            <div className="mt-5 ">
              <PhoneInputBox<ISignupPayload>
                errors={errors}
                name="phoneNumber"
                control={control}
                labelName="Phone Number"
              />
            </div>
            <div className="mt-5 ">
              <Controller
                name="captcha"
                control={control}
                render={({ field }) => (
                  <ReCAPTCHA
                    sitekey={GOOGLE_SITE_KEY}
                    onChange={v => field.onChange(v)}
                  />
                )}
              />
            </div>
            <div className="mt-5">
              <div className="flex items-center gap-x-2">
                <Checkbox
                  className="checkbox"
                  id="terms"
                  registerHanlder={() => register('terms')}
                />
                <p className="text-[14px] font-[400]">
                  By creating an account, you agree to uPivotal's <br />
                  <a
                    href="/terms-and-conditions"
                    rel="noopener noreferrer"
                    target="_blank"
                  >
                    <span className="cursor-pointer text-primary underline">
                      Terms and Conditions
                    </span>
                  </a>
                </p>
              </div>
            </div>
            <Button
              type="submit"
              disabled={!isDirty || !isValid || isFetching || !!errors.email}
              className="mx-auto mt-[32px] w-full 
              max-w-[400px] border   
              border-primary bg-primary text-white disabled:cursor-not-allowed disabled:bg-transparent disabled:text-primary"
            >
              {isFetching ? (
                <Spinner className="" />
              ) : (
                <p className={`text-[14px] font-[700] sm:text-[16px]`}>Next</p>
              )}
            </Button>
          </div>
        </Form>
        <div className="mt-[32px] w-full max-w-[400px]">
          <p className="text-center text-[14px] font-[500] text-primary">
            Already have an account?
          </p>
          <Button
            type="button"
            onClick={() => {
              keycloak.login(
                mapCurrentAccountTypeToRedirectUrlProp(currentAccountType),
              );
            }}
            className="mx-auto
             mt-2 w-full max-w-[400px] bg-primary"
          >
            <p className="text-[16px] font-[700] text-white">Sign In</p>
          </Button>
        </div>
      </div>
    </div>
  );
}
