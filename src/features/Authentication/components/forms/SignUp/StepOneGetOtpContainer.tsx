import { motion } from 'framer-motion';
import { ReactNode } from 'react';

import BrandBanner from '../../../../../components/ui/BrandBanner';
import { useAuthEventContext } from '../../../context/event/AuthEventContext';

type Props = {
  children: ReactNode;
};

export default function StepOneGetOtpContainer({ children }: Props) {
  const { signupLevel } = useAuthEventContext();
  return signupLevel === 1 ? (
    <motion.div
      exit={{ opacity: 0, transition: { duration: 0.2 } }}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.2, delay: 0.5, ease: 'easeIn' }}
      className="h-[calc(100%-40px)] overscroll-contain"
    >
      <div className="relative flex h-full flex-col sm:flex-row">
        {' '}
        <BrandBanner />
        <div className="h-full max-h-full flex-1 overflow-y-auto px-4 xs:px-16">
          {children}
        </div>
      </div>
    </motion.div>
  ) : null;
}
