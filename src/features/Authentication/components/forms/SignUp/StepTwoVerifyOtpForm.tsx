import parsePhoneNumber from 'libphonenumber-js';
import { useEffect } from 'react';
import { SubmitHandler } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';

import Form from '../../../../../components/forms/Form';
import Button from '../../../../../components/ui/ButtonComponent';
import { Spinner } from '../../../../../components/ui/CommonWidget/Loader';
import { useAutoInputFocus } from '../../../../../hooks/useAutoInputFocus';
import { useDisableResendOtp } from '../../../../../hooks/useDisableResendOtp';
import { useFormDataAndApiMutateHandler } from '../../../../../hooks/useFormDataAndApiMutateHandler';
import { useAuthEventContext } from '../../../context/event/AuthEventContext';
import { useVerifyPhoneNumber } from '../../../hooks/apiQueryHooks/authQueryHooks';
import { otpSchema } from '../../../lib/yup/validations';
import { PinPayload } from '../../../types';
import { useHandleQueryParams } from '@/hooks/useHandleQueryParams';
import useGetUnregisteredPhoneOTPHook from '../../../hooks/useGetUnregisteredPhoneOTPHook';

export default function StepTwoVerifyOTPForm() {
  const navigate = useNavigate();
  useEffect(() => {
    navigate('/signup?n_t=2');
  }, []);
  const { handleQuery } = useHandleQueryParams();
  const { canSendOtp, toggleCanSendOtp, countdown } = useDisableResendOtp({
    initialState: false,
  });

  const { inputsRef } = useAutoInputFocus();
  const {
    setSignupLevelHandler,
    setActivateGetOtpHandler,
    temporaryUser,
    setTemporaryUserHandler,
  } = useAuthEventContext();
  const prev = () => {
    handleQuery({ edit_phone_no: 'true' });
    setTimeout(() => {
      setSignupLevelHandler(1);
    }, 2000);
  };
  const next = () => {
    setTemporaryUserHandler(prev => ({ ...prev, phoneNumberVerified: true }));
    setTimeout(() => {
      setSignupLevelHandler(3);
    }, 2000);
  };
  const skipVerifyNextAction = () => {
    setTemporaryUserHandler(prev => ({ ...prev, phoneNumberVerified: false }));
    setTimeout(() => {
      setSignupLevelHandler(3);
    }, 2000);
  };
  const {
    mutate,
    register,
    handleSubmit,
    isLoading,
    formState: { isDirty, isValid },
  } = useFormDataAndApiMutateHandler<PinPayload, any>(
    otpSchema,
    useVerifyPhoneNumber,
    {
      next,
    },
  );
  const onSubmit: SubmitHandler<PinPayload> = data => {
    mutate(data);
  };
  useGetUnregisteredPhoneOTPHook();

  const handleRefetchOtp = () => {
    if (!canSendOtp) {
      return;
    }
    setActivateGetOtpHandler(true);
    toggleCanSendOtp();
  };
  return (
    <>
      <div className="w-full max-w-[700px] px-4">
        <div className="w-full">
          <div className="mx-auto mb-[40px] max-w-[400px] sm:mb-[61px]">
            <h2 className="mb-6 text-center text-[20px] font-[500] text-black sm:text-[32px]">
              We Sent You A Code
            </h2>
            <p
              className="text-center text-[14px] leading-[18px] text-subText 
              sm:text-[16px] sm:leading-[27px] "
            >
              Enter the 6-digit code sent to{' '}
              <span className="whitespace-nowrap">
                {temporaryUser
                  ? parsePhoneNumber(
                      `+${temporaryUser.phoneNumber}`,
                    )?.formatInternational()
                  : ' --'}{' '}
              </span>
              to verify your identity
            </p>
          </div>
          <Form
            onSubmit={handleSubmit(onSubmit)}
            className="mx-auto w-full max-w-[500px]"
          >
            <div className="flex justify-between gap-x-2">
              {(
                [
                  'pinOne',
                  'pinTwo',
                  'pinThree',
                  'pinFour',
                  'pinFive',
                  'pinSix',
                ] as const
              ).map((pinName, index) => {
                const { ref, ...rest } = register(pinName);
                return (
                  <input
                    pattern="[0-9]*"
                    autoComplete="off"
                    inputMode="numeric"
                    key={pinName + index}
                    className="inputPin text-center text-[18px] sm:text-[28px]"
                    maxLength={1}
                    type="password"
                    {...rest}
                    ref={e => {
                      ref(e);
                      inputsRef.current[index] = e!;
                    }}
                  />
                );
              })}
            </div>
            <div className="mt-4 flex flex-col items-center justify-center gap-4">
              <div className="flex items-center justify-center gap-2">
                <div>
                  <p>Didn't get a message?</p>
                  {!canSendOtp && (
                    <p className="text-[12px]">Try again in {countdown}</p>
                  )}
                </div>
                <div className="flex items-center">
                  <Button
                    disabled={!canSendOtp}
                    onClick={handleRefetchOtp}
                    type="button"
                    className="group mx-auto w-full   
                max-w-[123px] border border-primary bg-primary bg-transparent text-white hover:bg-primary"
                  >
                    <p className="text-[14px] leading-8 text-primary group-hover:text-white">
                      Resend Code
                    </p>
                  </Button>
                </div>
              </div>
              <div className="flex flex-col items-center">
                <p className="mb-1">or</p>
                <div className="flex items-center gap-x-3">
                  <Button
                    onClick={prev}
                    type="button"
                    className="group mx-auto w-full   
                  max-w-[193px] border border-primary bg-primary bg-transparent text-white hover:bg-primary"
                  >
                    <p className="text-[14px] leading-8 text-primary group-hover:text-white">
                      Try a different number
                    </p>
                  </Button>
                </div>
              </div>
              <div className="flex flex-col items-center">
                <p className="mb-1">or</p>
                <div className="flex items-center gap-x-3">
                  <Button
                    onClick={skipVerifyNextAction}
                    type="button"
                    className="group mx-auto w-full   
                  max-w-[177px] border border-primary bg-primary bg-transparent text-white hover:bg-primary"
                  >
                    <p className="text-[14px] leading-8 text-primary group-hover:text-white">
                      Skip and verify later
                    </p>
                  </Button>
                </div>
              </div>
            </div>
            <Button
              type="submit"
              disabled={!isDirty || !isValid || isLoading}
              className="mx-auto mt-[40px] w-full   
                max-w-[400px] border border-primary bg-primary text-white disabled:cursor-not-allowed disabled:bg-transparent disabled:text-primary"
            >
              {isLoading ? (
                <Spinner className="" />
              ) : (
                <p className="text-[14px] font-[700] sm:text-[16px]">Next</p>
              )}
            </Button>
          </Form>
        </div>
      </div>
    </>
  );
}
