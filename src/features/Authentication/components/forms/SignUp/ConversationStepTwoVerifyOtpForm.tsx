import { SubmitHandler } from 'react-hook-form';
import Form from '../../../../../components/forms/Form';
import Button from '../../../../../components/ui/ButtonComponent';
import { Spinner } from '../../../../../components/ui/CommonWidget/Loader';
import { useAutoInputFocus } from '../../../../../hooks/useAutoInputFocus';
import { useDisableResendOtp } from '../../../../../hooks/useDisableResendOtp';
import { useAuthEventContext } from '../../../context/event/AuthEventContext';
import { useVerifyUnregisteredEmail } from '../../../hooks/apiQueryHooks/authQueryHooks';
import useGetUnregisteredEmailOTPHook from '../../../hooks/useGetUnregisteredPhoneOTPHook';
import { otpSchema } from '../../../lib/yup/validations';
import { PinPayload } from '../../../types';
import { useFormDataAndApiQueryHandler } from '@/hooks/useFormDataAndApiQueryHandler';
import { useState } from 'react';

export default function ConversationStepTwoVerifyOtpForm() {
  const [activateVerifyEmail, setActivateVerifyEmail] = useState(false);
  const { canSendOtp, toggleCanSendOtp, countdown } = useDisableResendOtp({
    initialState: false,
  });
  const { inputsRef } = useAutoInputFocus();
  const {
    setConversationSignupLevelHandler,
    setActivateGetOtpHandler,
    conversationTemporaryUser,
    setConversationTemporaryUserHandler,
  } = useAuthEventContext();

  const next = () => {
    setActivateVerifyEmail(false);
    setConversationTemporaryUserHandler(prev => ({ ...prev }));
    setTimeout(() => {
      setConversationSignupLevelHandler(3);
    }, 2000);
  };

  const onError = () => {
    setActivateVerifyEmail(false);
  };

  const {
    register,
    handleSubmit,
    isFetching,
    formState: { isDirty, isValid },
  } = useFormDataAndApiQueryHandler<any, any>(
    activateVerifyEmail,
    otpSchema,
    useVerifyUnregisteredEmail,
    {
      next,
      onError,
    },
  );
  const onSubmit: SubmitHandler<PinPayload> = () => {
    setActivateVerifyEmail(true);
  };

  useGetUnregisteredEmailOTPHook();

  const handleRefetchOtp = () => {
    if (!canSendOtp) {
      return;
    }
    setActivateGetOtpHandler(true);
    toggleCanSendOtp();
  };

  return (
    <>
      <div className="w-full max-w-[700px] px-4">
        <div className="w-full">
          <div className="mx-auto mb-[40px] max-w-[456px] sm:mb-[61px]">
            <h2 className="mb-6 text-center text-[20px] font-[500] text-black sm:text-[32px]">
              We Sent You A Code
            </h2>
            <p
              className="text-center text-[14px] leading-[18px] text-subText 
              sm:text-[16px] sm:leading-[27px] "
            >
              Enter the 6-digit code sent to{' '}
              <span className="whitespace-nowrap">
                {conversationTemporaryUser.email}{' '}
              </span>
              to verify your identity
            </p>
          </div>
          <Form
            onSubmit={handleSubmit(onSubmit)}
            className="mx-auto w-full max-w-[500px]"
          >
            <div className="flex justify-between gap-x-2">
              {(
                [
                  'pinOne',
                  'pinTwo',
                  'pinThree',
                  'pinFour',
                  'pinFive',
                  'pinSix',
                ] as const
              ).map((pinName, index) => {
                const { ref, ...rest } = register(pinName);
                return (
                  <input
                    pattern="[0-9]*"
                    autoComplete="off"
                    inputMode="numeric"
                    key={pinName + index}
                    className="inputPin text-center text-[18px] sm:text-[28px]"
                    maxLength={1}
                    type="password"
                    {...rest}
                    ref={e => {
                      ref(e);
                      inputsRef.current[index] = e!;
                    }}
                  />
                );
              })}
            </div>
            <p className="mt-5">
              Didn't get a text?{' '}
              <span
                onClick={handleRefetchOtp}
                className={`${
                  canSendOtp
                    ? 'cursor-pointer text-primary underline'
                    : 'text-[14px] text-grayTen'
                }`}
              >
                Resend Code
              </span>
            </p>
            {!canSendOtp && (
              <p className="text-[12px]">Try again in {countdown}</p>
            )}
            <Button
              type="submit"
              disabled={!isDirty || !isValid || isFetching}
              className="mx-auto mt-[40px] w-full   
                max-w-[400px] border border-primary bg-primary text-white disabled:cursor-not-allowed disabled:bg-transparent disabled:text-primary"
            >
              {isFetching ? (
                <Spinner className="" />
              ) : (
                <p className="text-[14px] font-[700] sm:text-[16px]">Next</p>
              )}
            </Button>
          </Form>
        </div>
      </div>
    </>
  );
}
