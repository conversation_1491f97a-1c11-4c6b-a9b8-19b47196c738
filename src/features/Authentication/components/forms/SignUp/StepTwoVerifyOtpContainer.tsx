import { motion } from 'framer-motion';
import { ReactNode } from 'react';
import { Link } from 'react-router-dom';

import { upivotalLogo } from '../../../../../assets/icons';
import bgImage from '../../../../../assets/images/onboardingBgImage.png';
import Button from '../../../../../components/ui/ButtonComponent';
import useScrollToTop from '../../../../../hooks/useScrollToTop';
import { useAuthEventContext } from '../../../context/event/AuthEventContext';

type Props = {
  children: ReactNode;
};

export default function StepTwoVerifyOTPContainer({ children }: Props) {
  useScrollToTop();
  const { signupLevel, setSignupLevelHandler } = useAuthEventContext();
  return signupLevel === 2 ? (
    <motion.div
      exit={{ opacity: 0, transition: { duration: 0.2 } }}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.2, delay: 0.5, ease: 'easeIn' }}
      style={{ backgroundImage: `url(${bgImage})` }}
      className="relative mx-auto flex h-screen max-w-full items-center justify-center overflow-hidden bg-cover"
    >
      <Link className="absolute left-[7%] top-[5%]" to="/">
        <div className="min-w-[80px] max-w-[117px]">
          <img loading="lazy" src={upivotalLogo} className={` h-full w-full`} />
        </div>
      </Link>
      {children}
      <div className="absolute bottom-[10%] right-[7%]">
        <Button
          onClick={() => setSignupLevelHandler(1)}
          className="h-[20px] w-full max-w-[100px] bg-primary text-white disabled:bg-transparent disabled:text-primary"
        >
          <p className="text-[10px]">Back</p>
        </Button>
      </div>
    </motion.div>
  ) : null;
}
