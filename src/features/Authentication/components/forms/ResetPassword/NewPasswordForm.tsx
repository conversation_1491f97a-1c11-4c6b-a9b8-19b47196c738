import { useKeycloak } from '@react-keycloak/web';
import { ReactNode, useState } from 'react';
import { SubmitHandler } from 'react-hook-form';
import { useParams } from 'react-router-dom';

import { SuccessIcon } from '../../../../../assets/icons';
import Form from '../../../../../components/forms/Form';
import { FormInput } from '../../../../../components/forms/FormInput';
import Button from '../../../../../components/ui/ButtonComponent';
import { Spinner } from '../../../../../components/ui/CommonWidget/Loader';
import { useAppContext } from '../../../../../context/event/AppEventContext';
import { useFormDataAndApiMutateHandler } from '../../../../../hooks/useFormDataAndApiMutateHandler';
import { PasswordHide, PasswordView } from '../../..';
import { useResetPassword } from '../../../hooks/apiQueryHooks/authQueryHooks';
import { passwordSchema } from '../../../lib/yup/validations';
import { IPasswordPayload } from '../../../types';
import { mapCurrentAccountTypeToRedirectUrlProp } from '@/utils/helpers/mapCurrentAccountTypeToRedirectUrlProp';

export default function NewPasswordForm() {
  const { keycloak } = useKeycloak();
  const { token } = useParams();
  const [password, setPassword] = useState(false);
  const [confirmPassword, setConfirmPassword] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const { currentAccountType } = useAppContext();
  const next = () => {
    setShowSuccess(true);
  };
  const {
    register,
    handleSubmit,
    formState: { errors, isDirty, isValid },
    isLoading,
    mutate,
  } = useFormDataAndApiMutateHandler<IPasswordPayload, any>(
    passwordSchema,
    useResetPassword,
    {
      next,
    },
  );
  const onSubmit: SubmitHandler<IPasswordPayload> = ({
    password,
    confirmPassword,
  }) => {
    const newPayload = {
      token: token as string,
      password: password.trim(),
      confirmPassword,
    };
    mutate(newPayload);
  };
  return (
    <div className="flex flex-col md:flex-row">
      <div className="mx-auto mt-8 h-full w-full max-w-[400px] px-5 py-8 sm:py-20">
        {showSuccess ? (
          <div className="flex flex-col items-center pt-12 sm:pt-24">
            <SuccessIcon className="mb-8" />
            <p className="text-[20px] font-semibold">
              Password reset successfully
            </p>
            <Button
              onClick={() => {
                keycloak.login(
                  mapCurrentAccountTypeToRedirectUrlProp(currentAccountType),
                );
              }}
              type="button"
              className="mt-5 w-full   max-w-[318px] bg-transparent 
              text-primary hover:bg-primary hover:text-white"
            >
              <p className="text-[16px] font-[700]">Login</p>
            </Button>
          </div>
        ) : (
          <>
            <h2 className="mb-1 pt-12 text-center text-[20px] font-[500] leading-[47px] sm:pt-24 sm:text-[32px]">
              Create Password
            </h2>
            <p className="text-center text-[16px] leading-[27px]">
              Create a password for your upivotal account with the details you
              have provided
            </p>
            <Form onSubmit={handleSubmit(onSubmit)} className="mt-7 w-full">
              <div className="">
                <div className="">
                  <label htmlFor="email">Create Password</label>
                  <div className="relative mt-2">
                    <FormInput
                      className="input "
                      placeholder="Password"
                      autoComplete="password"
                      id="password"
                      registerHanlder={() => register('password')}
                      type={password ? 'text' : 'password'}
                    />
                    <div
                      className="absolute right-6 top-[50%] -translate-y-[50%] cursor-pointer text-black"
                      onClick={() => setPassword(previous => !previous)}
                    >
                      {password ? (
                        <img src={PasswordView} className="h-[24px] w-[24px]" />
                      ) : (
                        <img src={PasswordHide} className="h-[24px] w-[24px]" />
                      )}
                    </div>
                  </div>
                </div>
                <div className="mt-5 ">
                  <label htmlFor="email">Confirm Password</label>
                  <div className="relative mt-2">
                    <FormInput
                      className="input"
                      placeholder="Confirm Password"
                      autoComplete="confirm-password"
                      id="confirmPassword"
                      registerHanlder={() => register('confirmPassword')}
                      type={confirmPassword ? 'text' : 'password'}
                    />
                    <div
                      className="absolute right-6 top-[50%] -translate-y-[50%] cursor-pointer text-black"
                      onClick={() => setConfirmPassword(previous => !previous)}
                    >
                      {confirmPassword ? (
                        <img src={PasswordView} className="h-[24px] w-[24px]" />
                      ) : (
                        <img src={PasswordHide} className="h-[24px] w-[24px]" />
                      )}
                    </div>
                  </div>
                </div>
                {(errors?.confirmPassword || errors?.password) && (
                  <p className="mt-2 pr-8 text-[11px] text-red-800 sm:text-[14px]">
                    {(errors.confirmPassword?.message as ReactNode) ||
                      (errors.password?.message as ReactNode)}
                  </p>
                )}
                <Button
                  type="submit"
                  disabled={!isDirty || !isValid || isLoading}
                  className="mx-auto mt-[32px] w-full 
              border border-primary   
              bg-primary text-white disabled:cursor-not-allowed disabled:bg-transparent disabled:text-primary"
                >
                  {isLoading ? (
                    <Spinner className="" />
                  ) : (
                    <p className={`text-[14px] font-[700] sm:text-[16px]`}>
                      Next
                    </p>
                  )}
                </Button>
              </div>
            </Form>
          </>
        )}
      </div>
    </div>
  );
}
