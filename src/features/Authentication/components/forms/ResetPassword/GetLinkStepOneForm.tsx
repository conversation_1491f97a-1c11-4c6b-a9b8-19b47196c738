import { motion } from 'framer-motion';
import { useEffect } from 'react';
import { SubmitHandler } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';

import Form from '../../../../../components/forms/Form';
import { FormInput } from '../../../../../components/forms/FormInput';
import Button from '../../../../../components/ui/ButtonComponent';
import { Spinner } from '../../../../../components/ui/CommonWidget/Loader';
import { useFormDataAndApiQueryHandler } from '../../../../../hooks/useFormDataAndApiQueryHandler';
import { BasicRegistrationPayload } from '../../../../../types';
import { useAuthEventContext } from '../../../context/event/AuthEventContext';
import { useGetLinkForPasswordReset } from '../../../hooks/apiQueryHooks/authQueryHooks';
import { getLinkSchema } from '../../../lib/yup/validations';

export default function GetLinkStepOneForm() {
  const navigate = useNavigate();
  useEffect(() => {
    navigate('/reset-password');
  }, []);
  const {
    activateGetOtp,
    setActivateGetOtpHandler,
    setEmailHandler,
    setResetPasswordLevelHandler,
  } = useAuthEventContext();
  const next = () => {
    setResetPasswordLevelHandler(2);
    setActivateGetOtpHandler(false);
  };
  const onError = () => {
    setActivateGetOtpHandler(false);
  };
  const {
    register,
    handleSubmit,
    formState: { isDirty, isValid },
    isFetching,
  } = useFormDataAndApiQueryHandler<
    Pick<BasicRegistrationPayload, 'email'>,
    any
  >(activateGetOtp, getLinkSchema, useGetLinkForPasswordReset, {
    next,
    onError,
  });
  const onSubmit: SubmitHandler<
    Pick<BasicRegistrationPayload, 'email'>
  > = data => {
    setActivateGetOtpHandler(true);
    setEmailHandler(data);
  };
  return (
    <motion.div
      exit={{ opacity: 0 }}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.2, delay: 0.5, ease: 'easeIn' }}
      className="flex-1 px-4 sm:px-0"
    >
      <div className="mx-auto mt-8 h-full w-full max-w-[457px] px-5 py-8 sm:py-20">
        <h2 className="mb-3 pt-12 text-center text-[24px] font-[500] leading-[47px] sm:pt-24 sm:text-[32px]">
          Reset Password
        </h2>
        <p className="pb-12 text-center text-[16px] leading-[24px]">
          We're here to help you regain access to your account. Simply follow
          these straightforward steps to reset your password:
        </p>
        <Form
          onSubmit={handleSubmit(onSubmit)}
          className="mx-auto w-full max-w-[400px]"
        >
          <div className="mt-5">
            <div className="">
              <label htmlFor="email">Email Address</label>
              <div className="relative mt-2">
                <FormInput
                  className="input "
                  placeholder="Email"
                  autoComplete="email"
                  id="email"
                  registerHanlder={() => register('email')}
                />
              </div>
            </div>
            <Button
              type="submit"
              disabled={!isDirty || !isValid || isFetching}
              className="mx-auto mt-[32px] w-full 
              border border-primary   
              bg-primary text-white disabled:cursor-not-allowed disabled:bg-transparent disabled:text-primary"
            >
              {isFetching ? (
                <Spinner className="" />
              ) : (
                <p className={` text-[14px] font-[700] sm:text-[16px]`}>
                  {' '}
                  Send Recovery Link
                </p>
              )}
            </Button>
          </div>
        </Form>
      </div>
    </motion.div>
  );
}
