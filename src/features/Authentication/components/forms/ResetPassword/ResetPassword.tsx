import { AnimatePresence } from 'framer-motion'

import { AuthEventProvider } from '../../../context/event/AuthEventContext'
import GetLinkStepTwo from '../../ui/GetLinkStepTwo'
import GetLinkStepOneForm from './GetLinkStepOneForm'
import ResetPasswordContainer from './ResetPasswordContainer'

export function ResetPassword() {
  return (
    <AuthEventProvider>
      <div
        className="overflow-hidden mx-auto
        py-10 sm:p-10 h-screen"
      >
        <AnimatePresence>
          <ResetPasswordContainer level={1} key="stepOne">
            <GetLinkStepOneForm />
          </ResetPasswordContainer>
          <ResetPasswordContainer level={2} key="stepTwo">
            <GetLinkStepTwo />
          </ResetPasswordContainer>
        </AnimatePresence>
      </div>
    </AuthEventProvider>
  )
}
