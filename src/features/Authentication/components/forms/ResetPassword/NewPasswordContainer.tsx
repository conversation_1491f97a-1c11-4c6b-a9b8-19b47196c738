import { motion } from 'framer-motion'
import { ReactNode } from 'react'
import { <PERSON> } from 'react-router-dom'

import { upivotalLogo } from '../../../../../assets/icons'
import bgImage from '../../../../../assets/images/onboardingBgImage.png'

export default function NewPasswordContainer({
  children,
}: {
  children: ReactNode
}) {
  return (
    <motion.div
      exit={{ opacity: 0, transition: { duration: 0.2 } }}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.2, delay: 0.5, ease: 'easeIn' }}
      style={{ backgroundImage: `url(${bgImage})` }}
      className="relative overflow-hidden mx-auto max-w-full bg-cover h-screen flex justify-center"
    >
      <Link className="absolute top-[5%] left-[7%]" to="/">
        <div className="min-w-[80px] max-w-[117px]">
          <img loading="lazy" src={upivotalLogo} className={` w-full h-full`} />
        </div>
      </Link>
      {children}
    </motion.div>
  )
}
