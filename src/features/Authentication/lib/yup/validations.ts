import * as yup from 'yup';

import { Helper } from '../../../../utils/helpers';
import { SubscriptionType } from '@/types';

export const signupStepOneSchema = yup
  .object()
  .shape({
    firstName: yup.string().required('First Name is a required field'),
    lastName: yup.string().required('Last Name is a required field'),
    email: yup.string().required('Email is a required field'),
    phoneNumber: yup.string().required('Phone Number is a required field'),
    terms: yup
      .boolean()
      .oneOf<boolean>([true], 'Please accept terms')
      .required('Please accept terms'),
    captcha: yup.string().required(),
    title: yup.string(),
    subscriptionType: yup.string<SubscriptionType>(),
    phoneNumberVerified: yup.boolean(),
  })
  .required();
export const conversationSignupStepOneSchema = yup
  .object()
  .shape({
    email: yup.string().required('Email is a required field'),
    captcha: yup.string().required(),
    terms: yup
      .boolean()
      .oneOf<boolean>([true], 'Please accept terms')
      .required('Please accept terms'),
  })
  .required();
export const conversationSignupStepThreeSchema = yup
  .object()
  .shape({
    firstName: yup.string().required('First Name is a required field'),
    lastName: yup.string().required('Last Name is a required field'),
  })
  .required();
export const otpSchema = yup
  .object()
  .shape({
    pinOne: yup.number().required('Only numeric input'),
    pinTwo: yup.number().required('Only numeric input'),
    pinThree: yup.number().required('Only numeric input'),
    pinFour: yup.number().required('Only numeric input'),
    pinFive: yup.number().required('Only numeric input'),
    pinSix: yup.number().required('Only numeric input'),
  })
  .required();

export const passwordSchema = yup
  .object()
  .shape({
    password: yup
      .string()
      .required('Password is a required field')
      .matches(
        Helper.passwordRegex,
        'Your password needs to be at least 8 characters, One Uppercase, One Number and One Special Character.',
      ),
    confirmPassword: yup
      .string()
      .required('Confirm your password.')
      .test('passwords-match', 'Passwords must match', function (value) {
        return this.parent.password === value;
      }),
  })
  .required();

export const getLinkSchema = yup
  .object()
  .shape({
    email: yup.string().required('Please enter email'),
  })
  .required();
