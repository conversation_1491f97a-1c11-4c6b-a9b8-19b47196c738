import { useCallback, useState } from 'react';

import { NullishUser } from '../../../../data/nulllishData';
import { BasicRegistrationPayload } from '../../../../types';
import { IConversationSignupPayload, ISignupPayload } from '../../types';

export default function useContextStateAndActions() {
  const [signupLevel, setSignupLevel] = useState(1);
  const [conversationSignupLevel, setConversationSignupLevel] = useState(1);
  const [resetPasswordLevel, setResetPasswordLevel] = useState(1);
  const [temporaryUser, setTemporaryUser] = useState<ISignupPayload>(
    new NullishUser().getData,
  );
  const [conversationTemporaryUser, setConversationTemporaryUser] =
    useState<IConversationSignupPayload>({} as IConversationSignupPayload);
  const [email, setEmail] = useState({ email: '' });
  const [activateGetOtp, setActivateGetOtp] = useState(false);

  const setSignupLevelHandler = useCallback(
    (value: React.SetStateAction<number>) => setSignupLevel(value),
    [],
  );
  const setConversationSignupLevelHandler = useCallback(
    (value: React.SetStateAction<number>) => setConversationSignupLevel(value),
    [],
  );
  const setTemporaryUserHandler = useCallback(
    (value: React.SetStateAction<ISignupPayload>) => setTemporaryUser(value),
    [],
  );
  const setConversationTemporaryUserHandler = useCallback(
    (value: React.SetStateAction<IConversationSignupPayload>) =>
      setConversationTemporaryUser(value),
    [],
  );
  const setResetPasswordLevelHandler = useCallback(
    (value: React.SetStateAction<number>) => setResetPasswordLevel(value),
    [],
  );

  const setActivateGetOtpHandler = useCallback(
    (value: boolean) => setActivateGetOtp(value),
    [],
  );
  const setEmailHandler = useCallback(
    (value: Pick<BasicRegistrationPayload, 'email'>) => setEmail(value),
    [],
  );
  return {
    conversationSignupLevel,
    setConversationSignupLevelHandler,
    setSignupLevelHandler,
    setResetPasswordLevelHandler,
    resetPasswordLevel,
    signupLevel,
    setActivateGetOtpHandler,
    activateGetOtp,
    email,
    setEmailHandler,
    temporaryUser,
    setTemporaryUserHandler,
    conversationTemporaryUser,
    setConversationTemporaryUserHandler,
  };
}
