import React, { createContext, useContext } from 'react'

import useAuthContextStateAndActions from './useAuthContextStateAndActions'

type AuthEventContextType = ReturnType<typeof useAuthContextStateAndActions>

const AuthEventContext = createContext({} as AuthEventContextType)
export const useAuthEventContext = () => {
  const context = useContext(AuthEventContext)
  if (!context) {
    throw new Error(
      `Authentication components cannot be rendered outside the Authentication features`
    )
  }
  return context
}

export const AuthEventProvider = ({
  children,
}: {
  children: React.ReactNode
}) => {
  return (
    <AuthEventContext.Provider value={useAuthContextStateAndActions()}>
      {children}
    </AuthEventContext.Provider>
  )
}
