import { useMutation, useQuery } from '@tanstack/react-query';

import { ApiError, BasicRegistrationPayload } from '../../../../types';
import {
  getOTP,
  getUnregisteredEmailOTP,
  requestEmailVerification,
  requestPasswordReset,
  resetPassword,
  signUpUser,
  signUpUserForConversation,
  verifyEmail,
  verifyEmailExistence,
  verifyPhoneNumber,
  verifyUnregisteredEmail,
} from '../../services/authApiRequest';
import {
  CONFIRM_OTP_QUERY_KEY,
  GET_UNREGISTERED_OTP_QUERY,
  OTP_QUERY_KEY,
  RESEND_OTP_QUERY_KEY,
  SIGNUP_FOR_CONVERSATION_QUERY_KEY,
  SIGNUP_QUERY_KEY,
} from '../../utils/queryKeys';
export const useSignUpUser = (options = {}) => {
  return useMutation(signUpUser, {
    mutationKey: [SIGNUP_QUERY_KEY],
    ...options,
  });
};
export const useSignUpUserForConversation = (options = {}) => {
  return useMutation(signUpUserForConversation, {
    mutationKey: [SIGNUP_FOR_CONVERSATION_QUERY_KEY],
    ...options,
  });
};
export const useGetOTP = (payload: any, options = {}) => {
  const { phoneNumber } = payload;
  return useQuery<any, ApiError<any>>(
    [OTP_QUERY_KEY, phoneNumber],
    () => getOTP(phoneNumber),
    {
      cacheTime: 0,
      staleTime: 0,
      ...options,
    },
  );
};
export const useGetUnregisteredEmailOTP = (payload: any, options = {}) => {
  const { email } = payload;
  return useQuery<any, ApiError<any>>(
    [GET_UNREGISTERED_OTP_QUERY, email],
    () => getUnregisteredEmailOTP(email),
    {
      cacheTime: 0,
      staleTime: 0,
      ...options,
    },
  );
};
export const useGetUnregisteredPhoneOTP = (payload: any, options = {}) => {
  const { phoneNumber } = payload;
  return useQuery<any, ApiError<any>>(
    [RESEND_OTP_QUERY_KEY, phoneNumber],
    () => getOTP(phoneNumber),
    {
      ...options,
    },
  );
};
export const useVerifyPhoneNumber = (options = {}) => {
  return useMutation(verifyPhoneNumber, {
    mutationKey: [CONFIRM_OTP_QUERY_KEY],
    ...options,
  });
};
export const useVerifyEmailExistence = (options = {}) => {
  return useMutation(verifyEmailExistence, {
    mutationKey: ['VERIFY_EMAIL_EXISTENCE_QUERY'],
    ...options,
  });
};

export const useVerifyEmail = (payload = {}, options = {}) => {
  return useQuery<any, ApiError<any>>(
    ['EMAIL_VERIFY_KEY', payload],
    () => verifyEmail(payload),
    {
      ...options,
    },
  );
};
export const useVerifyUnregisteredEmail = (payload = {}, options = {}) => {
  return useQuery<any, ApiError<any>>(
    ['UNREGISTERED_EMAIL_VERIFY_KEY', payload],
    () => verifyUnregisteredEmail(payload),
    {
      ...options,
    },
  );
};
export const useGetLinkForPasswordReset = (
  payload: Pick<BasicRegistrationPayload, 'email'>,
  options = {},
) => {
  return useQuery(
    [OTP_QUERY_KEY, payload],
    () => requestPasswordReset(payload),
    {
      ...options,
    },
  );
};
export const useResetPassword = (options = {}) => {
  return useMutation(resetPassword, {
    mutationKey: ['RESET_PASSWORD_KEY'],
    ...options,
  });
};
export const useRequestEmailVerification = (
  payload: Pick<BasicRegistrationPayload, 'email'>,
  options = {},
) => {
  return useQuery(
    [OTP_QUERY_KEY, payload],
    () => requestEmailVerification(payload),
    {
      ...options,
    },
  );
};
