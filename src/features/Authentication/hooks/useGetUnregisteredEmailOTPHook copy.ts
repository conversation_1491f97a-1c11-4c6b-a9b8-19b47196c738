import { useCustomToast } from '../../../hooks/useToast';
import { ApiError } from '../../../types';
import { Helper } from '../../../utils/helpers';
import { useAuthEventContext } from '../context/event/AuthEventContext';
import { useGetUnregisteredEmailOTP } from './apiQueryHooks/authQueryHooks';

export default function useGetUnregisteredEmailOTPHook() {
  const {
    setActivateGetOtpHandler,
    activateGetOtp,
    conversationTemporaryUser,
  } = useAuthEventContext();
  const { errorToast, successToast } = useCustomToast();
  useGetUnregisteredEmailOTP(conversationTemporaryUser, {
    enabled: !!activateGetOtp && !!conversationTemporaryUser.email,
    onSuccess: () => {
      successToast('OTP sent successfully');
      setActivateGetOtpHandler(false);
    },
    onError: (error: ApiError<any>) => {
      setActivateGetOtpHandler(false);
      errorToast(Helper.getError(error));
    },
  });
}
