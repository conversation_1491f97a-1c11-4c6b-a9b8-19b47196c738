import { useSearchParams } from 'react-router-dom';

import { useAuthEventContext } from '../context/event/AuthEventContext';
import { Helper } from '../../../utils/helpers';
import { ISignupPayload } from '../types';
import { SubscriptionType } from '@/types';

export default function useSubscriptionTypeHandler() {
  const [searchParams] = useSearchParams();
  const s_type = searchParams
    .get('s_type')
    ?.toUpperCase() as SubscriptionType | null;
  const { setTemporaryUserHandler, setActivateGetOtpHandler } =
    useAuthEventContext();

  const handleDefaultOrFromPriceRoute = (
    data: ISignupPayload,
    s_type: SubscriptionType | null,
  ) => {
    const newData = {
      subscriptionType: s_type || undefined,
      ...data,
    };
    setTemporaryUserHandler({
      ...Helper.clearEmptyField(newData),
    });
    setActivateGetOtpHandler(true);
  };
  return {
    handleDefaultOrFromPriceRoute,
    s_type,
  };
}
