import { useCustomToast } from '../../../hooks/useToast';
import { ApiError } from '../../../types';
import { Helper } from '../../../utils/helpers';
import { useAuthEventContext } from '../context/event/AuthEventContext';
import { useGetUnregisteredPhoneOTP } from './apiQueryHooks/authQueryHooks';

export default function useGetUnregisteredPhoneOTPHook() {
  const { setActivateGetOtpHandler, activateGetOtp, temporaryUser } =
    useAuthEventContext();
  const { errorToast, successToast } = useCustomToast();
  useGetUnregisteredPhoneOTP(temporaryUser, {
    enabled: !!activateGetOtp && !!temporaryUser?.phoneNumber,
    onSuccess: () => {
      successToast('OTP sent successfully');
      setActivateGetOtpHandler(false);
    },
    onError: (error: ApiError<any>) => {
      setActivateGetOtpHandler(false);
      errorToast(Helper.getError(error));
    },
  });
}
