import { BasicRegistrationPayload, SubscriptionType } from '../../types';

interface IPasswordPayload {
  password: string;
  confirmPassword: string;
}

interface PinPayload {
  pinOne: number;
  pinTwo: number;
  pinThree: number;
  pinFour: number;
  pinFive: number;
  pinSix: number;
}
interface ISignupPayload extends BasicRegistrationPayload {
  captcha: string;
  terms: boolean;
  title?: string;
  subscriptionType?: SubscriptionType;
  phoneNumberVerified?: boolean;
}
interface IConversationSignupStepOnePayload {
  email: string;
  captcha: string;
  terms: boolean;
}
interface IConversationSignupStepThreePayload {
  firstName: string;
  lastName: string;
}

type IConversationSignupPayload = Partial<
  IConversationSignupStepThreePayload & IConversationSignupStepOnePayload
>;

export type {
  IPasswordPayload,
  ISignupPayload,
  PinPayload,
  IConversationSignupStepOnePayload,
  IConversationSignupPayload,
  IConversationSignupStepThreePayload,
};
