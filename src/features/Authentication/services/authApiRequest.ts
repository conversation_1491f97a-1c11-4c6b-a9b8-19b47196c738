import { publicRequest } from '../../../lib/axios/publicRequest';
import { BasicRegistrationPayload } from '../../../types';
import { user } from '../../../utils/apiServiceControllersRoute';
import { BASE_URL } from '../../../utils/apiUrls';
import { IPasswordPayload, ISignupPayload, PinPayload } from '../types';
import {
  registerPath,
  requestEmailVerificationPath,
  requestPasswordResetPath,
  requestPhoneVerification,
  resetPasswordPath,
  verifyEmailPath,
  verifyPhoneNumberPath,
} from '../utils/apiUrls';

export const signUpUser = async (payload: any): Promise<any> => {
  const {
    /* eslint-disable @typescript-eslint/no-unused-vars */
    captcha,
    /* eslint-disable @typescript-eslint/no-unused-vars */
    terms,
    /* eslint-disable @typescript-eslint/no-unused-vars */
    confirmPassword,
    ...others
  }: IPasswordPayload & ISignupPayload = payload;
  const res = await publicRequest(BASE_URL).post(`${user}${registerPath}`, {
    ...others,
    phoneNumber: '+' + others.phoneNumber,
  });
  return res.data;
};
export const signUpUserForConversation = async (payload: any): Promise<any> => {
  const {
    /* eslint-disable @typescript-eslint/no-unused-vars */
    captcha,
    /* eslint-disable @typescript-eslint/no-unused-vars */
    terms,
    /* eslint-disable @typescript-eslint/no-unused-vars */
    confirmPassword,
    ...others
  }: IPasswordPayload & ISignupPayload = payload;
  const res = await publicRequest(BASE_URL).post(`${user}${registerPath}`, {
    ...others,
  });
  return res.data;
};
export const getOTP = async (phoneNumber: string): Promise<any> => {
  const res = await publicRequest(BASE_URL).get(
    `${user}${requestPhoneVerification}?phoneNumber=${'+' + phoneNumber}`,
  );
  return res.data;
};
export const getUnregisteredEmailOTP = async (email: string): Promise<any> => {
  const res = await publicRequest(BASE_URL).get(
    `${user}/request-unregistered-email-token?email=${email}`,
  );
  return res.data;
};
export const verifyPhoneNumber = async (payload: PinPayload): Promise<any> => {
  const code = Object.values(payload).concat().reverse().join('');
  const res = await publicRequest(BASE_URL).post(
    `${user}${verifyPhoneNumberPath}`,
    { data: code },
  );
  return res.data;
};
export const verifyEmail = async (payload = {}): Promise<any> => {
  const newPayload = Object.values(payload).concat().join('');
  const res = await publicRequest(BASE_URL).get(
    `${user}${verifyEmailPath}?token=${newPayload}`,
  );
  return res.data;
};
export const verifyUnregisteredEmail = async (payload = {}): Promise<any> => {
  const newPayload = Object.values(payload).concat().join('');
  const res = await publicRequest(BASE_URL).get(
    `${user}/verify-unregistered-email?token=${newPayload}`,
  );
  return res.data;
};
export const verifyEmailExistence = async (payload: string): Promise<any> => {
  const res = await publicRequest(BASE_URL).post(
    `${user}/verify-email-existence`,
    {
      data: payload,
    },
  );
  return res.data;
};

export const requestPasswordReset = async (
  payload: Pick<BasicRegistrationPayload, 'email'>,
): Promise<any> => {
  const { email } = payload;
  const res = await publicRequest(BASE_URL).get(
    `${user}${requestPasswordResetPath}?email=${email}`,
  );
  return res.data;
};
export const requestEmailVerification = async (
  payload: Pick<BasicRegistrationPayload, 'email'>,
): Promise<any> => {
  const { email } = payload;
  const res = await publicRequest(BASE_URL).get(
    `${user}${requestEmailVerificationPath}?email=${email}`,
  );
  return res.data;
};
export const resetPassword = async (payload: any): Promise<any> => {
  const { password, token }: { token: string; password: string } = payload;
  const res = await publicRequest(BASE_URL).post(
    `${user}${resetPasswordPath}`,
    { token, newPassword: password },
  );
  return res.data;
};
