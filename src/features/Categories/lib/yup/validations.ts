import * as yup from 'yup';
import moment from 'moment';

import {
  ProjectlevelUserRoleType,
  ProjectStatusValues,
} from '../../../../types';
import { visibility } from '@/data/constants';

export const iAddProjectSchema = (isStudentOrFaculty: boolean) =>
  yup
    .object()
    .shape({
      projectName: yup.string().required('Project Name is a required field'),
      projectDescription: yup
        .string()
        .required('Project Description is a required field'),
      projectCountries: yup
        .array()
        .of(yup.string().required('Focus Country is a required field'))
        .min(1, 'You can only pick a minimum of 1 focus country')
        .max(5, 'You can only pick a maximum of 5 focus countries')
        .required('Focus Country is a required field'),
      projectLevel: yup.string().when([], {
        is: () => isStudentOrFaculty,
        then: schema => schema.required('Project Level is a required field'),
      }),
      presumedProjectCost: yup.string().when([], {
        is: () => !isStudentOrFaculty,
        then: schema =>
          schema.required('Presumed Project Cost is a required field'),
      }),
      projectStatus: yup.string<ProjectStatusValues>(),
      projectImage: yup.mixed<File>(),
      sdgCategoryRefs: yup
        .array()
        .of(yup.string().required('SDG is a required field'))
        .min(1, 'You can only pick a minimum of 1 SDG')
        .max(5, 'You can only pick a maximum of 5 SDGs')
        .required('SDGs is a required field'),
      problemStatementRef: yup.string(),
      startDate: yup.date().nullable().required('Duration is a required field'),
      endDate: yup.date().nullable().required('Duration is a required field'),
      descriptionVideoUrl: yup
        .string()
        .nullable()
        .transform(value => (value === '' ? null : value))
        .matches(
          /^(https?:\/\/)?(www\.)?(youtube\.com\/(watch\?v=|embed\/|v\/|shorts\/|user\/|c\/|channel\/|playlist\?list=)|youtu\.be\/)[\w-]+(&\S*)?$/,
          'Please enter a valid YouTube link',
        )
        .notRequired(),
      documents: yup
        .mixed<File[]>()
        .test(
          'fileSize',
          'One or more files are too large. Maximum size is 25 MB.',
          files => {
            if (!files) return true;
            return files.every(file => file.size <= 25 * 1024 * 1024); // 25 MB limit
          },
        )
        .test('fileTypes', 'Unsupported file format.', files => {
          if (!files) return true;
          const allowedFormats = new Set([
            'jpg',
            'jpeg',
            'pdf',
            'png',
            'gif',
            'doc',
            'docx',
          ]);
          return files.every(file =>
            allowedFormats.has(
              (file.name.split('.').pop() || '')?.toLowerCase(),
            ),
          );
        }),
      visibility: yup
        .string()
        .oneOf([visibility.PRIVATE, visibility.PUBLIC])
        .required('Project Visibility Settings is a required field'),
    })
    .required();

export const IAddProblemStatementSchema = yup
  .object()
  .shape({
    countries: yup
      .array()
      .of(yup.string().required('Country is a required field'))
      .required('Country is a required field'),
    title: yup.string().required('Title is a required field'),
    categories: yup
      .array()
      .of(yup.string().required('Categories is a required field'))
      .min(1, 'Pick a maximum of two categories')
      .max(2, 'Pick a maximum of two categories')
      .required('Categories is a required field'),
    subcategories: yup
      .array()
      .of(yup.string().required('SubCategories is a required field'))
      .required('SubCategories is a required field'),
    description: yup.string().required('Description is a required field'),
    descriptionVideoUrl: yup.string().required('Video url is a required field'),
    descriptionFile: yup
      .mixed<File>()
      .required('Description File is a required field'),
  })
  .required();

export const iInviteToJoinProjectSchema = yup
  .object()
  .shape({
    firstName: yup.string().required('First Name is a required field'),
    lastName: yup.string().required('Last Name is a required field'),
    email: yup.string().required('Email is a required field'),
    projectRole: yup
      .string<ProjectlevelUserRoleType>()
      .required('Role is a required field'),
  })
  .required();

export const iUpdateProjectDescriptionSchema = yup
  .object()
  .shape({
    projectDescription: yup
      .string()
      .max(3000, 'Description must have a maximum of 3000 characters')
      .required('Project Description is required'),
  })
  .required();

export const iUpdateProjectImageSchema = yup
  .object()
  .shape({
    projectImage: yup
      .mixed<FileList>()
      .test('fileType', 'Please upload your image', value => {
        const file = value as FileList;
        return file && file.length > 0;
      })
      .test('fileSize', 'The file is too large (max 5MB)', value => {
        const file = value as FileList;
        if (!file || !file[0]) return true;
        return file[0].size <= 5_000_000;
      })
      .required('Project Image is required'),
  })
  .required();

export const iUpdateProjectSchema = (isStudentOrFaculty: boolean) =>
  yup
    .object()
    .shape({
      projectDescription: yup
        .string()
        .required('Project Description is a required field'),
      projectName: yup.string().required('Project Name is a required field'),
      projectImage: yup.mixed<File>(),
      projectCountries: yup
        .array()
        .of(yup.string().required('Focus Country is a required field'))
        .min(1, 'You can only pick a minimum of 1 focus country')
        .max(5, 'You can only pick a maximum of 5 focus countries'),
      projectLevel: yup.string().nullable().when([], {
        is: () => isStudentOrFaculty,
        then: schema => schema.required('Project Level is a required field'),
      }),
      presumedProjectCost: yup.string().nullable().when([], {
        is: () => !isStudentOrFaculty,
        then: schema =>
          schema.required('Presumed Project Cost is a required field'),
      }),
      projectStatus: yup.string<ProjectStatusValues>(),
      sdgCategoryRefs: yup
        .array()
        .of(yup.string().required('SDG is a required field'))
        .min(1, 'You can only pick a minimum of 1 SDG')
        .max(5, 'You can only pick a maximum of 5 SDGs'),
      startDate: yup
        .date()
        .test(
          'is-less-than-endDate',
          'Start date must be less than end date',
          function (value) {
            const { endDate } = this.parent;
            if (!value || !endDate) return true;
            return moment(value).isBefore(moment(endDate), 'day');
          },
        ),
      endDate: yup
        .date()
        .test(
          'is-greater-than-startDate',
          'End date must be greater than start date',
          function (value) {
            const { startDate } = this.parent;
            if (!value || !startDate) return true;
            return moment(value).isAfter(moment(startDate), 'day');
          },
        ),
      descriptionVideoUrl: yup
        .string()
        .nullable()
        .transform(value => (value === '' ? null : value))
        .matches(
          /^(https?:\/\/)?(www\.)?(youtube\.com\/(watch\?v=|embed\/|v\/|shorts\/|user\/|c\/|channel\/|playlist\?list=)|youtu\.be\/)[\w-]+(&\S*)?$/,
          'Please enter a valid YouTube link',
        )
        .notRequired(),
      documents: yup
        .mixed<File[]>()
        .test(
          'fileSize',
          'One or more files are too large. Maximum size is 25 MB.',
          files => {
            if (!files) return true;
            return files.every(file => file.size <= 25 * 1024 * 1024); // 25 MB limit
          },
        )
        .test('fileTypes', 'Unsupported file format.', files => {
          if (!files) return true;
          const allowedFormats = new Set([
            'jpg',
            'jpeg',
            'pdf',
            'png',
            'gif',
            'doc',
            'docx',
          ]);
          return files.every(file =>
            allowedFormats.has(
              (file.name.split('.').pop() || '')?.toLowerCase(),
            ),
          );
        }),
    })
    .required();

export const iUpdateProjectAttachmentSchema = yup
  .object()
  .shape({
    descriptionVideoUrl: yup
      .string()
      .nullable()
      .transform(value => (value === '' ? null : value))
      .matches(
        /^(https?:\/\/)?(www\.)?(youtube\.com\/(watch\?v=|embed\/|v\/|shorts\/|user\/|c\/|channel\/|playlist\?list=)|youtu\.be\/)[\w-]+(&\S*)?$/,
        'Please enter a valid YouTube link',
      )
      .notRequired(),
    documents: yup
      .mixed<File[]>()
      .test(
        'fileSize',
        'One or more files are too large. Maximum size is 25 MB.',
        files => {
          if (!files) return true;
          return files.every(file => file.size <= 25 * 1024 * 1024); // 25 MB limit
        },
      )
      .test('fileTypes', 'Unsupported file format.', files => {
        if (!files) return true;
        const allowedFormats = new Set([
          'jpg',
          'jpeg',
          'pdf',
          'png',
          'gif',
          'doc',
          'docx',
        ]);
        return files.every(file =>
          allowedFormats.has((file.name.split('.').pop() || '')?.toLowerCase()),
        );
      }),
  })
  .required();

export const iAddProblemStatementToProjectSchema = yup
  .object()
  .shape({
    problemStatementRef: yup.string().required('Problem Statement is required'),
  })
  .required();
