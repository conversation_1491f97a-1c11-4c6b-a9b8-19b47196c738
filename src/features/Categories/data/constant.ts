import { categoryByStatus, problemCategory } from '../../../data/constants'

export const categoryByStatusOptions = [
  { value: categoryByStatus.all, label: 'All' },
  {
    value: categoryByStatus.mostPopular,
    label: 'Most Popular',
  },
  {
    value: categoryByStatus.recentlyAdded,
    label: 'Recently Added',
  },
]

export const problemCategoryOptions = [
  {
    value: problemCategory.healthcare,
    label: problemCategory.healthcare,
  },
  {
    value: problemCategory.crossBorderTrade,
    label: problemCategory.crossBorderTrade,
  },
  {
    value: problemCategory.education,
    label: problemCategory.education,
  },
  {
    value: problemCategory.foodSupplyChain,
    label: problemCategory.foodSupplyChain,
  },
]

export const courseProvider = {
  UDEMY: 'Udemy',
  UDACITY: 'Udacity',
  COURSERA: 'Coursera',
  UPICK: 'uPick',
  UBOOK: 'uBooks',
} as const

export const COURSE_SECTIONS = {
  ALL: 'all_courses',
  MY_LEARNING: 'my_learning',
  ...courseProvider,
} as const
