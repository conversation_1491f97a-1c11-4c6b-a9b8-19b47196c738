import { useAppContext } from '@/context/event/AppEventContext';
import { useUserContext } from '../../../context/user/UserContext';
import { publicRequest } from '../../../lib/axios/publicRequest';
import { usePrivateRequest } from '../../../lib/axios/usePrivateRequest';
import {
  ProjectlevelUserRoleType,
  ServerCoursePrivileges,
  ServerCourseraCourse,
  ServerCourseraCourseActivity,
  ServerCourseraCourses,
  ServerMyCourses,
  ServerProject,
  ServerProjectAdmins,
  ServerProjectMembers,
  ServerProjects,
  ServerProjectTeams,
  ServerUdacityCourse,
  ServerUdacityCourses,
  ServerUdemyCourse,
  ServerUdemyCourseActivity,
  ServerUdemyCourses,
  ServerWishlistCourses,
  Value,
  VisibilityType,
} from '../../../types';
import {
  courseActivities,
  courseraCourses,
  myCourses,
  problemStatements,
  projects,
  udacityCourses,
  udemyCourses,
  wishlist,
} from '../../../utils/apiServiceControllersRoute';
import { BASE_URL } from '../../../utils/apiUrls';
import { Helper } from '../../../utils/helpers';
import {
  IAddProblemStatementPayload,
  IAddProjectPayload,
  ICourseActivitiesPayload,
  ICoursePrivilegesPayload,
  IDeleteProjectFilesPayload,
  IInviteToJoinProjectPayload,
} from '../types';

export const useGetCanCreateProjectApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);
  return async (): Promise<any> => {
    const res = await axiosInstance.current?.get(
      `${projects}/can-create-project`,
    );
    return res?.data?.data;
  };
};
export const useAddProjectApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);
  return async (payload: IAddProjectPayload): Promise<any> => {
    const { documents, ...rest } = payload;
    const dateValue: Value = [payload?.startDate, payload?.endDate] as Value;

    const data = Helper.createFormData(
      Helper.clearEmptyField({
        ...rest,
        startDate: Helper.formatDateToString(dateValue, 0),
        endDate: Helper.formatDateToString(dateValue, 1),
      }),
    );

    if (Array.isArray(documents) && documents?.length > 0) {
      for (let i = 0; i < documents.length; i++) {
        data.append('documents', documents[i]);
      }
    }

    const res = await axiosInstance.current?.post(`${projects}`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
    return res?.data;
  };
};

export const useAddProblemStatementApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);
  return async (payload: IAddProblemStatementPayload): Promise<any> => {
    const data = Helper.createFormData(payload);
    const res = await axiosInstance.current?.post(
      `${problemStatements}`,
      data,
      {
        headers: { 'Content-Type': 'multipart/form-data' },
      },
    );
    return res?.data;
  };
};
export const useRequestToJoinAProjectApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);
  const requestToJoinAProject = async (path: {
    projectRef: string;
  }): Promise<any> => {
    const res = await axiosInstance.current?.get(
      `${projects}/request-to-join-project/${path.projectRef}`,
    );
    return res?.data;
  };
  return requestToJoinAProject;
};
export const useAddTeamToProjectApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);
  const addTeamToProject = async (path: {
    projectRef: string;
    teamRef: string;
  }): Promise<any> => {
    const res = await axiosInstance.current?.post(
      `${projects}/${path.projectRef}/teams/${path.teamRef}`,
    );
    return res?.data;
  };
  return addTeamToProject;
};
export const useRemoveTeamFromProjectApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);
  return async (path: {
    projectRef: string;
    teamRef: string;
  }): Promise<any> => {
    const res = await axiosInstance.current?.delete(
      `${projects}/${path.projectRef}/teams/${path.teamRef}`,
    );
    return res?.data;
  };
};
export const useAddExternalTeamToProjectApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);
  return async (path: {
    projectRef: string;
    teamRef: string;
  }): Promise<any> => {
    const res = await axiosInstance.current?.post(
      `${projects}/${path.projectRef}/invite/${path.teamRef}`,
    );
    return res?.data;
  };
};
export const useAddMyTeamToAvailableProjectApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);
  return async (path: {
    projectRef: string;
    teamRef: string;
  }): Promise<any> => {
    const res = await axiosInstance.current?.post(
      `${projects}/join-request`,
      path,
    );
    return res?.data;
  };
};
export const useInviteToJoinProjectApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);
  const inviteToJoinAProject = async (
    payload: IInviteToJoinProjectPayload,
  ): Promise<any> => {
    const res = await axiosInstance.current?.post(
      `${projects}/invite-user-to-join-project`,
      payload,
    );
    return res?.data;
  };

  return inviteToJoinAProject;
};
export const useProcessRequestToJoinAProjectApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);
  const processRequestToJoinAProject = async (path: {
    userId: string;
    decision: string;
    projectRef: string;
  }): Promise<any> => {
    const res = await axiosInstance.current?.get(
      `${projects}/process-request-to-join-project/${path.projectRef}/${path.userId}/${path.decision}`,
    );
    return res?.data;
  };
  return processRequestToJoinAProject;
};

export const useGetProjectsApi = () => {
  const { currentAccountType } = useAppContext();
  const axiosInstance = usePrivateRequest(BASE_URL);
  const getProjects = async (params: {
    pageSize?: number;
    page?: number;
    search?: string | null;
    countries?: string[];
    categoryRefs?: string[];
  }): Promise<ServerProjects> => {
    const newParams = {
      ...params,
      categoryRefs: params?.categoryRefs?.join(','),
      countries: params?.countries?.join(','),
      accountType: currentAccountType.toUpperCase(),
    };
    const cleanparams = Helper.clearEmptyField(newParams);
    const res = await axiosInstance.current?.get(
      `${projects}/user-created-projects`,
      {
        params: params.search
          ? {
              search: params.search,
              accountType: currentAccountType.toUpperCase(),
            }
          : { ...cleanparams },
      },
    );
    return res?.data;
  };
  return getProjects;
};

export const useGetProjectsInfiniteQueryApi = () => {
  const { currentAccountType } = useAppContext();
  const axiosInstance = usePrivateRequest(BASE_URL);
  return async ({ pageParam: pageParameter = 1 }): Promise<ServerProjects> => {
    const cleanParams = Helper.clearEmptyField({
      page: pageParameter,
      accountType: currentAccountType.toUpperCase(),
    });
    const res = await axiosInstance.current?.get(
      `${projects}/user-created-projects`,
      {
        params: { ...cleanParams },
      },
    );
    return res?.data;
  };
};
export const useGetExternalProjectsInfiniteQueryApi = () => {
  const { currentAccountType } = useAppContext();
  const axiosInstance = usePrivateRequest(BASE_URL);
  return async ({ pageParam: pageParameter = 1 }): Promise<ServerProjects> => {
    const cleanParams = Helper.clearEmptyField({
      page: pageParameter,
      accountType: currentAccountType.toUpperCase(),
    });
    const res = await axiosInstance.current?.get(`${projects}/external`, {
      params: { ...cleanParams },
    });
    return res?.data;
  };
};
export const useGetAvailableProjectsInfiniteQueryApi = () => {
  const { currentAccountType } = useAppContext();
  const axiosInstance = usePrivateRequest(BASE_URL);
  return async ({ pageParam: pageParameter = 1 }): Promise<ServerProjects> => {
    const cleanParams = Helper.clearEmptyField({
      page: pageParameter,
      accountType: currentAccountType.toUpperCase(),
    });
    const res = await axiosInstance.current?.get(
      `${projects}/projects-not-created-by-user`,
      {
        params: { ...cleanParams },
      },
    );
    return res?.data;
  };
};
export const useGetProjectsUserMemberOfInfiniteQueryApi = () => {
  const { currentAccountType } = useAppContext();
  const axiosInstance = usePrivateRequest(BASE_URL);
  return async ({ pageParam: pageParameter = 1 }): Promise<ServerProjects> => {
    const cleanParams = Helper.clearEmptyField({
      page: pageParameter,
      accountType: currentAccountType.toUpperCase(),
    });
    const res = await axiosInstance.current?.get(`${projects}/member-of`, {
      params: { ...cleanParams },
    });
    return res?.data;
  };
};
export const useGetProjectsByTeamRefInfiniteQueryApi = () => {
  const { currentAccountType } = useAppContext();
  const axiosInstance = usePrivateRequest(BASE_URL);
  return async ({ pageParam, queryKey }: any): Promise<ServerProjects> => {
    const res = await axiosInstance.current?.get(
      `${projects}/team/${queryKey[1].teamRef}`,
      {
        params: {
          accountType: currentAccountType.toUpperCase(),
          page: pageParam,
        },
      },
    );
    return res?.data;
  };
};
export const useGetProjectsByTeamRefApi = () => {
  const { currentAccountType } = useAppContext();
  const axiosInstance = usePrivateRequest(BASE_URL);
  const getProjectsByTeamRef = async (
    teamRef: string,
  ): Promise<ServerProjects> => {
    const res = await axiosInstance.current?.get(
      `${projects}/team/${teamRef}`,
      {
        params: {
          accountType: currentAccountType.toUpperCase(),
        },
      },
    );
    return res?.data;
  };
  return getProjectsByTeamRef;
};

export const useGetAvailableProjectsApi = () => {
  const { currentAccountType } = useAppContext();
  const axiosInstance = usePrivateRequest(BASE_URL);
  return async (params: {
    pageSize?: number;
    page?: number;
    search?: string | null;
    countries?: string[];
    categoryRefs?: string[];
  }): Promise<ServerProjects> => {
    const newParams = {
      ...params,
      categoryRefs: params?.categoryRefs?.join(','),
      countries: params?.countries?.join(','),
      accountType: currentAccountType.toUpperCase(),
    };
    const cleanparams = Helper.clearEmptyField(newParams);
    const res = await axiosInstance.current?.get(
      `${projects}/projects-not-created-by-user`,
      {
        params: params.search
          ? {
              search: params.search,
              accountType: currentAccountType.toUpperCase(),
            }
          : { ...cleanparams },
      },
    );
    return res?.data;
  };
};
export const useGetExternalProjectsApi = () => {
  const { currentAccountType } = useAppContext();
  const axiosInstance = usePrivateRequest(BASE_URL);
  return async (params: {
    pageSize?: number;
    page?: number;
    search?: string | null;
    countries?: string[];
    categoryRefs?: string[];
  }): Promise<ServerProjects> => {
    const newParams = {
      ...params,
      categoryRefs: params?.categoryRefs?.join(','),
      countries: params?.countries?.join(','),
      accountType: currentAccountType.toUpperCase(),
    };
    const cleanparams = Helper.clearEmptyField(newParams);
    const res = await axiosInstance.current?.get(`${projects}/external`, {
      params: params.search
        ? {
            search: params.search,
            accountType: currentAccountType.toUpperCase(),
          }
        : { ...cleanparams },
    });
    return res?.data;
  };
};
export const useGetProjectApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);
  const getProject = async (projectRef: string): Promise<ServerProject> => {
    const res = await axiosInstance.current?.get(`${projects}/${projectRef}`);
    return res?.data;
  };
  return getProject;
};

export const useGetProjectMembersApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);
  const getProjectMembers = async (params: {
    projectRef: string;
  }): Promise<ServerProjectMembers> => {
    const res = await axiosInstance.current?.get(
      `${projects}/${params?.projectRef}/project-team-members`,
    );
    return res?.data;
  };
  return getProjectMembers;
};
export const useGetProjectAdminsApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);
  return async (params: {
    projectRef: string;
  }): Promise<ServerProjectAdmins> => {
    const res = await axiosInstance.current?.get(
      `${projects}/${params?.projectRef}/admins`,
    );
    return res?.data;
  };
};

export const useUpdateProjectMemberRoleApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);
  const updateProjectMemberRole = async (payload: {
    projectRef: string;
    userId: string;
    projectRole: ProjectlevelUserRoleType;
  }): Promise<any> => {
    const res = await axiosInstance.current?.patch(
      `${projects}/update-project-member-role`,
      { ...payload },
    );
    return res?.data;
  };
  return updateProjectMemberRole;
};

export const useGetUdemyCoursesApi = () => {
  const getUdemyCourses = async (params: {
    pageSize?: number;
    page?: number;
    search?: string | null;
    categoryRefList?: string[];
  }): Promise<ServerUdemyCourses> => {
    const newParams = {
      ...params,
      categoryRefList: params?.categoryRefList?.join(','),
    };
    const cleanParams = Helper.clearEmptyField(newParams);
    const res = await publicRequest(BASE_URL).get(`${udemyCourses}/public`, {
      params: { ...cleanParams },
    });
    return res?.data;
  };
  return getUdemyCourses;
};

export const useGetCourseraCoursesApi = () => {
  const getCourseraCourses = async (params: {
    pageSize?: number;
    page?: number;
    search?: string | null;
    categoryRefList?: string[];
  }): Promise<ServerCourseraCourses> => {
    const newParams = {
      ...params,
      categoryRefList: params?.categoryRefList?.join(','),
    };
    const cleanParams = Helper.clearEmptyField(newParams);
    const res = await publicRequest(BASE_URL).get(`${courseraCourses}/public`, {
      params: { ...cleanParams },
    });
    return res?.data;
  };
  return getCourseraCourses;
};

export const useGetUdacityCoursesApi = () => {
  const getUdacityCourses = async (params: {
    pageSize?: number;
    page?: number;
    search?: string | null;
    categoryRefList?: string[];
  }): Promise<ServerUdacityCourses> => {
    const newParams = {
      ...params,
      categoryRefList: params?.categoryRefList?.join(','),
    };
    const cleanParams = Helper.clearEmptyField(newParams);
    const res = await publicRequest(BASE_URL).get(`${udacityCourses}/public`, {
      params: { ...cleanParams },
    });
    return res?.data;
  };
  return getUdacityCourses;
};

export const useGetUdemyCourseApi = () => {
  const getUdemyCourses = async (
    courseRef: string,
  ): Promise<ServerUdemyCourse> => {
    const res = await publicRequest(BASE_URL).get(
      `${udemyCourses}/public/${courseRef}`,
    );
    return res?.data;
  };
  return getUdemyCourses;
};

export const useGetCourseraCourseApi = () => {
  const getCourseraCourse = async (
    courseRef: string,
  ): Promise<ServerCourseraCourse> => {
    const res = await publicRequest(BASE_URL).get(
      `${courseraCourses}/public/${courseRef}`,
    );
    return res?.data;
  };
  return getCourseraCourse;
};

export const useGetUdacityCourseApi = () => {
  const getUdacityCourse = async (
    courseRef: string,
  ): Promise<ServerUdacityCourse> => {
    const res = await publicRequest(BASE_URL).get(
      `${udacityCourses}/public/${courseRef}`,
    );
    return res?.data;
  };
  return getUdacityCourse;
};

export const useGetWishlistApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);

  const getWishlist = async (): Promise<ServerWishlistCourses> => {
    const res = await axiosInstance?.current?.get(wishlist);
    return res?.data;
  };
  return getWishlist;
};

export const useAddToWishlistApi = (courseRef: string) => {
  const axiosInstance = usePrivateRequest(BASE_URL);

  const addToWishlist = async (): Promise<any> => {
    const res = await axiosInstance?.current?.get(`${wishlist}/${courseRef}`);
    return res?.data;
  };
  return addToWishlist;
};

export const useGetMyCoursesApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);

  const getUdemyCourses = async (params: {
    pageSize?: number;
    page?: number;
    search?: string | null;
    provider?: string;
  }): Promise<ServerMyCourses> => {
    const cleanParams = Helper.clearEmptyField(params);
    const res = await axiosInstance?.current?.get(myCourses, {
      params: { ...cleanParams },
    });
    return res?.data;
  };
  return getUdemyCourses;
};

export const useAddToMyCoursesApi = (courseRef: string) => {
  const axiosInstance = usePrivateRequest(BASE_URL);

  const addToWishlist = async (): Promise<any> => {
    const res = await axiosInstance?.current?.post(`${myCourses}/${courseRef}`);
    return res?.data;
  };
  return addToWishlist;
};

export const useGetUdemyCourseActivitiesApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);

  const getUdemyCourseActivities = async (
    params: ICourseActivitiesPayload,
  ): Promise<ServerUdemyCourseActivity> => {
    const res = await axiosInstance?.current?.get(`${courseActivities}/udemy`, {
      params,
    });
    return res?.data;
  };
  return getUdemyCourseActivities;
};

export const useGetCourseraCourseActivitiesApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);

  const getCourseraCourseActivities = async (
    params: ICourseActivitiesPayload,
  ): Promise<ServerCourseraCourseActivity> => {
    const res = await axiosInstance?.current?.get(
      `${courseActivities}/coursera`,
      {
        params,
      },
    );
    return res?.data;
  };
  return getCourseraCourseActivities;
};

export const useGetCoursePrivilegeApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);

  const { data } = useUserContext();

  const getCoursePrivilege = async (
    params: ICoursePrivilegesPayload,
  ): Promise<ServerCoursePrivileges> => {
    const res = await axiosInstance?.current?.get(
      `${myCourses}/validate-privilege`,
      {
        params: {
          ...params,
          userEmail: data?.user?.email,
        },
      },
    );
    return res?.data;
  };
  return getCoursePrivilege;
};

export const useGetProjectTeamsApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);
  const getProjectTeams = async (
    projectRef: string,
  ): Promise<ServerProjectTeams> => {
    const res = await axiosInstance.current?.get(
      `${projects}/${projectRef}/teams`,
    );
    return res?.data;
  };
  return getProjectTeams;
};
export const useProcessExternalTeamInviteApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);
  return async (payload: {
    projectRef: string;
    teamRef: string;
    accept: boolean;
  }): Promise<any> => {
    const cleanParams = Helper.clearEmptyField(payload);
    const res = await axiosInstance.current?.post(
      `${projects}/teams/process-invite`,
      { ...cleanParams },
    );
    return res?.data;
  };
};
export const useProcessJoinRequestApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);
  return async (payload: {
    projectRef: string;
    teamRef: string;
    accept: boolean;
  }): Promise<any> => {
    const cleanParams = Helper.clearEmptyField(payload);
    const res = await axiosInstance.current?.post(
      `${projects}/process-join-request`,
      { ...cleanParams },
    );
    return res?.data;
  };
};

export const useUpdateProjectApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);
  return async (payload: Partial<IAddProjectPayload>): Promise<any> => {
    const { documents, ...rest } = payload;
    const dateValue = [payload?.startDate, payload?.endDate] as Value;
    const data = Helper.createFormData(
      Helper.clearEmptyField({
        ...rest,
        startDate: payload?.startDate
          ? Helper.formatDateToString(dateValue, 0)
          : undefined,
        endDate: payload?.endDate
          ? Helper.formatDateToString(dateValue, 1)
          : undefined,
      }),
    );

    if (Array.isArray(documents) && documents?.length > 0) {
      for (let i = 0; i < documents.length; i++) {
        data.append('documents', documents[i]);
      }
    }
    const res = await axiosInstance.current?.patch(`${projects}`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
    return res?.data;
  };
};
export const useUpdateProjectStatusApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);
  return async (payload: Partial<IAddProjectPayload>): Promise<any> => {
    const data = Helper.createFormData(payload);
    const res = await axiosInstance.current?.patch(`${projects}`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
    return res?.data;
  };
};
export const useUpdateProjectVisibilityApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);
  return async (payload: {
    visibility: VisibilityType;
    projectRef: string;
  }): Promise<any> => {
    const data = Helper.createFormData(payload);
    const res = await axiosInstance.current?.patch(`${projects}`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
    return res?.data;
  };
};

export const useUnlinkProblemStatementApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);
  return async (payload: {
    problemStatementRef: string;
    projectRef: string;
  }): Promise<any> => {
    const { problemStatementRef, projectRef } = payload;
    const res = await axiosInstance.current?.patch(
      `${projects}/${projectRef}/un-link-problem-statement/${problemStatementRef}`,
    );
    return res?.data;
  };
};

export const useDeleteProjectFilesApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);
  return async (payload: IDeleteProjectFilesPayload): Promise<any> => {
    const res = await axiosInstance.current?.delete(`${projects}/files`, {
      data: payload,
    });
    return res?.data;
  };
};
