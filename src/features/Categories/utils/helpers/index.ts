import { TeamlevelUserRoleType } from '../../../../types';
import { Helper } from '../../../../utils/helpers';
import J<PERSON>Zip from 'jszip';

export class CategoryHelper extends Helper {
  public static getCategoryStatusFilterApiRequest() {}
  public static createReponseOptions(input: TeamlevelUserRoleType) {
    return {
      label: Helper.capitalizeString(input),
      value: input,
    };
  }
  public static createCategoryOptionsArr = (array: any[]) => {
    return array.map(({ categoryName }) => {
      return { label: categoryName, value: categoryName };
    });
  };
}

interface ProjectAttachment {
  fileRef: string;
  url: string;
  name: string;
  size: string;
  createdAt: string;
}

/**
 * Proxies a file URL through our API to avoid CORS issues
 * @param fileUrl - The original file URL
 * @returns The proxied file URL
 */
export function getProxiedFileUrl(fileUrl: string): string {
  // If it's already a local URL or data URL, return as is
  if (fileUrl.startsWith('data:') || fileUrl.startsWith('/')) {
    return fileUrl;
  }

  // If it's a relative URL, return as is
  if (!fileUrl.startsWith('http')) {
    return fileUrl;
  }

  // Don't proxy YouTube URLs
  const url = fileUrl.toLowerCase();
  if (
    url.includes('youtube.com') ||
    url.includes('youtu.be') ||
    url.includes('youtube-nocookie.com')
  ) {
    return fileUrl;
  }

  // For now, return the original URL and let the browser handle CORS
  // If CORS issues occur, you may need to implement a backend proxy
  return fileUrl;
}

/**
 * Checks if a file URL needs to be proxied
 * @param fileUrl - The file URL to check
 * @returns True if the URL should be proxied
 */
export function shouldProxyFile(fileUrl: string): boolean {
  // Don't proxy data URLs, local URLs, or relative URLs
  if (
    fileUrl.startsWith('data:') ||
    fileUrl.startsWith('/') ||
    !fileUrl.startsWith('http')
  ) {
    return false;
  }

  // Don't proxy YouTube URLs
  const url = fileUrl.toLowerCase();
  if (
    url.includes('youtube.com') ||
    url.includes('youtu.be') ||
    url.includes('youtube-nocookie.com')
  ) {
    return false;
  }

  // For now, return false since we're not using a proxy
  // If CORS issues occur, you may need to implement a backend proxy
  return false;
}

/**
 * Checks if a URL is downloadable (not a streaming service)
 */
function isDownloadableUrl(url: string): boolean {
  const lowerUrl = url.toLowerCase();
  const nonDownloadableDomains = [
    'youtube.com',
    'youtu.be',
    'youtube-nocookie.com',
    'vimeo.com',
    'twitch.tv',
    'facebook.com',
    'instagram.com',
    'twitter.com',
    'x.com',
    'tiktok.com',
  ];

  return !nonDownloadableDomains.some(domain => lowerUrl.includes(domain));
}

interface ProjectAttachment {
  fileRef: string;
  url: string;
  name: string;
  size: string;
  createdAt: string;
}

export const downloadAllFilesAsZipRobust = async (
  files: ProjectAttachment[],
  projectName: string,
  _onProgress?: (progress: number) => void,
  onError?: (error: string) => void,
) => {
  try {
    const zip = new JSZip();

    // Filter out non-downloadable files (like YouTube links)
    const downloadableFiles = files.filter(file => isDownloadableUrl(file.url));

    const totalFiles = downloadableFiles.length;

    if (totalFiles === 0) {
      if (onError) {
        onError('No downloadable files found');
      }
      return;
    }

    // Generate ZIP with optimized settings
    const zipBlob = await zip.generateAsync({
      type: 'blob',
      compression: 'DEFLATE',
      compressionOptions: {
        level: 6, // Balanced compression
      },
      streamFiles: true, // Better for large files
    });

    // Create download
    const url = URL.createObjectURL(zipBlob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${projectName.replace(/[<>:"/\\|?*]/g, '_')}_files.zip`;
    a.style.display = 'none';

    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);

    // Clean up
    setTimeout(() => {
      URL.revokeObjectURL(url);
    }, 1000);
  } catch (error) {
    console.error('ZIP creation failed:', error);
    if (onError) {
      onError(`ZIP creation failed: ${error}`);
    }
  }
};
