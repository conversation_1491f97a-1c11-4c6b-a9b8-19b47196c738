import {
  ExtractData,
  ProjectlevelUserRoleType,
  ProjectStatusValues,
  ServerProject,
  ServerProjects,
  ValuesUnion,
  VisibilityType,
} from '../../types';
import { COURSE_SECTIONS } from './data/constant';

interface IAddProjectPayload {
  projectName: string;
  projectCountries: string[];
  sdgCategoryRefs: string[];
  projectLevel?: string | null;
  presumedProjectCost?: string | null;
  projectDescription?: string;
  projectImage?: File | FileList | undefined;
  problemStatementRef?: string | null;
  startDate: string | Date | null;
  endDate: string | Date | null;
  projectStatus?: ProjectStatusValues;
  descriptionVideoUrl?: string | null;
  documents?: File[] | undefined;
  visibility?: VisibilityType;
}
interface IAddProblemStatementPayload {
  title: string;
  categories: string[];
  subcategories: string[];
  countries: string[];
  description: string;
  descriptionVideoUrl: string;
  descriptionFile: File;
}

type Project = Omit<
  ExtractData<ServerProject>,
  'createdBy' | 'problemStatementRef' | 'createdByName'
>;
type Projects = ExtractData<ServerProjects>;
interface IInviteToJoinProjectPayload {
  firstName: string;
  lastName: string;
  email: string;
  projectRole: ProjectlevelUserRoleType;
}

interface ICourseActivitiesPayload {
  courseId: number | undefined;
  userEmail: string;
}

interface ICoursePrivilegesPayload {
  courseRef: string;
  provider: string;
}

type StudentCourseTypes = ValuesUnion<typeof COURSE_SECTIONS>;
interface IUpdateProjectDescriptionPayload {
  projectDescription?: string;
}

interface IUpdateProjectImagePayload {
  projectImage?: FileList | File;
}

interface IUpdateProjectPayload {
  projectCountries?: string[];
  sdgCategoryRefs?: string[];
  projectLevel?: string | null;
  startDate?: Date | string | null;
  endDate?: Date | string | null;
  projectStatus?: ProjectStatusValues;
  presumedProjectCost?: string | null;
  projectName?: string;
  descriptionVideoUrl?: string | null;
  documents?: File[] | undefined;
  projectDescription?: string;
  projectImage?: File | FileList | undefined;
}

interface IUpdateProjectAttachmentPayload {
  descriptionVideoUrl?: string | null;
  documents?: File[] | undefined;
}

interface IAddProblemStatementToProject {
  problemStatementRef?: string;
}

interface IDeleteProjectFilesPayload {
  projectRef: string;
  documentRefs?: string[];
  descriptionVideoUrlRefs?: string[];
}

export type {
  IAddProblemStatementPayload,
  IAddProjectPayload,
  ICourseActivitiesPayload,
  ICoursePrivilegesPayload,
  IInviteToJoinProjectPayload,
  Project,
  Projects,
  StudentCourseTypes,
  IUpdateProjectDescriptionPayload,
  IUpdateProjectImagePayload,
  IUpdateProjectPayload,
  IAddProblemStatementToProject,
  IDeleteProjectFilesPayload,
  IUpdateProjectAttachmentPayload,
};
