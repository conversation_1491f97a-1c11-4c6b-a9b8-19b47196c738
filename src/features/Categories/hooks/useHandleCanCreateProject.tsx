import { useCallback, useState } from 'react';

import { useAppContext } from '../../../context/event/AppEventContext';
import useHandleApiFeebackWithToast from '../../../hooks/useHandleApiFeebackWithToast';
import { useGetCanCreateProject } from './apiQueryHooks/eduQueryHooks';
import useManageActiveSubscription from '@/hooks/useManageActiveSubscription';

export const useHandleCanCreateProject = ({
  onSuccess,
}: {
  onSuccess: () => void;
}) => {
  const { setShowModalHandler } = useAppContext();
  const [trigger, setTrigger] = useState<boolean>(false);
  const { isInactiveOrExpiredOrCancelled } = useManageActiveSubscription();
  const { isFetching } = useGetCanCreateProject({
    enabled: trigger,
    keepPreviousData: false,
    ...useHandleApiFeebackWithToast({
      next: data => {
        if (data) onSuccess();
        else setShowModalHandler('ProjectLimitModal');
        setTrigger(false);
      },
      onError: () => setTrigger(false),
      noSuccessToast: true,
    }),
  });

  const handleTrigger = useCallback(() => {
    if (isInactiveOrExpiredOrCancelled) {
      setShowModalHandler('InactiveSubscriptionModal');
      return;
    }
    setTrigger(true);
  }, [trigger]);

  return { isFetching, handleTrigger };
};
