import { useInfiniteQuery, useMutation, useQuery } from '@tanstack/react-query';

import {
  ServerCourseraCourse,
  ServerCourseraCourses,
  ServerMyCourses,
  ServerUdacityCourse,
  ServerUdacityCourses,
  ServerUdemyCourse,
  ServerUdemyCourses,
  ServerWishlistCourses,
} from '../../../../types';
import { Helper } from '../../../../utils/helpers';
import {
  ADD_EXTERNAL_TEAM_TO_PROJECT_QUERY,
  ADD_MY_TEAM_TO_AVAILABLE_PROJECT_QUERY,
  ADD_PROBLEM_STATEMENT_QUERY,
  ADD_PROJECT_QUERY,
  ADD_TEAM_TO_PROJECT_QUERY,
  ADD_TO_MY_COURSES_QUERY,
  ADD_TO_WISHLIST_QUERY,
  DELETE_PROJECT_FILES_QUERY,
  GET_AVAILABLE_PROJECTS_INFINITE_QUERY,
  GET_AVAILABLE_PROJECTS_QUERY,
  GET_CAN_CREATE_PROJECT_QUERY,
  GET_COURSE_ACTIVITIES_QUERY,
  GET_COURSERA_COURSES_QUERY,
  GET_EXTERNAL_PROJECTS_INFINITE_QUERY,
  GET_EXTERNAL_PROJECTS_QUERY,
  GET_MY_COURSES_QUERY,
  GET_PROJECT_ADMINS_QUERY,
  GET_PROJECT_MEMBERS_QUERY,
  GET_PROJECT_QUERY,
  GET_PROJECT_TEAMS_QUERY,
  GET_PROJECTS_INFINITE_QUERY,
  GET_PROJECTS_QUERY,
  GET_PROJECTS_TEAMREF_INFINITE_QUERY,
  GET_PROJECTS_USER_MEMBEROF_INFINITE_QUERY,
  GET_STUDENT_WISHLIST_QUERY,
  GET_UDACITY_COURSES_QUERY,
  GET_UDEMY_COURSE_QUERY,
  GET_UDEMY_COURSES_QUERY,
  INVITE_TO_JOIN_PROJECT_QUERY,
  PROCESS_EXTERNAL_TEAM_INVITE_QUERY,
  PROCESS_JOIN_REQUEST_QUERY,
  PROCESS_REQUEST_TO_JOIN_PROJECT_QUERY,
  REMOVE_TEAM_FROM_PROJECT_QUERY,
  REQUEST_TO_JOIN_PROJECT_QUERY,
  UNLINK_PROBLEM_STATEMENT_QUERY,
  UPDATE_PROJECT_MEMBERS_QUERY,
  UPDATE_PROJECT_QUERY,
  UPDATE_PROJECT_STATUS_QUERY,
  UPDATE_PROJECT_VISIBILITY_QUERY,
  VALIDATE_COURSE_PRIVILEGES_QUERY,
} from '../../../../utils/queryKeys';
import {
  useAddExternalTeamToProjectApi,
  useAddMyTeamToAvailableProjectApi,
  useAddProblemStatementApi,
  useAddProjectApi,
  useAddTeamToProjectApi,
  useAddToMyCoursesApi,
  useAddToWishlistApi,
  useDeleteProjectFilesApi,
  useGetAvailableProjectsApi,
  useGetAvailableProjectsInfiniteQueryApi,
  useGetCanCreateProjectApi,
  useGetCoursePrivilegeApi,
  useGetCourseraCourseActivitiesApi,
  useGetCourseraCourseApi,
  useGetCourseraCoursesApi,
  useGetExternalProjectsApi,
  useGetExternalProjectsInfiniteQueryApi,
  useGetMyCoursesApi,
  useGetProjectAdminsApi,
  useGetProjectApi,
  useGetProjectMembersApi,
  useGetProjectsApi,
  useGetProjectsByTeamRefApi,
  useGetProjectsByTeamRefInfiniteQueryApi,
  useGetProjectsInfiniteQueryApi,
  useGetProjectsUserMemberOfInfiniteQueryApi,
  useGetProjectTeamsApi,
  useGetUdacityCourseApi,
  useGetUdacityCoursesApi,
  useGetUdemyCourseActivitiesApi,
  useGetUdemyCourseApi,
  useGetUdemyCoursesApi,
  useGetWishlistApi,
  useInviteToJoinProjectApi,
  useProcessExternalTeamInviteApi,
  useProcessJoinRequestApi,
  useProcessRequestToJoinAProjectApi,
  useRemoveTeamFromProjectApi,
  useRequestToJoinAProjectApi,
  useUnlinkProblemStatementApi,
  useUpdateProjectApi,
  useUpdateProjectMemberRoleApi,
  useUpdateProjectStatusApi,
  useUpdateProjectVisibilityApi,
} from '../../services/eduApiRequests';
import {
  ICourseActivitiesPayload,
  ICoursePrivilegesPayload,
} from '../../types';

export const useGetCanCreateProject = (options = {}) => {
  const getCanAddProject = useGetCanCreateProjectApi();
  return useQuery([GET_CAN_CREATE_PROJECT_QUERY], () => getCanAddProject(), {
    staleTime: 0,
    cacheTime: 0,
    ...options,
  });
};

export const useRequestToJoinAProject = (
  path: {
    projectRef: string;
  } = { projectRef: '' },
  options = {},
) => {
  const requestToJoinProject = useRequestToJoinAProjectApi();
  return useQuery(
    [REQUEST_TO_JOIN_PROJECT_QUERY, path],
    () => requestToJoinProject(path),
    {
      ...options,
    },
  );
};
export const useAddProject = (options = {}) => {
  const addProject = useAddProjectApi();
  return useMutation(addProject, {
    mutationKey: [ADD_PROJECT_QUERY],
    ...options,
  });
};
export const useAddTeamToProject = (options = {}) => {
  const addTeamToProject = useAddTeamToProjectApi();
  return useMutation(addTeamToProject, {
    mutationKey: [ADD_TEAM_TO_PROJECT_QUERY],
    ...options,
  });
};
export const useRemoveTeamFromProject = (options = {}) => {
  const removeTeamFromProject = useRemoveTeamFromProjectApi();
  return useMutation(removeTeamFromProject, {
    mutationKey: [REMOVE_TEAM_FROM_PROJECT_QUERY],
    ...options,
  });
};
export const useAddExternalTeamToProject = (options = {}) => {
  const addExternalTeamToProject = useAddExternalTeamToProjectApi();
  return useMutation(addExternalTeamToProject, {
    mutationKey: [ADD_EXTERNAL_TEAM_TO_PROJECT_QUERY],
    ...options,
  });
};
export const useAddMyTeamToAvailableProject = (options = {}) => {
  const addMyTeamToAvailableProject = useAddMyTeamToAvailableProjectApi();
  return useMutation(addMyTeamToAvailableProject, {
    mutationKey: [ADD_MY_TEAM_TO_AVAILABLE_PROJECT_QUERY],
    ...options,
  });
};
export const useAddProblemStatement = (options = {}) => {
  const addProblemStatement = useAddProblemStatementApi();
  return useMutation(addProblemStatement, {
    mutationKey: [ADD_PROBLEM_STATEMENT_QUERY],
    ...options,
  });
};

export const useProcessRequestToJoinProject = (
  params: {
    userId: string;
    decision: string;
    projectRef: string;
  } = { userId: '', decision: '', projectRef: '' },
  options = {},
) => {
  const processRequestToJoinProduct = useProcessRequestToJoinAProjectApi();
  return useQuery(
    [PROCESS_REQUEST_TO_JOIN_PROJECT_QUERY, params],
    () => processRequestToJoinProduct(params),
    {
      ...options,
    },
  );
};

export const useInviteToJoinProject = (options = {}) => {
  const inviteToJoinProject = useInviteToJoinProjectApi();
  return useMutation(inviteToJoinProject, {
    mutationKey: [INVITE_TO_JOIN_PROJECT_QUERY],
    ...options,
  });
};
export const useUpdateProjectMemberRole = (options = {}) => {
  const updateProjectMemberRole = useUpdateProjectMemberRoleApi();
  return useMutation(updateProjectMemberRole, {
    mutationKey: [UPDATE_PROJECT_MEMBERS_QUERY],
    ...options,
  });
};

export const useGetProjects = (
  {
    pageSize = 10,
    page = 1,
    search = '',
    countries = [],
    categoryRefs = [],
  }: {
    pageSize?: number;
    page?: number;
    search?: string | null;
    countries?: string[];
    categoryRefs?: string[];
  } = {},
  options = {},
) => {
  const getProjects = useGetProjectsApi();
  return useQuery(
    [GET_PROJECTS_QUERY, { pageSize, page, search, countries, categoryRefs }],
    () => getProjects({ pageSize, page, search, countries, categoryRefs }),
    {
      ...options,
    },
  );
};

export const useGetProjectsInfiniteQuery = (options = {}) => {
  const getMyProjects = useGetProjectsInfiniteQueryApi();
  return useInfiniteQuery({
    queryKey: [GET_PROJECTS_INFINITE_QUERY],
    queryFn: getMyProjects,
    getNextPageParam: (lastPage, allPages) => {
      const nextPage = lastPage?.data?.projects.length
        ? allPages.length + 1
        : undefined;
      return nextPage;
    },
    ...options,
  });
};
export const useGetExternalProjectsInfiniteQuery = (options = {}) => {
  const getExternalProjects = useGetExternalProjectsInfiniteQueryApi();
  return useInfiniteQuery({
    queryKey: [GET_EXTERNAL_PROJECTS_INFINITE_QUERY],
    queryFn: getExternalProjects,
    getNextPageParam: (lastPage, allPages) => {
      const nextPage = lastPage?.data?.projects.length
        ? allPages.length + 1
        : undefined;
      return nextPage;
    },
    ...options,
  });
};
export const useGetAvailableProjectsInfiniteQuery = (options = {}) => {
  const getExternalProjects = useGetAvailableProjectsInfiniteQueryApi();
  return useInfiniteQuery({
    queryKey: [GET_AVAILABLE_PROJECTS_INFINITE_QUERY],
    queryFn: getExternalProjects,
    getNextPageParam: (lastPage, allPages) => {
      const nextPage = lastPage?.data?.projects.length
        ? allPages.length + 1
        : undefined;
      return nextPage;
    },
    ...options,
  });
};
export const useGetProjectsUserMemberOfInfiniteQuery = (options = {}) => {
  const caller = useGetProjectsUserMemberOfInfiniteQueryApi();
  return useInfiniteQuery({
    queryKey: [GET_PROJECTS_USER_MEMBEROF_INFINITE_QUERY],
    queryFn: caller,
    getNextPageParam: (lastPage, allPages) => {
      const nextPage = lastPage?.data?.projects.length
        ? allPages.length + 1
        : undefined;
      return nextPage;
    },
    ...options,
  });
};
export const useGetProjectByTeamRefInfiniteQuery = (
  params = {},
  options = {},
) => {
  const getProjectsByTeamRef = useGetProjectsByTeamRefInfiniteQueryApi();
  return useInfiniteQuery({
    queryKey: [GET_PROJECTS_TEAMREF_INFINITE_QUERY, params],
    queryFn: arg => getProjectsByTeamRef(arg as any),
    getNextPageParam: (lastPage, allPages) => {
      const nextPage = lastPage?.data?.projects.length
        ? allPages.length + 1
        : undefined;
      return nextPage;
    },
    ...options,
  });
};
export const useGetProjectsByTeamRef = (
  params: { teamRef: string },
  options = {},
) => {
  const getProjectsByTeamRef = useGetProjectsByTeamRefApi();
  return useQuery(
    [GET_PROJECTS_QUERY, params.teamRef],
    () => getProjectsByTeamRef(params.teamRef),
    {
      ...options,
    },
  );
};

export const useGetAvailableProjects = (
  {
    pageSize = 10,
    page = 1,
    search = '',
    countries = [],
    categoryRefs = [],
  }: {
    pageSize?: number;
    page?: number;
    search?: string | null;
    countries?: string[];
    categoryRefs?: string[];
  } = {},
  options = {},
) => {
  const getProjects = useGetAvailableProjectsApi();
  return useQuery(
    [
      GET_AVAILABLE_PROJECTS_QUERY,
      { pageSize, page, search, countries, categoryRefs },
    ],
    () => getProjects({ pageSize, page, search, countries, categoryRefs }),
    {
      ...options,
    },
  );
};
export const useGetExternalProjects = (
  {
    pageSize = 10,
    page = 1,
    search = '',
    countries = [],
    categoryRefs = [],
  }: {
    pageSize?: number;
    page?: number;
    search?: string | null;
    countries?: string[];
    categoryRefs?: string[];
  } = {},
  options = {},
) => {
  const getProjects = useGetExternalProjectsApi();
  return useQuery(
    [
      GET_EXTERNAL_PROJECTS_QUERY,
      { pageSize, page, search, countries, categoryRefs },
    ],
    () => getProjects({ pageSize, page, search, countries, categoryRefs }),
    {
      ...options,
    },
  );
};
export const useGetProject = (projectRef: string, options = {}) => {
  const getProject = useGetProjectApi();
  return useQuery(
    [GET_PROJECT_QUERY, projectRef],
    () => getProject(projectRef),
    {
      ...options,
    },
  );
};

export const useGetProjectMembers = (
  params: {
    projectRef: string;
  } = {
    projectRef: '',
  },
  options = {},
) => {
  const getProjectMembers = useGetProjectMembersApi();
  return useQuery(
    [GET_PROJECT_MEMBERS_QUERY, params],
    () => getProjectMembers(params),
    {
      ...options,
    },
  );
};
export const useGetProjectAdmins = (
  params: {
    projectRef: string;
  } = {
    projectRef: '',
  },
  options = {},
) => {
  const getProjectAdmin = useGetProjectAdminsApi();
  return useQuery(
    [GET_PROJECT_ADMINS_QUERY, params],
    () => getProjectAdmin(params),
    {
      ...options,
    },
  );
};

export const useGetUdemyCourses = (
  params: {
    pageSize?: number;
    page?: number;
    search?: string | null;
    categoryRefList?: string[];
  } = {
    pageSize: 10,
    page: 1,
    search: '',
    categoryRefList: [],
  },
  options = {},
) => {
  const getUdemyCourses = useGetUdemyCoursesApi();
  return useQuery(
    [GET_UDEMY_COURSES_QUERY, params],
    () => getUdemyCourses(params),
    {
      select: (data: ServerUdemyCourses) => ({
        ...data,
        data: {
          ...data?.data,
          UdemyCourses: data?.data?.UdemyCourses.map(course => ({
            ...course,
            duration: course?.estimatedContentLength,
            courseImageUrl: course?.imageUrls?.size_480x270,
            courseImageSrcSet: Helper.getSrcSet(course.imageUrls),
          })),
        },
      }),
      ...options,
    },
  );
};

export const useGetCourseraCourses = (
  params: {
    pageSize?: number;
    page?: number;
    search?: string | null;
    categoryRefList?: string[];
  } = {
    pageSize: 8,
    page: 1,
    search: '',
    categoryRefList: [],
  },
  options = {},
) => {
  const getCourseraCourses = useGetCourseraCoursesApi();
  return useQuery(
    [GET_COURSERA_COURSES_QUERY, params],
    () => getCourseraCourses(params),
    {
      select: (data: ServerCourseraCourses) => ({
        ...data,
        data: {
          ...data?.data,
          CourseraCourses: data?.data?.['Coursera Courses'].map(course => ({
            ...course,
            courseImageUrl: course?.promoPhotoUrl,
            duration: course?.estimatedContentLength,
            categoryRef: course.categoryRef,
          })),
        },
      }),
      ...options,
    },
  );
};

export const useGetUdacityCourses = (
  params: {
    pageSize?: number;
    page?: number;
    search?: string | null;
    categoryRefList?: string[];
  } = {
    pageSize: 8,
    page: 1,
    search: '',
    categoryRefList: [],
  },
  options = {},
) => {
  const getUdacityCourses = useGetUdacityCoursesApi();
  return useQuery(
    [GET_UDACITY_COURSES_QUERY, params],
    () => getUdacityCourses(params),
    {
      select: (data: ServerUdacityCourses) => ({
        ...data,
        data: {
          ...data?.data,
          UdacityCourses: data?.data?.['udacity_courses'].map(course => ({
            ...course,
            courseImageUrl: course?.image_url,
            duration: course?.duration,
            categoryRef: course.categoryRef,
          })),
        },
      }),
      ...options,
    },
  );
};

export const useGetUdemyCourse = (courseRef: string, options = {}) => {
  const getUdemyCourse = useGetUdemyCourseApi();
  return useQuery(
    [GET_UDEMY_COURSE_QUERY, courseRef],
    () => getUdemyCourse(courseRef),
    {
      select: (data: ServerUdemyCourse) => ({
        ...data,
        data: {
          ...data.data,
          courseImageUrl: data.data?.imageUrls?.size_480x270,
          courseImageSrcSet: Helper.getSrcSet(data.data.imageUrls),
          categoryRef: data.data.categoryRef,
          duration: data.data?.estimatedContentLength,
        },
      }),
      ...options,
    },
  );
};

export const useGetCourseraCourse = (courseRef: string, options = {}) => {
  const getCourseraCourse = useGetCourseraCourseApi();
  return useQuery(
    [GET_COURSERA_COURSES_QUERY, courseRef],
    () => getCourseraCourse(courseRef),
    {
      select: (data: ServerCourseraCourse) => ({
        ...data,
        data: {
          ...data.data,
          courseImageUrl: data?.data?.promoPhotoUrl,
          categoryRef: data.data.categoryRef,
          duration: 0,
        },
      }),
      ...options,
    },
  );
};

export const useGetUdacityCourse = (courseRef: string, options = {}) => {
  const getUdacityCourse = useGetUdacityCourseApi();
  return useQuery(
    [GET_UDACITY_COURSES_QUERY, courseRef],
    () => getUdacityCourse(courseRef),
    {
      select: (data: ServerUdacityCourse) => ({
        ...data,
        data: {
          ...data.data,
          courseImageUrl: data?.data?.image_url,
          categoryRef: data.data.categoryRef,
          duration: data.data.duration,
        },
      }),
      ...options,
    },
  );
};

export const useAddToWishlist = (courseRef: string, options = {}) => {
  const addToWishlist = useAddToWishlistApi(courseRef);

  return useQuery([ADD_TO_WISHLIST_QUERY], () => addToWishlist(), {
    ...options,
    enabled: false,
  });
};

export const useGetStudentWishlists = (options = {}) => {
  const getWishlist = useGetWishlistApi();
  return useQuery([GET_STUDENT_WISHLIST_QUERY], () => getWishlist(), {
    select: (data: ServerWishlistCourses) => ({
      ...data,
      data: {
        ...data.data,
        courses: [
          ...(data?.data?.udemyCourses || []).map(course => ({
            title: course.title,
            courseImageUrl: course?.imageUrls?.size_480x270,
            courseImageSrcSet: Helper.getSrcSet(course.imageUrls),
            courseRef: course.courseRef,
            courseProvider: course.courseProvider,
          })),
          ...(data?.data?.udacityCourses || []).map(course => ({
            title: course.title,
            courseImageUrl: course.image_url,
            courseRef: course.courseRef,
            courseProvider: course.courseProvider,
            courseImageSrcSet: '',
          })),
          ...(data?.data?.courseraCourses || []).map(course => ({
            title: course.title,
            courseImageUrl: course.promoPhotoUrl,
            categoryRef: course.categoryRef,
            courseRef: course.courseRef,
            courseProvider: course.courseProvider,
            courseImageSrcSet: '',
          })),
        ],
      },
    }),
    ...options,
  });
};

export const useGetMyCourses = (
  params: {
    pageSize?: number;
    page?: number;
    search?: string | null;
    provider?: string;
  } = {
    pageSize: 8,
    page: 1,
    search: '',
  },
  options = {},
) => {
  const getMyCourses = useGetMyCoursesApi();
  return useQuery([GET_MY_COURSES_QUERY, params], () => getMyCourses(params), {
    select: (data: ServerMyCourses) => ({
      ...data,
      data: {
        ...data?.data,
        courses: [
          ...(data?.data?.['UDEMY COURSES'] || []).map(course => ({
            ...course,
            courseImageUrl: course?.imageUrls?.size_480x270,
            courseImageSrcSet: Helper.getSrcSet(course.imageUrls),
            duration: course.estimatedContentLength,
            categoryRef: course.categoryRef,
          })),
          ...(data?.data?.['COURSERA COURSES'] || []).map(course => ({
            ...course,
            courseImageUrl: course?.promoPhotoUrl,
            courseImageSrcSet: '',
            categoryRef: course.categoryRef,
          })),
          ...(data?.data?.['UDACITY COURSES'] || []).map(course => ({
            ...course,
            courseImageUrl: course?.image_url,
            courseImageSrcSet: '',
            duration: course.duration,
            categoryRef: course.categoryRef,
          })),
        ],
      },
    }),
    ...options,
  });
};

export const useAddToMyCourses = (courseRef: string, options = {}) => {
  const addToMyCourses = useAddToMyCoursesApi(courseRef);

  return useMutation([ADD_TO_MY_COURSES_QUERY], () => addToMyCourses(), {
    ...options,
  });
};
export const useProcessExternalTeamInvite = (options = {}) => {
  const processExternalTeamInvite = useProcessExternalTeamInviteApi();
  return useMutation(processExternalTeamInvite, {
    mutationKey: [PROCESS_EXTERNAL_TEAM_INVITE_QUERY],
    ...options,
  });
};
export const useProcessJoinRequest = (options = {}) => {
  const processJoinRequest = useProcessJoinRequestApi();
  return useMutation(processJoinRequest, {
    mutationKey: [PROCESS_JOIN_REQUEST_QUERY],
    ...options,
  });
};

export const useGetUdemyCourseActivities = (
  { courseId, userEmail }: ICourseActivitiesPayload,
  options = {},
) => {
  const getUdemyCourseActivities = useGetUdemyCourseActivitiesApi();
  return useQuery(
    [GET_COURSE_ACTIVITIES_QUERY, 'UDEMY', { courseId, userEmail }],
    () => getUdemyCourseActivities({ courseId, userEmail }),
    {
      ...options,
    },
  );
};

export const useGetCourseraCourseActivities = (
  { courseId, userEmail }: ICourseActivitiesPayload,
  options = {},
) => {
  const getetCourseraCourseActivities = useGetCourseraCourseActivitiesApi();
  return useQuery(
    [GET_COURSE_ACTIVITIES_QUERY, 'COURSERA', { courseId, userEmail }],
    () => getetCourseraCourseActivities({ courseId, userEmail }),
    {
      ...options,
    },
  );
};

export const useGetCoursePrivilege = (
  params: ICoursePrivilegesPayload,
  options = {},
) => {
  const getCoursePrivilege = useGetCoursePrivilegeApi();

  return useQuery(
    [VALIDATE_COURSE_PRIVILEGES_QUERY, params],
    () => getCoursePrivilege(params),
    {
      ...options,
      cacheTime: -1,
      enabled: false,
    },
  );
};

export const useGetProjectTeams = (
  params: { projectRef: string },
  options = {},
) => {
  const getProjectTeams = useGetProjectTeamsApi();
  return useQuery(
    [GET_PROJECT_TEAMS_QUERY, params],
    () => getProjectTeams(params?.projectRef),
    {
      ...options,
    },
  );
};

export const useUpdateProject = (options = {}) => {
  const updateProject = useUpdateProjectApi();
  return useMutation(updateProject, {
    mutationKey: [UPDATE_PROJECT_QUERY],
    ...options,
  });
};
export const useUpdateProjectVisibility = (options = {}) => {
  const caller = useUpdateProjectVisibilityApi();
  return useMutation(caller, {
    mutationKey: [UPDATE_PROJECT_VISIBILITY_QUERY],
    ...options,
  });
};
export const useUpdateProjectStatus = (options = {}) => {
  const updateProject = useUpdateProjectStatusApi();
  return useMutation(updateProject, {
    mutationKey: [UPDATE_PROJECT_STATUS_QUERY],
    ...options,
  });
};
export const useUnlinkProblemStatement = (options = {}) => {
  const unlinkProblemStatement = useUnlinkProblemStatementApi();
  return useMutation(unlinkProblemStatement, {
    mutationKey: [UNLINK_PROBLEM_STATEMENT_QUERY],
    ...options,
  });
};
export const useDeleteProjectFiles = (options = {}) => {
  const deleteProjectFiles = useDeleteProjectFilesApi();
  return useMutation(deleteProjectFiles, {
    mutationKey: [DELETE_PROJECT_FILES_QUERY],
    ...options,
  });
};
