import {
  CrossBorderIcon,
  EducationIcon,
  FoodChainIcon,
  HealthcareIcon,
} from '../../../assets/icons'
import { problemCategory } from '../../../data/constants'
import { ExtractData, ServerSDGs } from '../../../types'

export default function useCreateListForProblemStatements(
  list: ExtractData<ServerSDGs>
) {
  if (list) {
    return list?.map(({ categoryName, subcategories, categoryRef }) => {
      return {
        category: categoryName,
        subMenuList: subcategories,
        icon: getProblemStatementCategoryIcon(categoryName),
        categoryRef: categoryRef,
      }
    })
  }
}

const getProblemStatementCategoryIcon = (categoryName: string) => {
  return problemCategory.education === categoryName
    ? EducationIcon
    : problemCategory.healthcare === categoryName
      ? HealthcareIcon
      : problemCategory.crossBorderTrade === categoryName
        ? CrossBorderIcon
        : problemCategory.foodSupplyChain === categoryName
          ? FoodChainIcon
          : null
}
