import { ReactNode, useRef, useState } from 'react';

import bgImage from '../../../assets/images/rectangleTwo.png';
import Filter from '../../../components/ui/Filter';
import SearchBar from '../../../components/ui/SearchBar';
import { useHandleQueryParams } from '../../../hooks/useHandleQueryParams';
import { useOnClickOutside } from '../../../hooks/useOnClickOutside';
import CourseFilterSidebar from '../components/ui/FilterSidebars/CourseFilterSidebar';
import ProviderFilterSidebar from '../components/ui/FilterSidebars/ProviderFilterSidebar';
import { FilterIcon } from '@/assets/icons';

type Props = {
  children: ReactNode;
  filteredCategoryRefList?: string[];
  handleCategoryRef: (value: string) => void;
};

export default function CourseCategoryLayout({
  children,
  filteredCategoryRefList,
  handleCategoryRef,
}: Props) {
  const { handleQuery } = useHandleQueryParams();
  const [isOpen, setIsOpen] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useOnClickOutside(ref, (e?: MouseEvent | TouchEvent) => {
    const target = e?.target as Element;
    const filterButton = document.querySelector('#filter-btn');

    if (
      (target?.id && target.id === 'filter-btn') ||
      filterButton?.contains(target)
    )
      return;
    setIsOpen(false);
  });

  return (
    <section className="pt-24">
      <div>
        <div className="flex flex-col gap-4 px-5 sm:flex-row sm:items-center sm:justify-between md:px-[60px]">
          <div className="relative flex grow gap-6">
            <div
              id="filter-btn"
              onClick={() => setIsOpen(previous => !previous)}
              className=" flex h-[37px] items-center gap-3 border border-primary bg-grayTwo px-6 sm:hidden"
            >
              <FilterIcon />
              <p className="hidden text-primary">Filter</p>
            </div>
            {isOpen && (
              <div
                ref={ref}
                className="absolute top-full z-[10] block w-[303px] pt-[30px] sm:hidden"
              >
                <div className="bg-grayTwo">
                  <CourseFilterSidebar
                    handleCategoryRef={handleCategoryRef}
                    filteredCategoryRefList={filteredCategoryRefList || []}
                  />
                  <ProviderFilterSidebar />
                </div>
              </div>
            )}
            <SearchBar setSearchValue={value => handleQuery({ q: value })} />
          </div>
          <Filter options={[]} />
        </div>
        <div className="mx-5 mb-10 hidden flex-wrap justify-between bg-grayTwo pb-4 pt-6 text-[14px] md:mx-[60px]">
          <div>
            Suggestion: &nbsp;
            <span className="cursor-pointer text-primary">
              mobile &nbsp; user &nbsp; experience &nbsp; mobile/web &nbsp;
              flutter &nbsp; kotlin
            </span>
          </div>
          <div>
            <p className="text-blueGray">
              <span className="text-black">3,145,684</span> results find for
              "mobile development"
            </p>
          </div>
        </div>
        <div className="pb-6"></div>
        <div
          style={{ backgroundImage: `url(${bgImage})` }}
          className="mx-auto
          flex max-w-full overflow-hidden bg-cover pb-40"
        >
          <div className="w-[256px] max-sm:hidden md:w-[352px]">
            <CourseFilterSidebar
              handleCategoryRef={handleCategoryRef}
              filteredCategoryRefList={filteredCategoryRefList || []}
            />
            <ProviderFilterSidebar />
          </div>
          <div className="container_max_width">{children}</div>
        </div>
      </div>
    </section>
  );
}
