import { ReactNode } from 'react'
import { useSearchParams } from 'react-router-dom'

import Filter from '../../../components/ui/Filter'
import SearchBar from '../../../components/ui/SearchBar'
import { CategoryByStatusType } from '../../../types'
import { categoryByStatusOptions } from '../data/constant'

type Props = {
  children: ReactNode
  onChange: (value: CategoryByStatusType) => void
  setSearchValue?: (value: any) => void
}

export default function DashboardProblemStatementCategoryLayout({
  children,
  onChange,
  setSearchValue,
}: Props) {
  const [search] = useSearchParams()
  const filter = search.get('q')
  return (
    <section className="min-h-screen">
      <div className="w-full">
        <h4 className="capitalize text-black font-[500] text-[20px] max-w-[788px] mb-8">
          {filter
            ?.split('_')
            .map((index) => index)
            .join(' ')}
          <span className=""> Problem statements</span>
        </h4>
      </div>
      <div className="mb-10 flex gap-4 flex-col sm:flex-row sm:justify-between sm:items-center">
        <SearchBar setSearchValue={setSearchValue} />
        <Filter onChange={onChange} options={categoryByStatusOptions} />
      </div>
      <div className={`pb-40`}>{children}</div>
    </section>
  )
}
