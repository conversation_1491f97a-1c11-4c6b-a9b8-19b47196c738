import { ReactNode } from 'react'
import { useNavigate } from 'react-router-dom'

import { ArrowRightTailless } from '../../../assets/icons'
import Filter from '../../../components/ui/Filter'
import SearchBar from '../../../components/ui/SearchBar'
import { useAppContext } from '../../../context/event/AppEventContext'
import { ProblemStatementCategoryNameType } from '../../../types'
import { problemCategoryOptions } from '../data/constant'

type Props = {
  children: ReactNode
  onChange: (value: ProblemStatementCategoryNameType) => void
  setSearchValue?: (value: any) => void
}

export default function ProjectCategoryLayout({
  children,
  onChange,
  setSearchValue,
}: Props) {
  const { currentAccountType } = useAppContext()
  const navigate = useNavigate()
  return (
    <section className="min-h-screen">
      <div className="flex gap-x-1 items-center mt-[34px] mb-[36px] flex-wrap">
        <p
          onClick={() =>
            navigate(`/${currentAccountType}/dashboard-projects?q=all`)
          }
          className={`${'text-primary'} cursor-pointer`}
        >
          Create a Project
        </p>

        <>
          <ArrowRightTailless className="w-[24px] h-[24px] fill-primary" />
          <p>Select a problem statement</p>
        </>
      </div>
      <div className="mb-10 flex gap-4 flex-col sm:flex-row sm:justify-between sm:items-center">
        <SearchBar setSearchValue={setSearchValue} />{' '}
        <Filter onChange={onChange} options={problemCategoryOptions} />
      </div>
      <div className={`pb-40`}>{children}</div>
    </section>
  )
}
