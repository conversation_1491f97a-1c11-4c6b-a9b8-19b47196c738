import { ReactNode, useRef, useState } from 'react';

import bgImage from '../../../assets/images/rectangleTwo.png';
import SearchBar from '../../../components/ui/SearchBar';
import { useHandleQueryParams } from '../../../hooks/useHandleQueryParams';
import { useOnClickOutside } from '../../../hooks/useOnClickOutside';
import CourseFilterSidebar from '../components/ui/FilterSidebars/CourseFilterSidebar';
import { ArrowBackIcon, FilterIcon } from '@/assets/icons';
import { Link } from 'react-router-dom';

type Props = {
  children: ReactNode;
  filteredCategoryRefList?: string[];
  handleCategoryRef: (value: string) => void;
};

export default function BooksCategoryLayout({
  children,
  filteredCategoryRefList,
  handleCategoryRef,
}: Props) {
  const { handleQuery } = useHandleQueryParams();
  const [isOpen, setIsOpen] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useOnClickOutside(ref, (e?: MouseEvent | TouchEvent) => {
    const target = e?.target as Element;
    const filterButton = document.querySelector('#filter-btn');

    if (
      (target?.id && target.id === 'filter-btn') ||
      filterButton?.contains(target)
    )
      return;
    setIsOpen(false);
  });

  return (
    <section className="pt-24">
      <div>
        <div className="flex flex-col gap-4 px-5 sm:flex-row sm:items-center sm:justify-between md:px-[60px]">
          <div className="relative flex grow justify-between gap-6">
            <Link
              to="/courses-learn"
              className="flex items-center gap-4 text-primary md:mr-10"
            >
              <ArrowBackIcon />
              <span className="mt-1">uBooks</span>
            </Link>{' '}
            <div className="flex justify-between gap-6">
              <div
                id="filter-btn"
                onClick={() => setIsOpen(previous => !previous)}
                className=" flex h-[37px] items-center gap-3 border border-primary bg-grayTwo px-6 sm:hidden"
              >
                <FilterIcon />
                <p className="hidden text-primary">Filter</p>
              </div>
              {isOpen && (
                <div
                  ref={ref}
                  className="absolute top-full z-[10] block w-[303px] pt-[30px] sm:hidden"
                >
                  <div className="bg-grayTwo">
                    <CourseFilterSidebar
                      handleCategoryRef={handleCategoryRef}
                      filteredCategoryRefList={filteredCategoryRefList || []}
                    />
                  </div>
                </div>
              )}
              <SearchBar setSearchValue={value => handleQuery({ q: value })} />
            </div>
          </div>
        </div>
        <div className="pb-6"></div>
        <div
          style={{ backgroundImage: `url(${bgImage})` }}
          className="mx-auto
          flex max-w-full overflow-hidden bg-cover pb-40"
        >
          <div className="w-[256px] max-sm:hidden md:w-[352px]">
            <CourseFilterSidebar
              handleCategoryRef={handleCategoryRef}
              filteredCategoryRefList={filteredCategoryRefList || []}
            />
          </div>
          <div className="container_max_width">{children}</div>
        </div>
      </div>
    </section>
  );
}
