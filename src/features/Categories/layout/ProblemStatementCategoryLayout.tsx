import { ReactNode } from 'react';
import { useSearchParams } from 'react-router-dom';

import bgImage from '../../../assets/images/rectangleTwo.png';
import Filter from '../../../components/ui/Filter';
import SearchBar from '../../../components/ui/SearchBar';
import { CategoryByStatusType } from '../../../types';
import ProblemStatementFilterSidebar from '../components/ui/FilterSidebars/ProblemStatementFilterSidebar';
import { categoryByStatusOptions } from '../data/constant';
import { useAppContext } from '@/context/event/AppEventContext';

type Props = {
  children: ReactNode;
  onChange: (value: CategoryByStatusType) => void;
  setSearchValue?: (value: any) => void;
};

export default function ProblemStatementCategoryLayout({
  children,
  onChange,
  setSearchValue,
}: Props) {
  const { filters } = useAppContext();
  const [search] = useSearchParams();
  const filter = search.get('q');
  return (
    <section className="pt-24">
      <div>
        <div className="mb-4 flex flex-col gap-4 px-5 sm:flex-row sm:items-center sm:justify-between lg:px-28">
          <SearchBar setSearchValue={setSearchValue} />
          <Filter onChange={onChange} options={categoryByStatusOptions} />
        </div>
        <h1 className="mb-4 px-4 text-[clamp(20px,5vw,24px)] font-semibold capitalize text-orange-5 max-sm:px-5 sm:ml-[352px]">
          {filters &&
            filters.length === 0 &&
            filter
              ?.split('_')
              .map(index => index)
              .join(' ')}
          <span className="text-blackFive"> problem statements</span>
        </h1>
        <div
          style={{ backgroundImage: `url(${bgImage})` }}
          className={`mx-auto
    flex max-w-full overflow-hidden bg-cover pb-40`}
        >
          <div className="w-[352px] max-sm:hidden">
            <ProblemStatementFilterSidebar />
          </div>
          <div className="container_max_width">{children}</div>
        </div>
      </div>
    </section>
  );
}
