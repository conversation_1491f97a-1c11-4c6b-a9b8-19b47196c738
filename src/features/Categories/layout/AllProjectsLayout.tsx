import { ReactNode } from 'react';

import { ArrowBackIcon } from '@/assets/icons';

import Header from '../components/ui/Project/Projects/Shared/Header';

import { useHandleQueryParams } from '../../../hooks/useHandleQueryParams';

type Props = {
  children: ReactNode;
};

export default function AllProjectsLayout({ children }: Props) {
  const { query, handleQuery } = useHandleQueryParams();
  return (
    <section className="min-h-screen">
      <div className="mb-8 flex w-full justify-between">
        <h4 className="flex max-w-[788px] gap-2 text-[20px] font-[500] capitalize text-black">
          <ArrowBackIcon
            className="cursor-pointer"
            onClick={() => handleQuery({ project: undefined, page: undefined })}
          />
          {query
            .get('project')
            ?.split('_')
            .map(index => index)
            .join(' ')}
        </h4>
        <Header />
      </div>
      <div className={`pb-40`}>{children}</div>
    </section>
  );
}
