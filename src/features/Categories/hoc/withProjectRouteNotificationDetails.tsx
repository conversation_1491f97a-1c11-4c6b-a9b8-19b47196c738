import { JSXElementConstructor } from 'react';
import { useParams } from 'react-router-dom';
import { useHandleQueryParams } from '../../../hooks/useHandleQueryParams';
import { JoinProjectRequestProps } from '@/types';

export default function withProjectRouteNotificationDetails(
  NotificationComponent: JSXElementConstructor<JoinProjectRequestProps>,
) {
  /* eslint-disable react/display-name */
  return function NotificationComponentWrapper() {
    const { query } = useHandleQueryParams();
    const message = query.get('msg') || '';
    const customMessage = query.get('message') || '';
    const teamRef = query.get('teamRef') || '';
    const { projectRef } = useParams();

    const notificationDetailsProps: JoinProjectRequestProps = {
      type: 'join-project-request',
      msg: message,
      customMessage,
      teamRef,
      projectRef: projectRef || '',
    };

    return <NotificationComponent {...notificationDetailsProps} />;
  };
}
