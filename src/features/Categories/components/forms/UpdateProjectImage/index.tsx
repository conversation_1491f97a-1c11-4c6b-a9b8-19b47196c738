import { FileUploadIcon } from '@/assets/icons';
import Form from '@/components/forms/Form';
import Button from '@/components/ui/ButtonComponent';
import { Spinner } from '@/components/ui/CommonWidget/Loader';
import { useAppContext } from '@/context/event/AppEventContext';
import { useUpdateProject } from '@/features/Categories/hooks/apiQueryHooks/eduQueryHooks';
import { iUpdateProjectImageSchema } from '@/features/Categories/lib/yup/validations';
import { IUpdateProjectImagePayload } from '@/features/Categories/types';
import { PictureUploadDropzone } from '@/features/Profile/components/forms/UpdateProfile/PictureUploadDropzone';
import { useFormDataAndApiMutateHandler } from '@/hooks/useFormDataAndApiMutateHandler';
import { GET_PROJECT_QUERY } from '@/utils/queryKeys';
import { SubmitHandler } from 'react-hook-form';
import { useParams } from 'react-router-dom';

export const UpdateProjectImageForm = () => {
  const { setShowModalHandler } = useAppContext();
  const { projectRef } = useParams();

  const next = () => {
    setShowModalHandler('');
  };

  const {
    control,
    handleSubmit,
    isLoading,
    watch,
    formState: { errors, isDirty, isValid },
    mutate,
  } = useFormDataAndApiMutateHandler<IUpdateProjectImagePayload, any>(
    iUpdateProjectImageSchema,
    useUpdateProject,
    {
      next,
      queryKey: GET_PROJECT_QUERY,
    },
  );

  const onSubmit: SubmitHandler<IUpdateProjectImagePayload> = data => {
    const imageList = data?.projectImage as FileList;
    const image = imageList?.[0] as File;
    const newData = { projectImage: image, projectRef: projectRef };
    mutate(newData);
  };

  return (
    <Form
      onSubmit={handleSubmit(onSubmit)}
      className="flex w-full flex-col items-center"
    >
      <div className="w-full">
        <PictureUploadDropzone
          Icon={<FileUploadIcon className="mb-5" />}
          label={
            <div className="text-center text-black">
              <p className="mb-1.5 text-[18px] font-[500]">Upload a file</p>
              <p className="text-[12px] font-normal">
                Drag and drop or browse to choose a file
              </p>
            </div>
          }
          infoText={
            <p className="w-full max-w-[269px] text-[14px] font-[400] text-primary">
              File accepted - jpeg, png file size - max 5MB
            </p>
          }
          name="projectImage"
          control={control}
          errors={errors}
          dropZoneOptions={{
            accept: {
              'image/png': ['.png', '.jpeg', '.jpg'],
            },
          }}
        />
      </div>
      {watch('projectImage') && (
        <div className="mt-5 flex w-full justify-center">
          <Button
            disabled={!isDirty || !isValid || isLoading}
            type="submit"
            className="w-full max-w-[120px] flex-1 whitespace-nowrap !rounded-none border-[0.73px] border-primary bg-primary hover:border-black hover:bg-black disabled:cursor-not-allowed  disabled:border-disabled disabled:bg-disabled disabled:text-opacity-70"
          >
            {isLoading ? (
              <Spinner className="border-[3px]  border-white border-b-[transparent]" />
            ) : (
              <p className="text-[16px] leading-[24px] text-white group-hover:text-primary group-disabled:text-white">
                Submit
              </p>
            )}
          </Button>
        </div>
      )}
    </Form>
  );
};
