import 'react-phone-input-2/lib/bootstrap.css';

import { SubmitHandler } from 'react-hook-form';
import { useNavigate, useParams } from 'react-router-dom';

import { ArrowBackIcon, SendIcon } from '../../../../../../assets/icons';
import Form from '../../../../../../components/forms/Form';
import FormInputBox from '../../../../../../components/forms/FormInputBox';
import FormSelectBox from '../../../../../../components/forms/FormSelectBox';
import { Spinner } from '../../../../../../components/ui/CommonWidget/Loader';
import SharedFormButton from '../../../../../../components/ui/SharedFormButton';
import { useAppContext } from '../../../../../../context/event/AppEventContext';
import { projectLevelUserRole } from '../../../../../../data/constants';
import { useFormDataAndApiMutateHandler } from '../../../../../../hooks/useFormDataAndApiMutateHandler';
import { GET_PROJECT_MEMBERS_QUERY } from '../../../../../../utils/queryKeys';
import {
  useGetProject,
  useInviteToJoinProject,
} from '../../../../hooks/apiQueryHooks/eduQueryHooks';
import { iInviteToJoinProjectSchema as IInviteToJoinProjectSchema } from '../../../../lib/yup/validations';
import { IInviteToJoinProjectPayload } from '../../../../types';
const projectLevelUserRoleOptions = [
  {
    value: projectLevelUserRole.projectAdmin,
    label: 'Project Admin',
  },
  {
    value: projectLevelUserRole.projectMember,
    label: 'Project Member',
  },
];
export function SharedAddProjectMemberForm() {
  const { currentAccountType } = useAppContext();
  const navigate = useNavigate();
  const { projectRef } = useParams();
  const { data: project, isLoading: isLoadingProject } = useGetProject(
    projectRef || '',
  );
  const next = () => {
    (['firstName', 'lastName', 'email', 'projectRole'] as const).forEach(
      entry => resetField(entry),
    );
  };
  const {
    resetField,
    handleSubmit,
    register,
    control,
    isLoading,
    formState: { errors, isDirty, isValid },
    mutate,
  } = useFormDataAndApiMutateHandler<IInviteToJoinProjectPayload, any>(
    IInviteToJoinProjectSchema,
    useInviteToJoinProject,
    {
      next,
      queryKey: GET_PROJECT_MEMBERS_QUERY,
    },
  );
  const onSubmit: SubmitHandler<IInviteToJoinProjectPayload> = data => {
    const newData = {
      ...data,
      projectRef,
    };
    mutate(newData);
  };
  if (isLoadingProject)
    return (
      <div className="flex h-[400px] w-full items-center justify-center">
        <Spinner className="" />
      </div>
    );
  return (
    <>
      <div className="flex h-full w-full gap-x-2 sm:gap-x-8">
        <ArrowBackIcon
          className="cursor-pointer"
          onClick={() =>
            navigate(`/${currentAccountType}/dashboard-projects/${projectRef}`)
          }
        />
        <div className="w-full">
          <h4 className="max-w-[788px] text-[20px] font-[500] text-black">
            Add a Project member to
            <span className="text-primary"> {project?.data.projectName}</span>
          </h4>
          <div className="pt-4">
            <Form
              onSubmit={handleSubmit(onSubmit)}
              className="w-full max-w-[400px]"
            >
              <div className="">
                <FormInputBox<IInviteToJoinProjectPayload>
                  className="input mt-2"
                  name="firstName"
                  errors={errors}
                  labelName="First Name"
                  placeholder="First Name"
                  type="text"
                  autoComplete="first-name"
                  registerHanlder={() => register('firstName')}
                />
              </div>
              <div className="mt-5">
                <FormInputBox<IInviteToJoinProjectPayload>
                  className="input mt-2"
                  name="lastName"
                  errors={errors}
                  labelName="Last Name"
                  placeholder="Last Name"
                  type="text"
                  autoComplete="last-name"
                  registerHanlder={() => register('lastName')}
                />
              </div>
              <div className="mt-5">
                <FormInputBox<IInviteToJoinProjectPayload>
                  className="input mt-2"
                  name="email"
                  errors={errors}
                  labelName="Email Address"
                  placeholder="Email Address"
                  type="text"
                  autoComplete="email"
                  registerHanlder={() => register('email')}
                />
              </div>
              <div className="mt-5">
                <FormSelectBox<IInviteToJoinProjectPayload>
                  options={projectLevelUserRoleOptions}
                  optionsArr={projectLevelUserRoleOptions}
                  control={control}
                  name="projectRole"
                  errors={errors}
                  labelName="Role"
                  placeholder="Role"
                  isClearable={false}
                />
              </div>
              <SharedFormButton
                isLoading={isLoading}
                isDirty={isDirty}
                isValid={isValid}
                className=""
              >
                <div className="flex items-center gap-x-[10px]">
                  <SendIcon className="stroke-white group-disabled:stroke-primary" />
                  <p className="text-[14px] sm:text-[16px]">Send Invite</p>
                </div>
              </SharedFormButton>
            </Form>
          </div>
        </div>
      </div>
    </>
  );
}
