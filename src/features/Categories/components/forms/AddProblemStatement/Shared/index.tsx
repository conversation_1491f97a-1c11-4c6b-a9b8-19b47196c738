import 'react-phone-input-2/lib/bootstrap.css';

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, useWatch } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';

import { ArrowBackIcon } from '../../../../../../assets/icons';
import Form from '../../../../../../components/forms/Form';
import FormFileInputBox from '../../../../../../components/forms/FormFileInputBox';
import FormInputBox from '../../../../../../components/forms/FormInputBox';
import FormSelectBox from '../../../../../../components/forms/FormSelectBox';
import FormTextAreaBox from '../../../../../../components/forms/FormTextAreaBox';
import CoverElement from '../../../../../../components/ui/CommonWidget/CoverElement';
import { Spinner } from '../../../../../../components/ui/CommonWidget/Loader';
import SharedFormButton from '../../../../../../components/ui/SharedFormButton';
import { useAppContext } from '../../../../../../context/event/AppEventContext';
import {
  useGetSDG,
  useGetSDGs,
} from '../../../../../../hooks/apiQueryHooks/eduQueryHooks';
import { useCreateStatementCategoriesOptions } from '../../../../../../hooks/useCreateStatementCategoriesOptions';
import { useCreateStatementSubCategoriesOptions } from '../../../../../../hooks/useCreateStatementSubCategoriesOptions';
import { useFormDataAndApiMutateHandler } from '../../../../../../hooks/useFormDataAndApiMutateHandler';
import { GET_PROBLEM_STATEMENTS_QUERY } from '../../../../../../utils/queryKeys';
import { useAddProblemStatement } from '../../../../hooks/apiQueryHooks/eduQueryHooks';
import { IAddProblemStatementSchema } from '../../../../lib/yup/validations';
import { IAddProblemStatementPayload } from '../../../../types';
import { useGetCountries } from '@/hooks/apiQueryHooks/userQueryHooks';
import { Helper } from '@/utils/helpers';

export function SharedAddProblemStatementForm() {
  const navigate = useNavigate();
  const { currentAccountType } = useAppContext();

  const next = () => {
    (
      [
        'title',
        'categories',
        'subcategories',
        'countries',
        'description',
        'descriptionVideoUrl',
        'descriptionFile',
      ] as const
    ).forEach(entry => resetField(entry));
    navigate(`/${currentAccountType}/dashboard-statements`);
  };
  const {
    resetField,
    handleSubmit,
    control,
    register,
    getValues,
    isLoading,
    formState: { errors, isDirty, isValid },
    mutate,
  } = useFormDataAndApiMutateHandler<IAddProblemStatementPayload, any>(
    IAddProblemStatementSchema,
    useAddProblemStatement,
    {
      next,
      queryKey: GET_PROBLEM_STATEMENTS_QUERY,
    },
  );
  const onSubmit: SubmitHandler<IAddProblemStatementPayload> = data => {
    const newData = {
      ...data,
    };
    mutate(newData);
  };
  const { data: categories, isLoading: isLoadingCategories } = useGetSDGs({
    staleTime: 1000 * 60 * 60 * 24,
    cacheTime: 1000 * 60 * 60 * 25,
  });
  const { data: category, isFetching: isFetchingCategory } = useGetSDG(
    getValues('categories')?.[0] || '',
    {
      staleTime: 1000 * 60 * 60 * 24,
      cacheTime: 1000 * 60 * 60 * 25,
      enabled: !!getValues('categories')?.[0],
    },
  );
  const { data: countries, isLoading: isLoadingCountries } = useGetCountries();

  useWatch({
    control,
    defaultValue: {
      categories: [],
    },
  });
  return (
    <>
      <div className="flex h-full w-full gap-x-2 sm:gap-x-8">
        <ArrowBackIcon
          className="cursor-pointer"
          onClick={() =>
            navigate(`/${currentAccountType}/dashboard-statements`)
          }
        />
        <div className="w-full">
          <h4 className="max-w-[788px] text-[20px] font-[500] text-black">
            Create Problem Statement
          </h4>
          <div className="pt-4">
            <Form
              onSubmit={handleSubmit(onSubmit)}
              className="w-full max-w-[400px]"
            >
              <div className="">
                <FormInputBox<IAddProblemStatementPayload>
                  className={'input mt-2 max-w-full'}
                  name="title"
                  errors={errors}
                  labelName="Title"
                  placeholder="Title"
                  type="text"
                  autoComplete="title"
                  registerHanlder={() => register('title')}
                />
              </div>
              <div className="mt-5">
                <FormSelectBox<IAddProblemStatementPayload>
                  options={
                    useCreateStatementCategoriesOptions(
                      categories?.data || [],
                    ) || []
                  }
                  optionsArr={
                    useCreateStatementCategoriesOptions(
                      categories?.data || [],
                    ) || []
                  }
                  control={control}
                  name="categories"
                  errors={errors}
                  labelName="Select Goals Categories (Up to 2)"
                  placeholder="Select Category"
                  isClearable={false}
                  isMulti
                />
                {isLoadingCategories && (
                  <div className="mt-2">
                    <Spinner className="" />
                  </div>
                )}
              </div>
              <div className="relative mt-5">
                <FormSelectBox<IAddProblemStatementPayload>
                  options={
                    useCreateStatementSubCategoriesOptions(
                      category?.data.subcategories || [],
                    ) || []
                  }
                  optionsArr={
                    useCreateStatementSubCategoriesOptions(
                      category?.data.subcategories || [],
                    ) || []
                  }
                  control={control}
                  name="subcategories"
                  errors={errors}
                  labelName="Goals SubCategories"
                  placeholder="Select Subcategory"
                  isClearable={false}
                  isMulti
                />
                {isFetchingCategory && (
                  <div className="mt-2">
                    <Spinner className="" />
                  </div>
                )}
                {!getValues('categories')?.[0] && <CoverElement />}
              </div>
              <div className="relative mt-5">
                <FormSelectBox
                  labelName="Select Focus Countries"
                  options={Helper.createCountriesOptionsArray(
                    countries?.data || { countries: [] },
                  )}
                  optionsArr={Helper.createCountriesOptionsArray(
                    countries?.data || { countries: [] },
                  )}
                  control={control}
                  name="countries"
                  errors={errors}
                  placeholder="Select"
                  isMulti
                />
                {isLoadingCountries && (
                  <div className="mt-2">
                    <Spinner className="" />
                  </div>
                )}
              </div>
              <div className="mt-5">
                <FormTextAreaBox<IAddProblemStatementPayload>
                  labelName="Add Description"
                  className={`input mt-2 min-h-[100px] max-w-full`}
                  name="description"
                  errors={errors}
                  registerHanlder={() => register('description')}
                  rows={4}
                  placeholder="Description"
                />
              </div>
              <div className="mt-5">
                <FormInputBox<IAddProblemStatementPayload>
                  className={'input mt-2 max-w-full'}
                  name="descriptionVideoUrl"
                  errors={errors}
                  labelName="Description Video Link (Youtube)"
                  placeholder="Description Video Link (Youtube)"
                  type="text"
                  autoComplete="description-link"
                  registerHanlder={() => register('descriptionVideoUrl')}
                />
              </div>
              <div className="mt-5">
                <FormFileInputBox<IAddProblemStatementPayload>
                  control={control}
                  name="descriptionFile"
                  errors={errors}
                  autoComplete="description-file"
                  labelName="Upload Detailed Description (Docx, Pdf)"
                />
              </div>
              <SharedFormButton
                isLoading={isLoading}
                isDirty={isDirty}
                isValid={isValid}
                className=""
              >
                <p className="text-[14px] sm:text-[16px]">Submit</p>
              </SharedFormButton>
            </Form>
          </div>
        </div>
      </div>
    </>
  );
}
