import { useDropzone } from 'react-dropzone';
import { useCallback, useEffect, useState } from 'react';
import { UploadIcon } from '@/assets/icons';

export default function AddProjectDocumentDropZone({
  onFileUpload,
}: {
  onFileUpload: (value: File[]) => void;
}) {
  const [error, setError] = useState('');
  const [invalidFiles, setInvalidFiles] = useState<File[]>([]);
  useEffect(() => {
    if (error) {
      setTimeout(() => setError(''), 5000);
    }
  }, [error]);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const allowedFormats = new Set([
      'jpg',
      'jpeg',
      'pdf',
      'png',
      'gif',
      'doc',
      'docx',
    ]);
    const maxFileSize = 25 * 1024 * 1024;
    const invalidFiles = acceptedFiles.filter(file => {
      const fileExtension = file.name.split('.').pop()?.toLowerCase();
      return !allowedFormats.has(fileExtension!) || file.size > maxFileSize;
    });

    if (invalidFiles.length > 0) {
      setInvalidFiles(invalidFiles);
      setError('Invalid file included or file too large');
    } else {
      onFileUpload(acceptedFiles);
    }
  }, []);

  const { isDragActive, getRootProps, getInputProps } = useDropzone({
    onDrop,
  });

  return (
    <>
      <label
        htmlFor="image"
        {...getRootProps()}
        className="m-8 mx-auto flex h-full w-[90%] 
        cursor-pointer flex-col items-center justify-center rounded-xl bg-white px-20 py-12 max-md:px-5"
      >
        <div className="flex flex-col items-center justify-center rounded-xl border-[1.2px] border-dashed border-primary bg-lightOrangeTwo p-4">
          <UploadIcon />
          <header className="mt-5 whitespace-nowrap text-base font-semibold capitalize leading-6 text-black">
            {isDragActive ? (
              <p>Drop the files here ...</p>
            ) : (
              <p>Drag and drop or browse to choose a file</p>
            )}
          </header>
          <div className="mt-3 whitespace-nowrap text-[13px] leading-5 text-primary">
            File accepted - jpeg , jpg, png , gif, doc, docx or pdf,
          </div>
          <div className="mb-3 mt-1 whitespace-nowrap text-[13px] leading-5 text-primary">
            File size - 25MB
          </div>
          {invalidFiles.length > 0 && (
            <small className="text-red-800">{error}</small>
          )}
          <input {...getInputProps()} />
        </div>
      </label>
    </>
  );
}
