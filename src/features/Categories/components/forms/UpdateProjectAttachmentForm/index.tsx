import { useCallback, useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { SubmitHandler, UseFormSetValue } from 'react-hook-form';

import Form from '@/components/forms/Form';
import Button from '@/components/ui/ButtonComponent';
import { Spinner } from '@/components/ui/CommonWidget/Loader';
import SharedFormButton from '@/components/ui/SharedFormButton';

import { useAppContext } from '@/context/event/AppEventContext';
import {
  useGetProject,
  useUpdateProject,
} from '@/features/Categories/hooks/apiQueryHooks/eduQueryHooks';
import { useHandleQueryParams } from '@/hooks/useHandleQueryParams';
import { useFormDataAndApiMutateHandler } from '@/hooks/useFormDataAndApiMutateHandler';

import { Helper } from '@/utils/helpers';
import { GET_PROJECT_QUERY } from '@/utils/queryKeys';
import {
  IUpdateProjectAttachmentPayload,
  IUpdateProjectPayload,
} from '@/features/Categories/types';
import { GoalsHelper } from '@/features/projectManagementGoals/utils/helper';
import { iUpdateProjectAttachmentSchema } from '@/features/Categories/lib/yup/validations';
import { AttachmentIcon, DeleteIcon2 } from '@/assets/icons';
import FormInputBoxWithForwardRef from '@/components/forms/FormInputBox';
import ToolTip from '@/components/ui/CommonWidget/ToolTip';
import { DocIcon, PdfIcon } from '@/features/Chat/assets/icons';
import { noImagePlaceholder } from '@/assets/images';
import { cn } from '@/lib/twMerge/cn';
import FormLabel from '@/components/forms/FormLabel';
import AddProjectDocumentModal from '../../ui/Modals/AddProjectDocumentModal';

export const UpdateProjectAttachmentForm = () => {
  const [isDocumentModalOpen, setIsDocumentModalOpen] = useState(false);

  const { projectRef } = useParams();
  const { query } = useHandleQueryParams();
  const { setShowModalHandler } = useAppContext();

  const projectId = projectRef || query.get('project_ref') || '';

  const { data, isLoading: isLoadingProject } = useGetProject(projectId);

  const project = data?.data;

  const next = () => {
    setShowModalHandler('');
  };

  const {
    watch,
    setValue,
    register,
    isLoading,
    handleSubmit,
    formState: { errors, isDirty, isValid },
    mutate,
  } = useFormDataAndApiMutateHandler<IUpdateProjectAttachmentPayload, any>(
    iUpdateProjectAttachmentSchema,
    useUpdateProject,
    {
      next,
      queryKey: GET_PROJECT_QUERY,
    },
  );

  useEffect(() => {
    if (!project) return;
    const setFormValue =
      setValue as UseFormSetValue<IUpdateProjectAttachmentPayload>;

    setFormValue(
      'descriptionVideoUrl',
      project?.descriptionVideoUrls?.[project?.descriptionVideoUrls?.length - 1]
        ?.url,
    );
  }, [project]);

  const onSubmit: SubmitHandler<IUpdateProjectAttachmentPayload> = data => {
    const newData = {
      ...data,
      projectRef: projectId,
    };
    mutate(newData);
  };

  const handleModalToggle = useCallback((value: boolean) => {
    setIsDocumentModalOpen(value);
  }, []);

  const handleFile = (newFiles: File[]) => {
    const oldFiles = watch('documents') || [];

    const mergedFiles = [...oldFiles, ...newFiles];
    setValue('documents', mergedFiles, {
      shouldDirty: true,
      shouldValidate: true,
    });
    handleModalToggle(false);
  };

  const removeFile = (index: number) => {
    const files = watch('documents') || [];
    files.splice(index, 1);
    setValue('documents', files, { shouldDirty: true, shouldValidate: true });
  };

  if (isLoadingProject)
    return (
      <div className="flex h-[300px] w-full items-center justify-center">
        <Spinner />
      </div>
    );

  return (
    <div className="w-full px-5">
      <h4 className="max-w-[560px] text-[20px] font-[500] text-black">
        Add Files
      </h4>
      <Form
        onSubmit={handleSubmit(onSubmit)}
        className="w-full max-w-[609px] pt-4"
      >
        <div className="mt-5">
          <FormLabel name="comment" labelName="Attach Documents (Optional)" />
          <Button
            className={cn('input group flex gap-2', 'justify-start')}
            type="button"
            onClick={() => handleModalToggle(true)}
          >
            <AttachmentIcon className="stroke-black" />
            <p>Upload Documents</p>
          </Button>
        </div>
        <div className="mt-2 flex flex-wrap gap-2">
          {(watch('documents') || [])?.map((attachment: File, i: number) => (
            <div
              key={i}
              className="w-full max-w-24 border-[2px] border-grayNineTeen"
            >
              <div className="group/first relative rounded-[4px] bg-white after:absolute after:inset-0 after:z-[0] after:duration-500 after:ease-in-out after:content-[''] hover:after:bg-[#00000080] max-md:w-full">
                {GoalsHelper.isImage(attachment.name) && (
                  <img
                    loading="lazy"
                    src={URL.createObjectURL(attachment) || noImagePlaceholder}
                    className=" h-[50px] w-full max-w-[85px]  self-start overflow-hidden object-cover object-center"
                  />
                )}
                {GoalsHelper.isPdf(attachment.name) && (
                  <div className=" flex h-[50px] w-full max-w-[85px] items-center justify-center">
                    <PdfIcon className="h-5 w-5" />
                  </div>
                )}
                {GoalsHelper.isDoc(attachment.name) && (
                  <div className=" flex h-[50px] w-full max-w-[85px] items-center justify-center">
                    <DocIcon className="h-5 w-5" />
                  </div>
                )}

                <div className="absolute right-1 top-1 z-[1] flex items-center gap-1 opacity-0 duration-500 ease-in-out group-hover/first:opacity-100">
                  <span
                    onClick={() => removeFile(i)}
                    className="group relative h-5 w-5 cursor-pointer bg-white p-[2px]"
                  >
                    <DeleteIcon2 className="h-4 w-4" />
                    <div className="absolute -left-[10px] -top-[30px] rounded-lg max-xs:hidden">
                      <ToolTip text="Delete file" />
                    </div>
                  </span>
                </div>
              </div>
              <p className="py-[2px] text-center text-[10px]">
                {Helper.truncateAndKeepExtension(attachment?.name, 10)}
              </p>
            </div>
          ))}
        </div>
        <div className="mt-5">
          <FormInputBoxWithForwardRef<IUpdateProjectPayload>
            formLabel={
              <FormLabel
                name="descriptionVideoUrl"
                labelName="Description Video Link - Youtube (Optional)"
              />
            }
            className="input"
            name="descriptionVideoUrl"
            errors={errors}
            placeholder="Video Link"
            autoComplete="descriptionVideoUrl"
            registerHanlder={() => register('descriptionVideoUrl')}
          />
        </div>
        <div className="flex w-full max-w-[400px] items-center gap-x-4">
          <Button
            onClick={() => {
              setShowModalHandler('');
            }}
            type="button"
            className="group mt-[32px] w-full min-w-[154px] whitespace-nowrap border-[1px] border-grayTen bg-transparent  hover:border-primary hover:bg-primary hover:text-white"
          >
            <p className="text-[16px] leading-8 text-grayTen group-hover:text-white">
              Cancel
            </p>
          </Button>
          <SharedFormButton
            isLoading={isLoading}
            isDirty={isDirty}
            isValid={isValid}
            className=""
          >
            <p className="text-[14px] sm:text-[16px]">Submit</p>
          </SharedFormButton>
        </div>
      </Form>
      <AddProjectDocumentModal
        isOpen={isDocumentModalOpen}
        onClose={() => handleModalToggle(false)}
        onFileUpload={handleFile}
      />
    </div>
  );
};
