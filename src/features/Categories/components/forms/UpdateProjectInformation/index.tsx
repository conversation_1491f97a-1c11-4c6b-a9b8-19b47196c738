import { useCallback, useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import { Controller, SubmitHandler, UseFormSetValue } from 'react-hook-form';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

import Form from '@/components/forms/Form';
import Button from '@/components/ui/ButtonComponent';
import FormSelectBox from '@/components/forms/FormSelectBox';
import { Spinner } from '@/components/ui/CommonWidget/Loader';
import SharedFormButton from '@/components/ui/SharedFormButton';
import FormDatePickerBox from '@/components/forms/FormDatePickerBox';

import { useAppContext } from '@/context/event/AppEventContext';
import {
  useGetProject,
  useUpdateProject,
} from '@/features/Categories/hooks/apiQueryHooks/eduQueryHooks';
import { useGetSDGs } from '@/hooks/apiQueryHooks/eduQueryHooks';
import { useHandleQueryParams } from '@/hooks/useHandleQueryParams';
import { useGetCountries } from '@/hooks/apiQueryHooks/userQueryHooks';
import { useFormDataAndApiMutateHandler } from '@/hooks/useFormDataAndApiMutateHandler';

import { Helper } from '@/utils/helpers';
import { GET_PROJECT_QUERY } from '@/utils/queryKeys';
import { projectCosts, projectLevels } from '@/features/Categories/data/data';
import { IUpdateProjectPayload } from '@/features/Categories/types';
import { GoalsHelper } from '@/features/projectManagementGoals/utils/helper';
import { iUpdateProjectSchema } from '@/features/Categories/lib/yup/validations';
import { useOnClickOutside } from '@/hooks/useOnClickOutside';
import { projectProgressStatus } from '@/data/constants';
import { ArrowDownIcon, AttachmentIcon, DeleteIcon2 } from '@/assets/icons';
import { ProjectProgressOptions } from '@/features/projectManagementGoals';
import FormInputBoxWithForwardRef from '@/components/forms/FormInputBox';
import ToolTip from '@/components/ui/CommonWidget/ToolTip';
import { DocIcon, PdfIcon } from '@/features/Chat/assets/icons';
import { noImagePlaceholder } from '@/assets/images';
import { cn } from '@/lib/twMerge/cn';
import FormLabel from '@/components/forms/FormLabel';
import FormFileInputBox from '@/components/forms/FormFileInputBox';
import { uniqueId } from 'lodash';
import AddProjectDocumentModal from '../../ui/Modals/AddProjectDocumentModal';
import { ConversationHelper } from '@/features/Conversation/utils/helper';

export const UpdateProjectInformationForm = () => {
  const [currentDropdown, setCurrentDropdown] = useState('');
  const isActive = () => currentDropdown === 'Open';
  const progressOptionsRef = useRef<HTMLDivElement>(null);
  useOnClickOutside(progressOptionsRef, () => setCurrentDropdown(''));
  const [key, setKey] = useState('');
  const [isDocumentModalOpen, setIsDocumentModalOpen] = useState(false);
  const [projectDescription, setProjectDescription] = useState('');

  const { projectRef } = useParams();
  const { query } = useHandleQueryParams();
  const { setShowModalHandler, isStudentOrFaculty } = useAppContext();

  const projectId = projectRef || query.get('project_ref') || '';

  const { data, isLoading: isLoadingProject } = useGetProject(projectId);

  const project = data?.data;

  const { data: sdgs, isLoading: isLoadingSdgs } = useGetSDGs({
    staleTime: 1000 * 60 * 60 * 24,
    cacheTime: 1000 * 60 * 60 * 25,
  });

  const { data: countries, isLoading: isLoadingCountries } = useGetCountries({
    staleTime: 1000 * 60 * 60 * 24,
    cacheTime: 1000 * 60 * 60 * 25,
  });

  const sdgOptions = GoalsHelper.createCategoryOptionsArr(sdgs?.data || []);
  const countryOptions = Helper.createCountriesOptionsArray(
    countries?.data || { countries: [] },
  );
  const categories = sdgOptions.filter(item =>
    project?.categoryRefs.includes(item.value),
  );
  const projectCountries = countryOptions.filter(item =>
    project?.projectCountries?.includes(item.value),
  );
  const projectLevel = projectLevels.find(
    item => project?.projectLevel === item.value,
  );
  const projectCost = projectCosts.find(
    item => project?.presumedProjectCost === item.value,
  );

  const next = () => {
    setShowModalHandler('');
    setKey(uniqueId());
  };

  const {
    watch,
    control,
    setValue,
    register,
    isLoading,
    handleSubmit,
    trigger,
    formState: { errors, isDirty, isValid },
    mutate,
  } = useFormDataAndApiMutateHandler<IUpdateProjectPayload, any>(
    iUpdateProjectSchema(isStudentOrFaculty),
    useUpdateProject,
    {
      next,
      queryKey: GET_PROJECT_QUERY,
    },
  );

  useEffect(() => {
    if (!project) return;
    const setFormValue = setValue as UseFormSetValue<IUpdateProjectPayload>;

    setFormValue('projectName', project?.projectName);
    setFormValue('projectDescription', project?.projectDescription);
    setProjectDescription(project?.projectDescription || '');
    setFormValue(
      'descriptionVideoUrl',
      project?.descriptionVideoUrls?.[project?.descriptionVideoUrls?.length - 1]
        ?.url,
    );
    setFormValue('projectLevel', project?.projectLevel);
    setFormValue('sdgCategoryRefs', project?.categoryRefs);
    setFormValue('projectCountries', project?.projectCountries);
    setFormValue('startDate', project?.startDate);
    setFormValue('endDate', project?.endDate);
    setFormValue('presumedProjectCost', project?.presumedProjectCost);

    // Trigger validation after setting all values
    trigger();
  }, [project]);

  const onSubmit: SubmitHandler<IUpdateProjectPayload> = data => {
    const newData = {
      ...data,
      projectDescription: projectDescription,
      projectRef: projectId,
    };
    mutate(newData);
  };

  const handleModalToggle = useCallback((value: boolean) => {
    setIsDocumentModalOpen(value);
  }, []);

  const handleFile = (newFiles: File[]) => {
    const oldFiles = watch('documents') || [];

    const mergedFiles = [...oldFiles, ...newFiles];
    setValue('documents', mergedFiles, {
      shouldDirty: true,
      shouldValidate: true,
    });
    handleModalToggle(false);
  };

  const removeFile = (index: number) => {
    const files = watch('documents') || [];
    files.splice(index, 1);
    setValue('documents', files, { shouldDirty: true, shouldValidate: true });
  };

  const removeExistingAsset = (
    fileRef: string,
    type: 'documents' | 'descriptionVideoUrls',
  ) => {
    const currentRemovedAssets = watch('removedAssets') || [];
    const newRemovedAssets = [...currentRemovedAssets, { fileRef, type }];
    setValue('removedAssets', newRemovedAssets, {
      shouldDirty: true,
      shouldValidate: true,
    });
  };

  if (isLoadingProject)
    return (
      <div className="flex h-[300px] w-full items-center justify-center">
        <Spinner />
      </div>
    );

  return (
    <div className="w-full px-5">
      <h4 className="max-w-[560px] text-[20px] font-[500] text-black">
        Edit Project
      </h4>
      <Form
        onSubmit={handleSubmit(onSubmit)}
        className="w-full max-w-[609px] pt-4"
      >
        <div>
          <FormInputBoxWithForwardRef<IUpdateProjectPayload>
            className="input mt-2 max-w-full"
            name="projectName"
            errors={errors}
            labelName="Project Name"
            placeholder="Project Name"
            type="text"
            autoComplete="project-name"
            registerHanlder={() => register('projectName')}
          />
        </div>
        <div className="mt-5">
          <p className='after:text-[16px] after:text-primary after:content-["*"]'>
            Project Status
          </p>
          <Controller
            name="projectStatus"
            control={control}
            render={({ field: { onChange, value } }) => (
              <div className="relative mt-2 h-full">
                <div className="flex h-full items-center">
                  <div
                    onClick={() => setCurrentDropdown('Open')}
                    className={`h-[37px] p-2 ${Helper.getProjectStatusAppearance(
                      Helper.capitalizeString(
                        value || projectProgressStatus.To_Do,
                      ),
                    )} flex cursor-pointer items-center gap-x-2 rounded-[4px]`}
                  >
                    <p className="text-[14px] font-semibold uppercase">
                      {Helper.capitalizeString(
                        value || projectProgressStatus.To_Do,
                      ) === 'To Do'
                        ? 'To-do'
                        : Helper.capitalizeString(
                            value || projectProgressStatus.To_Do,
                          )}
                    </p>
                    <ArrowDownIcon className="h-2 w-2 stroke-black" />
                  </div>
                </div>
                {isActive() && (
                  <div
                    ref={progressOptionsRef}
                    className="absolute left-0 top-[calc(46px-30%)] z-[12] min-w-[200px] bg-white shadow-lg"
                  >
                    <ProjectProgressOptions
                      selectedProgress={value}
                      setSelectedStatusProgress={onChange}
                    />
                  </div>
                )}
              </div>
            )}
          />
        </div>
        <div className="mt-5">
          <FormLabel
            name="projectDescription"
            labelName="Project Description"
          />
          <div className="mt-2 [&_.ql-tooltip]:!left-0 [&_.ql-tooltip]:!top-1">
            <ReactQuill
              theme="snow"
              value={projectDescription}
              onChange={setProjectDescription}
              placeholder="Project Description"
              className="min-h-[100px]"
            />
          </div>
          {errors.projectDescription && (
            <p className="mt-1 text-sm text-red-500">
              {errors.projectDescription.message?.toString()}
            </p>
          )}
        </div>
        <div className="mt-5">
          <FormSelectBox<IUpdateProjectPayload>
            key={projectCountries?.length}
            labelName="Project Country"
            options={countryOptions}
            optionsArr={countryOptions}
            control={control}
            name="projectCountries"
            errors={errors}
            placeholder="Select Country"
            isLoading={isLoadingCountries}
            defaultValue={projectCountries}
            isMulti
          />
        </div>
        <div className="mt-5">
          <FormSelectBox<IUpdateProjectPayload>
            key={categories?.length}
            labelName="Select Goals Categories (Up to 2)"
            options={sdgOptions}
            optionsArr={sdgOptions}
            control={control}
            name="sdgCategoryRefs"
            errors={errors}
            placeholder="Goals Categories"
            isLoading={isLoadingSdgs}
            defaultValue={categories}
            isMulti
          />
        </div>
        <div className="mt-5">
          {isStudentOrFaculty ? (
            <FormSelectBox<IUpdateProjectPayload>
              key={projectLevel?.label}
              labelName="Minimum Project Level"
              options={projectLevels}
              optionsArr={projectLevels}
              control={control}
              name="projectLevel"
              errors={errors}
              placeholder="Select Project Level"
              defaultValue={projectLevel}
            />
          ) : (
            <FormSelectBox<IUpdateProjectPayload>
              key={projectCost?.label}
              labelName="Presumed Project Cost"
              options={projectCosts}
              optionsArr={projectCosts}
              control={control}
              name="presumedProjectCost"
              errors={errors}
              placeholder="Select Presumed Project Cost"
              defaultValue={projectCost}
            />
          )}
        </div>
        <div className="mt-5">
          <FormFileInputBox<IUpdateProjectPayload>
            key={key}
            formLabel={<p className="mb-4">Project Image</p>}
            control={control}
            name="projectImage"
            labelName="Project Image"
            errors={{}}
            autoComplete="organization-department"
            accept="image/*"
          />
        </div>
        {project?.projectImageUrl && (
          <div className="relative mt-4 inline-block rounded-lg border-2 border-grayNineTeen">
            <img
              src={project.projectImageUrl}
              alt="Current project image"
              className="h-[100px] w-[150px] object-cover"
            />
          </div>
        )}
        <div className="mt-5">
          <FormInputBoxWithForwardRef<IUpdateProjectPayload>
            formLabel={
              <FormLabel
                name="descriptionVideoUrl"
                labelName="Description Video Link - Youtube (Optional)"
              />
            }
            className="input"
            name="descriptionVideoUrl"
            errors={errors}
            placeholder="Video Link"
            autoComplete="descriptionVideoUrl"
            registerHanlder={() => register('descriptionVideoUrl')}
          />
        </div>
        {project?.descriptionVideoUrls &&
          project.descriptionVideoUrls.length > 0 && (
            <div className="hidden flex-wrap gap-2">
              {project.descriptionVideoUrls
                .filter(
                  video =>
                    !watch('removedAssets')?.some(
                      (removed: { fileRef: string; type: string }) =>
                        removed.fileRef === video.fileRef,
                    ),
                )
                .map(video => (
                  <div
                    key={video.fileRef}
                    className="w-full max-w-48 overflow-hidden rounded-lg border-[2px] border-grayNineTeen"
                  >
                    <div className="group/first relative bg-white">
                      <div className="flex items-center gap-3 p-3">
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-red-100">
                          <svg
                            className="h-4 w-4 text-red-600"
                            fill="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path d="M23.498 6.186a2.825 2.825 0 0 0-1.988-2.004C19.706 3.678 12 3.678 12 3.678s-7.706 0-9.51.504A2.825 2.825 0 0 0 .502 6.186C0 8.004 0 12 0 12s0 3.996.502 5.814a2.825 2.825 0 0 0 1.988 2.004c1.804.504 9.51.504 9.51.504s7.706 0 9.51-.504a2.825 2.825 0 0 0 1.988-2.004C24 15.996 24 12 24 12s0-3.996-.502-5.814zM9.75 15.568V8.432L15.5 12l-5.75 3.568z" />
                          </svg>
                        </div>
                        <div className="min-w-0 flex-1">
                          <p className="truncate text-sm font-medium text-gray-900">
                            YouTube Video
                          </p>
                          <p className="truncate text-xs text-gray-500">
                            {Helper.truncateAndKeepExtension(video.name, 20)}
                          </p>
                        </div>
                      </div>
                      <div className="absolute right-2 top-2 z-[1] opacity-0 duration-500 ease-in-out group-hover/first:opacity-100">
                        <span
                          onClick={() =>
                            removeExistingAsset(
                              video.fileRef,
                              'descriptionVideoUrls',
                            )
                          }
                          className="group relative h-5 w-5 cursor-pointer rounded-full bg-white p-[2px] shadow-md hover:bg-red-50"
                        >
                          <DeleteIcon2 className="h-4 w-4 text-red-600" />
                          <div className="absolute -left-[10px] -top-[30px] rounded-lg max-xs:hidden">
                            <ToolTip text="Delete video" />
                          </div>
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          )}
        <div className="mt-5">
          <FormLabel name="comment" labelName="Attach Documents (Optional)" />
          <Button
            className={cn('input group flex gap-2', 'justify-start')}
            type="button"
            onClick={() => handleModalToggle(true)}
          >
            <AttachmentIcon className="stroke-black" />
            <p>Upload Documents</p>
          </Button>
        </div>
        <div className="mt-2 flex flex-wrap gap-2">
          {(watch('documents') || [])?.map((attachment: File, i: number) => (
            <div
              key={i}
              className="w-full max-w-24 border-[2px] border-grayNineTeen"
            >
              <div className="group/first relative rounded-[4px] bg-white after:absolute after:inset-0 after:z-[0] after:duration-500 after:ease-in-out after:content-[''] hover:after:bg-[#00000080] max-md:w-full">
                {GoalsHelper.isImage(attachment.name) && (
                  <img
                    loading="lazy"
                    src={URL.createObjectURL(attachment) || noImagePlaceholder}
                    className=" h-[50px] w-full max-w-[85px]  self-start overflow-hidden object-cover object-center"
                  />
                )}
                {GoalsHelper.isPdf(attachment.name) && (
                  <div className=" flex h-[50px] w-full max-w-[85px] items-center justify-center">
                    <PdfIcon className="h-5 w-5" />
                  </div>
                )}
                {GoalsHelper.isDoc(attachment.name) && (
                  <div className=" flex h-[50px] w-full max-w-[85px] items-center justify-center">
                    <DocIcon className="h-5 w-5" />
                  </div>
                )}

                <div className="absolute right-1 top-1 z-[1] flex items-center gap-1 opacity-0 duration-500 ease-in-out group-hover/first:opacity-100">
                  <span
                    onClick={() => removeFile(i)}
                    className="group relative h-5 w-5 cursor-pointer bg-white p-[2px]"
                  >
                    <DeleteIcon2 className="h-4 w-4" />
                    <div className="absolute -left-[10px] -top-[30px] rounded-lg max-xs:hidden">
                      <ToolTip text="Delete file" />
                    </div>
                  </span>
                </div>
              </div>
              <p className="py-[2px] text-center text-[10px]">
                {Helper.truncateAndKeepExtension(attachment?.name, 10)}
              </p>
            </div>
          ))}
        </div>

        {project?.documents && project.documents.length > 0 && (
          <div className="hidden flex-wrap gap-2">
            {project.documents
              .filter(
                doc =>
                  !watch('removedAssets')?.some(
                    (removed: { fileRef: string; type: string }) =>
                      removed.fileRef === doc.fileRef,
                  ),
              )
              .map(attachment => (
                <div
                  key={attachment.fileRef}
                  className="w-full max-w-24 border-[2px] border-grayNineTeen"
                >
                  <div className="group/first relative rounded-[4px] bg-white after:absolute after:inset-0 after:z-[0] after:duration-500 after:ease-in-out after:content-[''] hover:after:bg-[#00000080] max-md:w-full">
                    {ConversationHelper.isImage(attachment.name) && (
                      <img
                        loading="lazy"
                        src={attachment.url || noImagePlaceholder}
                        className=" h-[50px] w-full max-w-[85px]  self-start overflow-hidden object-cover object-center"
                      />
                    )}
                    {ConversationHelper.isPdf(attachment.name) && (
                      <div className=" flex h-[50px] w-full max-w-[85px] items-center justify-center">
                        <PdfIcon className="h-5 w-5" />
                      </div>
                    )}
                    {ConversationHelper.isDoc(attachment.name) && (
                      <div className=" flex h-[50px] w-full max-w-[85px] items-center justify-center">
                        <DocIcon className="h-5 w-5" />
                      </div>
                    )}

                    <div className="absolute right-1 top-1 z-[1] flex items-center gap-1 opacity-0 duration-500 ease-in-out group-hover/first:opacity-100">
                      <span
                        onClick={() =>
                          removeExistingAsset(attachment.fileRef, 'documents')
                        }
                        className="group relative h-5 w-5 cursor-pointer bg-white p-[2px]"
                      >
                        <DeleteIcon2 className="h-4 w-4" />
                        <div className="absolute -left-[10px] -top-[30px] rounded-lg max-xs:hidden">
                          <ToolTip text="Delete file" />
                        </div>
                      </span>
                    </div>
                  </div>
                  <p className="py-[2px] text-center text-[10px]">
                    {Helper.truncateAndKeepExtension(attachment?.name, 10)}
                  </p>
                </div>
              ))}
          </div>
        )}
        <div className="mt-5 flex w-full gap-4">
          <div className="flex-grow">
            <FormDatePickerBox
              control={control}
              labelName="Start Date"
              name="startDate"
              errors={errors}
              className="w-full"
              placeholderText="Start Date"
            />
          </div>
          <div className="flex-grow">
            <FormDatePickerBox
              control={control}
              labelName="End Date"
              name="endDate"
              errors={errors}
              className="w-full"
              minDate={
                new Date(watch('startDate')) > new Date()
                  ? new Date(watch('startDate'))
                  : new Date()
              }
              placeholderText="End Date"
            />
          </div>
        </div>
        <div className="flex w-full max-w-[400px] items-center gap-x-4">
          <Button
            onClick={() => {
              setShowModalHandler('');
            }}
            type="button"
            className="group mt-[32px] w-full min-w-[154px] whitespace-nowrap border-[1px] border-grayTen bg-transparent  hover:border-primary hover:bg-primary hover:text-white"
          >
            <p className="text-[16px] leading-8 text-grayTen group-hover:text-white">
              Cancel
            </p>
          </Button>
          <SharedFormButton
            isLoading={isLoading}
            isDirty={isDirty}
            isValid={isValid}
            className=""
          >
            <p className="text-[14px] sm:text-[16px]">Submit</p>
          </SharedFormButton>
        </div>
      </Form>
      <AddProjectDocumentModal
        isOpen={isDocumentModalOpen}
        onClose={() => handleModalToggle(false)}
        onFileUpload={handleFile}
      />
    </div>
  );
};
