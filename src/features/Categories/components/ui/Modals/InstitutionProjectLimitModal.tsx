import { useAppContext } from '@/context/event/AppEventContext';
import { WarningIcon } from '../../../../../assets/icons';
import Button from '../../../../../components/ui/ButtonComponent';
import ModalCase from '../../../../../components/ui/Modals/ModalCase';
import { useState } from 'react';
import useSetTimeout from '@/hooks/useSetTimeout';
import { useNavigate } from 'react-router-dom';
const InstitutionProjectLimitModal = () => {
  const navigate = useNavigate();
  const [copyMessage, setCopyMessage] = useState(false);
  useSetTimeout(() => setCopyMessage(false), 3000, [copyMessage]);
  const { setShowModalHandler, currentAccountType } = useAppContext();
  return (
    <ModalCase
      className="relative mx-auto max-h-[80%] w-[95%] overflow-y-auto  bg-white
    px-4 py-8 text-black sm:max-w-[624px] sm:px-8 sm:py-20"
    >
      <div className="mt-8">
        <WarningIcon className="mx-auto" />
        <div className="my-6 rounded-lg px-4 text-center">
          <p className="relative text-[18px]">
            You have reached the limit for adding projects. Please click the add
            more button below to extend your limit.
          </p>
        </div>
        <div className="mb-16 flex w-full flex-wrap justify-center gap-[14px]">
          <Button
            type="button"
            onClick={() => setShowModalHandler('')}
            className="w-full max-w-[102px] rounded-none border border-primary bg-transparent text-primary transition duration-500 ease-in-out hover:border-black hover:bg-black hover:text-white disabled:cursor-not-allowed disabled:text-primary"
          >
            <p className="text-[14px] sm:text-[16px]">Cancel</p>
          </Button>
          <Button
            type="button"
            onClick={() => {
              navigate(`${currentAccountType}/dashboard-profile`);
              setShowModalHandler('UpdateSubscriptionModal');
            }}
            className="w-full max-w-[102px] rounded-none bg-primary text-white transition duration-500 ease-in-out hover:bg-black disabled:cursor-not-allowed disabled:text-primary"
          >
            <p className="text-[14px] sm:text-[16px]">Add more</p>
          </Button>
        </div>
      </div>
    </ModalCase>
  );
};

export default InstitutionProjectLimitModal;
