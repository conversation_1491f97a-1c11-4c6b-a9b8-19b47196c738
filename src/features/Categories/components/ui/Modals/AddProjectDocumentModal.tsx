import { AnimatePresence } from 'framer-motion';

import ModalCase from '../../../../../components/ui/Modals/ModalCase';
import AddProjectDocumentDropZone from '../../forms/AddProject/Shared/AddProjectDocumentDropZone';
import { cn } from '@/lib/twMerge/cn';
import { createPortal } from 'react-dom';

export default function AddProjectDocumentModal({
  isOpen,
  onClose,
  onFileUpload,
}: {
  isOpen: boolean;
  onClose: () => void;
  onFileUpload: (value: File[]) => void;
}) {
  return createPortal(
    <AnimatePresence>
      {isOpen && (
        <ModalCase
          className={cn(
            'mx-auto w-[95%] overflow-y-scroll  bg-white px-4 py-8 text-black sm:max-w-[623px] sm:px-8 sm:py-16',
            isOpen && 'pointer-events-auto',
          )}
          onClose={onClose}
        >
          <div className="mt-8">
            <AddProjectDocumentDropZone onFileUpload={onFileUpload} />
          </div>
        </ModalCase>
      )}
    </AnimatePresence>,
    document.body,
  );
}
