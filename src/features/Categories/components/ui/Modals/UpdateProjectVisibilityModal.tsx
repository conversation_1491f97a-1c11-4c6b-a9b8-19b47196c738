import Button from '@/components/ui/ButtonComponent';
import ModalCase from '@/components/ui/Modals/ModalCase';
import { useAppContext } from '@/context/event/AppEventContext';
import { useUpdateProjectVisibility } from '@/features/Categories/hooks/apiQueryHooks/eduQueryHooks';
import useHandleApiFeebackWithToast from '@/hooks/useHandleApiFeebackWithToast';
import { useHandleQueryParams } from '@/hooks/useHandleQueryParams';
import { VisibilityType } from '@/types';
import { GET_PROJECT_QUERY } from '@/utils/queryKeys';
import { AnimatePresence } from 'framer-motion';
import { useParams } from 'react-router-dom';

export default function UpdateProjectVisibilityModal() {
  const next = () => {
    setShowModalHandler('');
    handleQuery({ visibility: undefined, project_name: undefined });
  };
  const { projectRef } = useParams();
  const { query, handleQuery } = useHandleQueryParams();
  const { showModal, setShowModalHandler } = useAppContext();
  const { mutate, isLoading } = useUpdateProjectVisibility({
    ...useHandleApiFeebackWithToast({ next, queryKey: [GET_PROJECT_QUERY] }),
  });
  const visibilityValue = query.get('visibility') as VisibilityType;
  const projectName = query.get('project_name');
  return (
    <AnimatePresence>
      {showModal === 'UpdateProjectVisibilityModal' && (
        <ModalCase
          className="relative mx-auto max-h-[80%] w-[95%] overflow-y-auto  bg-white
        px-4 py-8 text-black sm:max-w-[624px] sm:px-8 sm:py-20"
        >
          <div className="mt-8">
            <p className="mx-auto mb-4 w-full max-w-[450px] text-center text-[16px] text-subText">
              Are you sure you want to make{' '}
              <span className="text-primary">{projectName}</span>{' '}
              {visibilityValue.toLowerCase()}?
            </p>
            <div className="mt-10 flex w-full flex-wrap justify-center gap-9">
              <Button
                type="button"
                className="group h-[36px] w-full max-w-[147px] whitespace-nowrap !rounded-none border-[0.73px] border-grayNine bg-transparent hover:border-primary hover:bg-primary"
                onClick={() => {
                  setShowModalHandler('');
                }}
              >
                <p className="text-[12.6px] leading-[24px] text-grayNine group-hover:text-white">
                  Cancel
                </p>
              </Button>
              <Button
                disabled={isLoading}
                type="button"
                onClick={() => {
                  mutate({
                    visibility: visibilityValue,
                    projectRef: projectRef || '',
                  });
                }}
                className="h-[36px] w-full max-w-[200px] whitespace-nowrap !rounded-none border-[0.73px] border-primary bg-primary hover:border-black hover:bg-black disabled:cursor-not-allowed disabled:border-disabled disabled:bg-disabled disabled:text-opacity-70"
              >
                <p className="text-[12.6px] leading-[24px] text-white">
                  Proceed
                </p>
              </Button>
            </div>
          </div>
        </ModalCase>
      )}
    </AnimatePresence>
  );
}
