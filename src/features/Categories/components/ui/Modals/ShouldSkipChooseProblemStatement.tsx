import { AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';

import Button from '../../../../../components/ui/ButtonComponent';
import ModalCase from '../../../../../components/ui/Modals/ModalCase';
import { useAppContext } from '../../../../../context/event/AppEventContext';
import { useHandleQueryParams } from '@/hooks/useHandleQueryParams';

export default function ShouldSkipChooseProblemStatement() {
  const { setShowModalHandler, currentAccountType, showModal } =
    useAppContext();
  const navigate = useNavigate();
  const { query } = useHandleQueryParams();
  const goalRef = query.get('goal_ref') || '';
  return (
    <AnimatePresence>
      {showModal === 'ShouldSkipChooseProblemStatement' && (
        <ModalCase
          className="mx-auto w-[95%] overflow-y-scroll  bg-white px-4
  py-8 text-black sm:max-w-[623px] sm:px-8 sm:py-16"
        >
          <div className="mt-8">
            <p className="mb-4 text-center text-[18px] font-[600] text-subText">
              Choose Problem Statement For Project?
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-[14px]">
              <Button
                type="button"
                onClick={() => {
                  navigate(
                    `/${currentAccountType}/dashboard-projects/choose-statement?${goalRef ? `goal_ref=${goalRef}` : ''}`,
                  );
                  setShowModalHandler('');
                }}
                className="w-full max-w-[140px] border border-primary bg-white 
      text-primary   hover:bg-primary hover:text-white"
              >
                <p className="text-[12px]">Choose</p>
              </Button>
              <Button
                type="button"
                onClick={() => {
                  navigate(
                    `/${currentAccountType}/dashboard-projects/add-project?${goalRef ? `goal_ref=${goalRef}` : ''}`,
                  );
                  setShowModalHandler('');
                }}
                className=" w-full max-w-[140px] bg-primary"
              >
                <p className="text-[12px] text-white"> Skip</p>
              </Button>
            </div>
          </div>
        </ModalCase>
      )}
    </AnimatePresence>
  );
}
