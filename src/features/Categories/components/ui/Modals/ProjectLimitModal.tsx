import { useUserContext } from '@/context/user/UserContext';
import InstitutionProjectLimitModal from './InstitutionProjectLimitModal';
import { OtherProjectLimitModal } from './OtherProjectLimitModal';

const ProjectLimitModal = () => {
  const { subscriptionType } = useUserContext();
  return subscriptionType === 'INSTITUTION' ? (
    <InstitutionProjectLimitModal />
  ) : (
    <OtherProjectLimitModal />
  );
};

export default ProjectLimitModal;
