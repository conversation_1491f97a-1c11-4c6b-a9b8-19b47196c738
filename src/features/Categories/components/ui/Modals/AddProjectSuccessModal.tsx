import { useNavigate } from 'react-router-dom';

import Button from '../../../../../components/ui/ButtonComponent';
import SuccessModal from '../../../../../components/ui/Modals/SuccessModal';
import { useAppContext } from '../../../../../context/event/AppEventContext';
import { useHandleQueryParams } from '../../../../../hooks/useHandleQueryParams';

export default function AddProjectSuccessModal() {
  const { setShowModalHandler, currentAccountType } = useAppContext();
  const navigate = useNavigate();
  const { query } = useHandleQueryParams();
  return (
    <SuccessModal key="AddProjectSuccessModal">
      <p className="mb-4 text-center text-[18px] font-[600] text-subText">
        Project created!
      </p>
      <div className="mt-10 flex items-center justify-center gap-x-[14px]">
        <Button
          type="button"
          onClick={() => {
            navigate(
              `/${currentAccountType}/dashboard-projects/${query.get(
                'project_ref',
              )}/add-team-to-project`,
            );
            setShowModalHandler('');
          }}
          className=" w-full max-w-[140px] bg-primary  text-white"
        >
          <p className="text-[12px]">Add Team</p>
        </Button>
        <Button
          type="button"
          onClick={() => {
            navigate(
              `/${currentAccountType}/dashboard-projects/${query.get(
                'project_ref',
              )}`,
            );
            setShowModalHandler('');
          }}
          className="w-full max-w-[140px] border border-primary bg-white 
          text-primary   hover:bg-primary hover:text-white"
        >
          <p className="text-[12px]">View Project</p>
        </Button>
        <Button
          type="button"
          onClick={() => {
            navigate(`/${currentAccountType}/goals`);
            setShowModalHandler('');
          }}
          className="w-full max-w-[140px] border border-primary bg-white 
          text-primary   hover:bg-primary hover:text-white"
        >
          <p className="text-[12px]">View Goals</p>
        </Button>
      </div>
    </SuccessModal>
  );
}
