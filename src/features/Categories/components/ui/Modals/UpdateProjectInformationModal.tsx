import { AnimatePresence } from 'framer-motion';

import ModalCase from '@/components/ui/Modals/ModalCase';
import { UpdateProjectInformationForm } from '@/features/Categories/components/forms/UpdateProjectInformation';

import { useAppContext } from '@/context/event/AppEventContext';

export const UpdateProjectInformationModal = () => {
  const { showModal } = useAppContext();
  return (
    <AnimatePresence>
      {showModal === 'UpdateProjectInformationModal' && (
        <ModalCase className="mx-auto max-h-[90%] w-full max-w-[518px] overflow-y-scroll  bg-white px-4 py-8 text-black sm:max-w-[836px] sm:px-8 sm:py-16">
          <div className="mt-8">
            <UpdateProjectInformationForm />
          </div>
        </ModalCase>
      )}
    </AnimatePresence>
  );
};
