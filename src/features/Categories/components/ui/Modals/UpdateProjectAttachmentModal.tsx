import { AnimatePresence } from 'framer-motion';

import ModalCase from '@/components/ui/Modals/ModalCase';

import { useAppContext } from '@/context/event/AppEventContext';
import { UpdateProjectAttachmentForm } from '../../forms/UpdateProjectAttachmentForm';

export const UpdateProjectAttachmentModal = () => {
  const { showModal } = useAppContext();
  return (
    <AnimatePresence>
      {showModal === 'UpdateProjectAttachmentModal' && (
        <ModalCase className="mx-auto max-h-[90%] w-full max-w-[518px] overflow-y-scroll  bg-white px-4 py-8 text-black sm:max-w-[836px] sm:px-8 sm:py-16">
          <div className="mt-8">
            <UpdateProjectAttachmentForm />
          </div>
        </ModalCase>
      )}
    </AnimatePresence>
  );
};
