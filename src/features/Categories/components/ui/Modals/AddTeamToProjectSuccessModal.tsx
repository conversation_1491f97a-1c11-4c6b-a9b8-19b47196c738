import { useNavigate, useParams } from 'react-router-dom';

import Button from '../../../../../components/ui/ButtonComponent';
import SuccessModal from '../../../../../components/ui/Modals/SuccessModal';
import { useAppContext } from '../../../../../context/event/AppEventContext';

export default function AddTeamToProjectSuccessModal() {
  const { setShowModalHandler, currentAccountType } = useAppContext();
  const navigate = useNavigate();
  const { projectRef } = useParams();
  return (
    <SuccessModal key="AddTeamToProjectSuccessModal">
      <p className="mb-4 text-center text-[18px] font-[600] text-subText">
        Team added to project successfully!
      </p>
      <div className="mt-10 flex items-center justify-center gap-x-[14px]">
        <Button
          type="button"
          onClick={() => {
            setShowModalHandler('');
          }}
          className="w-full max-w-[140px] border border-primary bg-white 
      text-primary   hover:bg-primary hover:text-white"
        >
          <p className="text-[12px]">Add More</p>
        </Button>
        <Button
          type="button"
          onClick={() => {
            navigate(`/${currentAccountType}/dashboard-projects/${projectRef}`);
            setShowModalHandler('');
          }}
          className=" w-full max-w-[140px] bg-primary"
        >
          <p className="text-[12px] text-white">View Project</p>
        </Button>
      </div>
    </SuccessModal>
  );
}
