import ModalCase from '@/components/ui/Modals/ModalCase';
import { useAppContext } from '@/context/event/AppEventContext';
import { AnimatePresence } from 'framer-motion';
import { UpdateProjectImageForm } from '../../forms/UpdateProjectImage';

export const UpdateProjectImageModal = () => {
  const { showModal } = useAppContext();
  return (
    <AnimatePresence>
      {showModal === 'UpdateProjectImageModal' && (
        <ModalCase className="mx-auto w-full max-w-[518px] overflow-y-scroll  bg-white px-4 py-8 text-black sm:max-w-[623px] sm:px-8 sm:py-16">
          <div className="mt-8">
            <UpdateProjectImageForm />
          </div>
        </ModalCase>
      )}
    </AnimatePresence>
  );
};
