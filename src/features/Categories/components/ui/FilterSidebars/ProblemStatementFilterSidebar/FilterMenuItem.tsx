import { useCallback, useState } from 'react';

import { CaretArrowDown } from '../../../../../../assets/icons';
import { problemCategory } from '../../../../../../data/constants';
import { Helper } from '../../../../../../utils/helpers';
import useCreateListForProblemStatements from '../../../../hooks/useCreateListForProblemStatements';
import FilterSubMenuList from './FilterSubMenuList';

type Props<T> = T extends Array<infer item> ? item : never;
export default function FilterMenuItem({
  category,
  icon: Icon = null,
  subMenuList,
  categoryRef,
}: Props<ReturnType<typeof useCreateListForProblemStatements>>) {
  const [currentDropdown, setCurrentDropdown] = useState('');
  const handleDropdown = useCallback(
    (currentRef: string) => {
      if (currentDropdown === currentRef) setCurrentDropdown('');
      else setCurrentDropdown(currentRef);
    },
    [currentDropdown],
  );
  const isActive = (categoryRef: string) => currentDropdown === categoryRef;
  return (
    <div className="cursor-pointer border border-graySeven bg-grayTwo py-4 ">
      <div
        id={category + categoryRef}
        className={` ${
          isActive(categoryRef) ? 'text-primary' : ''
        } flex items-center justify-between px-[20px] `}
        onClick={() => handleDropdown(categoryRef)}
      >
        <div className="flex items-center gap-x-2 text-[14px] font-[500]">
          {Icon && (
            <Icon
              className={` 
          ${
            category === problemCategory.foodSupplyChain
              ? `${isActive(categoryRef) ? 'fill-primary' : 'fill-grayNine'}`
              : `${
                  isActive(categoryRef) ? 'stroke-primary' : 'stroke-grayNine'
                }`
          }
           duration-500 ease-in-out`}
            />
          )}
          {Helper.capitalizeString(category)}
        </div>
        <CaretArrowDown
          className={`   ${
            isActive(categoryRef) ? 'rotate-180 stroke-primary' : ''
          } h-3 w-3 duration-500 ease-in-out`}
        />
      </div>
      <FilterSubMenuList
        className={`${
          isActive(categoryRef) ? 'max-h-screen' : 'max-h-0'
        } px-4 `}
        list={subMenuList}
      />
    </div>
  );
}
