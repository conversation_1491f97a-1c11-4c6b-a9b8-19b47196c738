import { useAppContext } from '@/context/event/AppEventContext';

export default function FilterSubMenuItem({
  title,
  subcategoryRef,
}: {
  title: string;
  count: number;
  subcategoryRef: string;
}) {
  const { filters, handleSetFilters } = useAppContext();
  return (
    <li className="flex items-center justify-between px-1 py-2">
      <div className="flex items-center gap-x-2">
        <input
          className="cursor-pointer checked:!bg-primary"
          type="checkbox"
          checked={filters.includes(subcategoryRef)}
          onChange={() => handleSetFilters(subcategoryRef)}
        />
        <p>{title}</p>
      </div>
    </li>
  );
}
