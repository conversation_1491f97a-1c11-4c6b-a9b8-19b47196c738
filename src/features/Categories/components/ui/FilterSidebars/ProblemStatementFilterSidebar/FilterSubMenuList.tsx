import { Subcategory } from '../../../../../../types';
import FilterSubMenuItem from './FilterSubMenuItem';

export default function FilterSubMenuList({
  list,
  className,
}: {
  list: Subcategory[];
  className: string;
}) {
  return (
    <ul
      className={`${className} ease flex flex-col overflow-auto text-[14px] text-subText duration-500`}
    >
      {list.map(({ count, subcategoryName, subcategoryRef }, index) => (
        <FilterSubMenuItem
          key={index}
          count={count}
          title={subcategoryName}
          subcategoryRef={subcategoryRef}
        />
      ))}
    </ul>
  );
}
