import { Spinner } from '../../../../../../components/ui/CommonWidget/Loader';
import { useGetSDGs } from '../../../../../../hooks/apiQueryHooks/eduQueryHooks';
import useCreateListForProblemStatements from '../../../../hooks/useCreateListForProblemStatements';
import FilterMenuItem from './FilterMenuItem';

export default function ProblemStatementFilterSidebar() {
  const { data, isLoading } = useGetSDGs({
    staleTime: 1000 * 60 * 60 * 24,
    cacheTime: 1000 * 60 * 60 * 25,
  });
  return (
    <div className="w-[352px]">
      <h2 className="border border-graySeven bg-grayTwo px-[20px] py-4 text-[18px] uppercase tracking-wider">
        Categories
      </h2>
      {isLoading && (
        <div className="mt-10">
          <Spinner />
        </div>
      )}
      {useCreateListForProblemStatements(data?.data || [])?.map(
        ({ category, icon, subMenuList, categoryRef }, index) => (
          <FilterMenuItem
            key={index}
            category={category}
            icon={icon}
            subMenuList={subMenuList}
            categoryRef={categoryRef}
          />
        ),
      )}
    </div>
  );
}
