import { Spinner } from '../../../../../../components/ui/CommonWidget/Loader'
import { useGetCourseCategories } from '../../../../../../hooks/apiQueryHooks/eduQueryHooks'
import useCreateListForCourses from '../../../../../../hooks/useCreateListForCourses'

export default function CourseFilterSidebar({
  filteredCategoryRefList,
  handleCategoryRef,
}: {
  filteredCategoryRefList: string[]
  handleCategoryRef: (value: string) => void
}) {
  const { data, isLoading } = useGetCourseCategories({
    staleTime: 1000 * 60 * 60 * 24,
    cacheTime: 1000 * 60 * 60 * 25,
  })

  return (
    <div className="mb-6">
      <h2 className="text-[16px] py-4 border border-graySeven bg-grayTwo px-[20px] uppercase tracking-wider">
        Category
      </h2>
      {isLoading && (
        <div className="my-10 bg-grayTwo">
          <Spinner />
        </div>
      )}
      <div className="py-4 border border-graySeven cursor-pointer bg-grayTwo ">
        <ul className="overflow-auto ease duration-500 flex flex-col text-[14px] text-subText px-4">
          {useCreateListForCourses(data?.data || [])?.map(
            ({ category, categoryRef }, index) => (
              <li className="flex gap-x-2 items-center py-2 px-1" key={index}>
                <input
                  type="checkbox"
                  checked={filteredCategoryRefList.includes(categoryRef)}
                  onClick={() => handleCategoryRef(categoryRef)}
                />
                <p>{category}</p>
              </li>
            )
          )}
        </ul>
      </div>
    </div>
  )
}
