import { useEffect } from 'react'

import { useHandleQueryParams } from '../../../../../../hooks/useHandleQueryParams'
import { COURSE_SECTIONS } from '../../../../data/constant'
import { StudentCourseTypes } from '../../../../types'

export default function ProviderFilterSidebar() {
  const { query, handleQuery } = useHandleQueryParams()

  const courseType = query.get('courseType') as StudentCourseTypes

  const updateCourseType = (type: StudentCourseTypes) => {
    if (type === courseType) {
      return handleQuery({ courseType: undefined })
    }
    handleQuery({ courseType: type })
  }

  useEffect(() => {
    handleQuery({ page: 1 })
  }, [courseType])

  return (
    <div className="mb-6">
      <h2 className="text-[16px] py-4 border border-graySeven bg-grayTwo px-[20px]  uppercase tracking-wider">
        Provider
      </h2>
      <div className="py-4 border border-graySeven cursor-pointer bg-grayTwo ">
        <ul className="overflow-auto ease duration-500 flex flex-col text-[14px] text-subText px-4">
          {/* <li className="flex items-center justify-between py-2 px-1">
            <div className="flex gap-x-2 items-center">
              <input
                type="checkbox"
                checked={courseType === COURSE_SECTIONS.UDEMY}
                onChange={() => updateCourseType(COURSE_SECTIONS.UDEMY)}
              />
              <p>Udemy</p>
            </div>
          </li>
          <li className="flex items-center justify-between py-2 px-1">
            <div className="flex gap-x-2 items-center">
              <input
                type="checkbox"
                checked={courseType === COURSE_SECTIONS.COURSERA}
                onChange={() => updateCourseType(COURSE_SECTIONS.COURSERA)}
              />
              <p>Coursera</p>
            </div>
          </li>
          <li className="flex items-center justify-between py-2 px-1">
            <div className="flex gap-x-2 items-center">
              <input
                type="checkbox"
                checked={courseType === COURSE_SECTIONS.UDACITY}
                onChange={() => updateCourseType(COURSE_SECTIONS.UDACITY)}
              />
              <p>Udacity</p>
            </div>
          </li> */}
          <li className="flex items-center justify-between py-2 px-1">
            <div className="flex gap-x-2 items-center">
              <input
                type="checkbox"
                checked={courseType === COURSE_SECTIONS.UPICK}
                onChange={() => updateCourseType(COURSE_SECTIONS.UPICK)}
              />
              <p>uPick</p>
            </div>
          </li>
        </ul>
      </div>
    </div>
  )
}
