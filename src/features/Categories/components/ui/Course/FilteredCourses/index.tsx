import { Fragment, useCallback, useEffect, useMemo, useState } from 'react';

import PaginationNavigator from '../../../../../../components/ui/PaginationNavigator/PaginationNavigator';
import { SeeMoreButton } from '../../../../../../components/ui/SeeMoreButton/SeeMoreButton';
import { useGetUpicks } from '../../../../../../hooks/apiQueryHooks/eduQueryHooks';
import { useDebounce } from '../../../../../../hooks/useDebounce';
import { useHandleQueryParams } from '../../../../../../hooks/useHandleQueryParams';
import { usePaginationHandler } from '../../../../../../hooks/usePaginationHandler';
import useScrollToTop from '../../../../../../hooks/useScrollToTop';
import { CourseCategoryList, UpickContainers } from '../../../../../Home';
import { UpickIcon } from '../../../../../Home/assets/icons';
import { COURSE_SECTIONS } from '../../../../data/constant';
import { CourseIcons } from '../../../../data/data';
import {
  useGetCourseraCourses,
  useGetUdacityCourses,
  useGetUdemyCourses,
} from '../../../../hooks/apiQueryHooks/eduQueryHooks';
import CourseCategoryLayout from '../../../../layout/CourseCategoryLayout';
import CourseEmptyState from '../CourseEmptyState';
import { CourseCategoryTypeCase } from '../Courses/CourseCategoryTypeCase';

export default function FilteredCourses() {
  // TODO: abstract logic to a custom hook. line 23 - 122
  const { scrollToTop } = useScrollToTop();
  const { query, handleQuery } = useHandleQueryParams();
  const [filteredCategoryRefList, setFilteredCategoryRefList] = useState<
    string[]
  >([]);
  const courseType = query.get('courseType');
  const keyword = useDebounce(query.get('q'));

  const handleCategoryRef = useCallback((value: string) => {
    setFilteredCategoryRefList(previous => {
      if (previous.includes(value)) {
        const updatedArray = previous.filter(element => element !== value);
        return updatedArray;
      }
      return [...previous, value];
    });
  }, []);

  useEffect(() => {
    const categoryRef = query.get('categoryRef') as string;
    if (categoryRef) {
      setFilteredCategoryRefList([categoryRef]);
      handleQuery({ categoryRef: undefined });
    }
  }, []);

  const { currentPage, handlePageChange } = usePaginationHandler();

  const isValidCourseType = useMemo(
    () =>
      courseType &&
      Object.keys(CourseIcons).find(
        type => type.toLowerCase() == courseType.toLowerCase(),
      ),
    [courseType],
  )!;

  const Icon = useMemo(
    () =>
      courseType && isValidCourseType ? CourseIcons[courseType] : Fragment,
    [courseType],
  );

  const pageSize = isValidCourseType ? 24 : 8;

  const queryParams = useMemo(
    () => ({
      pageSize,
      page: currentPage,
      search: keyword,
      categoryRefList: filteredCategoryRefList,
    }),
    [pageSize, currentPage, keyword, filteredCategoryRefList],
  );

  const { data: udemyData, isLoading: isUdemyLoading } = useGetUdemyCourses(
    queryParams,
    {
      enabled: courseType == null || courseType === COURSE_SECTIONS.UDEMY,
    },
  );

  const { data: courseraData, isLoading: isCourseraLoading } =
    useGetCourseraCourses(queryParams, {
      enabled: courseType == null || courseType === COURSE_SECTIONS.COURSERA,
    });

  const { data: udacityData, isLoading: isUdacityLoading } =
    useGetUdacityCourses(queryParams, {
      enabled: courseType == null || courseType === COURSE_SECTIONS.UDACITY,
    });

  const { data: getUPickData, isLoading: isUpickLoading } = useGetUpicks(
    queryParams,
    { enabled: courseType == null || courseType === COURSE_SECTIONS.UPICK },
  );

  const courseList = useMemo(() => {
    switch (courseType) {
      case COURSE_SECTIONS.UDEMY: {
        return udemyData?.data?.UdemyCourses;
      }
      case COURSE_SECTIONS.COURSERA: {
        return courseraData?.data?.CourseraCourses;
      }
      case COURSE_SECTIONS.UDACITY: {
        return udacityData?.data?.UdacityCourses;
      }
      default: {
        return [];
      }
    }
  }, [courseType, udemyData, courseraData, udacityData]);

  const courseLoading = useMemo(() => {
    switch (courseType) {
      case COURSE_SECTIONS.UDEMY: {
        return isUdemyLoading;
      }
      case COURSE_SECTIONS.COURSERA: {
        return isCourseraLoading;
      }
      case COURSE_SECTIONS.UDACITY: {
        return isUdacityLoading;
      }
      case COURSE_SECTIONS.UPICK: {
        return isUpickLoading;
      }
      default: {
        return false;
      }
    }
  }, [courseType, udemyData, udacityData, courseraData, getUPickData]);

  const coursePagination = useMemo(() => {
    switch (courseType) {
      case COURSE_SECTIONS.UDEMY: {
        return udemyData?.data?.pagination;
      }
      case COURSE_SECTIONS.COURSERA: {
        return courseraData?.data?.pagination;
      }
      case COURSE_SECTIONS.UDACITY: {
        return udacityData?.data?.pagination;
      }
      case COURSE_SECTIONS.UPICK: {
        return getUPickData?.data.pagination;
      }
    }
  }, [courseType, udemyData, udacityData, courseraData, getUPickData]);

  return (
    <CourseCategoryLayout
      handleCategoryRef={handleCategoryRef}
      filteredCategoryRefList={filteredCategoryRefList}
    >
      <div className="flex-1 max-sm:px-5 sm:pr-8">
        {isValidCourseType ? (
          <>
            <div className="px-4 pb-16">
              <CourseCategoryTypeCase icon={Icon} isLoading={courseLoading}>
                {courseType === COURSE_SECTIONS.UPICK ? (
                  <>
                    {(getUPickData?.data?.upick || []).length > 0 ? (
                      <UpickContainers
                        courseList={getUPickData?.data?.upick || []}
                        watchRoute={`/watch`}
                        className="flex-wrap overflow-visible"
                      />
                    ) : (
                      <CourseEmptyState />
                    )}
                  </>
                ) : courseList && courseList?.length > 0 ? (
                  <CourseCategoryList
                    watchRoute={`/watch`}
                    courseList={courseList || []}
                    className="flex-wrap justify-center"
                  />
                ) : (
                  <CourseEmptyState />
                )}
              </CourseCategoryTypeCase>
            </div>
            <PaginationNavigator
              isLoading={courseLoading}
              currentPage={currentPage}
              pagination={coursePagination}
              handlePageChange={page => {
                handlePageChange(page);
                scrollToTop();
              }}
            />
          </>
        ) : (
          <>
            <div className="px-4 pb-16">
              <CourseCategoryTypeCase
                icon={UpickIcon}
                isLoading={isUpickLoading}
              >
                <UpickContainers
                  courseList={getUPickData?.data?.upick || []}
                  watchRoute={`/watch`}
                  className="flex-wrap justify-center"
                />
                <SeeMoreButton
                  onClick={() => {
                    handleQuery({ courseType: COURSE_SECTIONS.UPICK });
                    scrollToTop();
                  }}
                />
              </CourseCategoryTypeCase>
            </div>
            {/* <div className="pb-16 px-4">
              <CourseCategoryTypeCase
                icon={CourseIcons.Udemy}
                isLoading={isUdemyLoading}
              >
                <CourseCategoryList
                  watchRoute={`/watch`}
                  courseList={udemyData?.data?.UdemyCourses || []}
                  className="flex-wrap justify-center"
                />
                <SeeMoreButton
                  onClick={() => {
                    handleQuery({ courseType: COURSE_SECTIONS.UDEMY });
                    scrollToTop();
                  }}
                />
              </CourseCategoryTypeCase>
            </div> */}
            {/* <div className="pb-16 px-4">
              <CourseCategoryTypeCase icon={CourseIcons.Coursera}>
                <CourseCategoryList
                  watchRoute={`/watch`}
                  courseList={courseraData?.data?.CourseraCourses || []}
                  className="flex-wrap justify-center"
                />
                <SeeMoreButton
                  onClick={() => {
                    handleQuery({ courseType: COURSE_SECTIONS.COURSERA });
                    scrollToTop();
                  }}
                />
              </CourseCategoryTypeCase>
            </div> */}
            {/* <div className="pb-16 px-4">
              <CourseCategoryTypeCase icon={CourseIcons.Udacity}>
                <CourseCategoryList
                  watchRoute={`/watch`}
                  courseList={udacityData?.data?.UdacityCourses || []}
                  className="flex-wrap justify-center"
                />
                <SeeMoreButton
                  onClick={() => {
                    handleQuery({ courseType: COURSE_SECTIONS.UDACITY });
                    scrollToTop();
                  }}
                />
              </CourseCategoryTypeCase>
            </div> */}
          </>
        )}
      </div>
    </CourseCategoryLayout>
  );
}
