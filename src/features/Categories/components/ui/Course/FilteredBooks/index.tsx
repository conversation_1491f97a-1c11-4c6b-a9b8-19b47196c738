import { useCallback, useEffect, useMemo, useState } from 'react';

import BookEmptyState from '../BookEmptyState';
import { CourseCategoryTypeCase } from '../Courses/CourseCategoryTypeCase';
import BooksCategoryLayout from '@/features/Categories/layout/BooksCategoryLayout';
import { BookContainers } from '@/features/Home/components/ui/BookCategory/BookContainers';
import PaginationNavigator from '../../../../../../components/ui/PaginationNavigator/PaginationNavigator';

import { useDebounce } from '../../../../../../hooks/useDebounce';
import useScrollToTop from '../../../../../../hooks/useScrollToTop';
import { useGetBooks } from '../../../../../../hooks/apiQueryHooks/eduQueryHooks';
import { useHandleQueryParams } from '../../../../../../hooks/useHandleQueryParams';
import { usePaginationHandler } from '../../../../../../hooks/usePaginationHandler';

import { booksData } from '@/features/Home/data/mockData';

export default function FilteredBooks() {
  const { scrollToTop } = useScrollToTop();
  const { query, handleQuery } = useHandleQueryParams();
  const [filteredCategoryRefList, setFilteredCategoryRefList] = useState<
    string[]
  >([]);
  const keyword = useDebounce(query.get('q'));

  const handleCategoryRef = useCallback((value: string) => {
    setFilteredCategoryRefList(previous => {
      if (previous.includes(value)) {
        const updatedArray = previous.filter(element => element !== value);
        return updatedArray;
      }
      return [...previous, value];
    });
  }, []);

  useEffect(() => {
    const categoryRef = query.get('categoryRef') as string;
    if (categoryRef) {
      setFilteredCategoryRefList([categoryRef]);
      handleQuery({ categoryRef: undefined });
    }
  }, []);

  const { currentPage, handlePageChange } = usePaginationHandler();

  const queryParams = useMemo(
    () => ({
      pageSize: 24,
      page: currentPage,
      search: keyword,
      categoryRefs: filteredCategoryRefList,
    }),
    [currentPage, keyword, filteredCategoryRefList],
  );

  const { data, isLoading } = useGetBooks(queryParams);

  const bookList = data?.data?.books;

  return (
    <BooksCategoryLayout
      handleCategoryRef={handleCategoryRef}
      filteredCategoryRefList={filteredCategoryRefList}
    >
      <div className="flex-1 max-sm:px-5 sm:pr-8">
        <div className="px-4 pb-16">
          <CourseCategoryTypeCase isLoading={isLoading}>
            <p className="text-[18px] font-semibold leading-[48px] text-black">
              All uBooks
            </p>
            {(bookList && bookList?.length > 0) || booksData.length > 0 ? (
              <BookContainers
                watchRoute={`/read`}
                bookList={bookList || []}
                className="flex-wrap justify-center"
              />
            ) : (
              <BookEmptyState />
            )}
          </CourseCategoryTypeCase>
        </div>
        <PaginationNavigator
          isLoading={isLoading}
          currentPage={currentPage}
          pagination={data?.data?.pagination}
          handlePageChange={page => {
            handlePageChange(page);
            scrollToTop();
          }}
        />
      </div>
    </BooksCategoryLayout>
  );
}
