import { notFound } from '../../../../../../../../assets/images';
import Button from '../../../../../../../../components/ui/ButtonComponent';
import CommonDashedBorderBox from '../../../../../../../../components/ui/CommonWidget/CommonDashedBorderBox';
import { useHandleQueryParams } from '../../../../../../../../hooks/useHandleQueryParams';
import { COURSE_SECTIONS } from '../../../../../../data/constant';

export const StudentCourseEmptyState = () => {
  const { handleQuery } = useHandleQueryParams();
  return (
    <CommonDashedBorderBox>
      <div>
        <img src={notFound} alt=" not found" className="mx-auto mb-[2rem]" />
        <p className="mb-[14px] text-center text-[18px] font-medium leading-7">
          No courses found
        </p>
        <div className="mx-auto">
          <Button
            onClick={() => handleQuery({ courseType: COURSE_SECTIONS.ALL })}
            className="mx-auto my-auto mb-8 w-full max-w-[144px] rounded-[5px] border border-solid border-black text-[14px] text-black hover:border-[color:transparent] hover:bg-primary hover:text-white "
          >
            <p>Start Learning</p>
          </Button>
        </div>
      </div>
    </CommonDashedBorderBox>
  );
};
