import { ReactNode } from 'react';

import { ArrowBackIcon } from '../../../../../../../../assets/icons';
import Filter from '../../../../../../../../components/ui/Filter';
import SearchBar from '../../../../../../../../components/ui/SearchBar';
import { useHandleQueryParams } from '../../../../../../../../hooks/useHandleQueryParams';
import { COURSE_SECTIONS } from '../../../../../../data/constant';
import { StudentFilters } from './StudentFilters';

export const StudentCourseCategoryLayout = ({
  children,
  courseType,
  isValidCourseType,
  filteredCategoryRefList,
  handleCategoryRef,
}: {
  children: ReactNode;
  courseType?: string;
  isValidCourseType: boolean;
  filteredCategoryRefList?: string[];
  handleCategoryRef: (value: string) => void;
}) => {
  const { resetQuery, handleQuery } = useHandleQueryParams();

  return (
    <div>
      <div className="mb-11 flex w-full flex-col gap-4 sm:flex-row sm:items-end sm:justify-between">
        <div className="flex grow flex-col gap-4">
          {courseType && isValidCourseType && (
            <ArrowBackIcon
              className="cursor-pointer"
              onClick={() => resetQuery()}
            />
          )}
          {isValidCourseType && courseType !== COURSE_SECTIONS.MY_LEARNING && (
            <div className="flex grow gap-6">
              <StudentFilters
                handleCategoryRef={handleCategoryRef}
                filteredCategoryRefList={filteredCategoryRefList || []}
              />
              <SearchBar setSearchValue={value => handleQuery({ q: value })} />
            </div>
          )}
        </div>
        <div className="flex items-center gap-14">
          <Filter options={[]} />
          {/* <StudentWishlist /> */}
        </div>
      </div>
      {children}
    </div>
  );
};
