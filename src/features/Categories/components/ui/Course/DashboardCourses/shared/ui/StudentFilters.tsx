import { useRef, useState } from 'react';

import { useOnClickOutside } from '../../../../../../../../hooks/useOnClickOutside';
import CourseFilterSidebar from '../../../../FilterSidebars/CourseFilterSidebar';
import { FilterIcon } from '@/assets/icons';

export const StudentFilters = ({
  filteredCategoryRefList,
  handleCategoryRef,
}: {
  filteredCategoryRefList?: string[];
  handleCategoryRef: (value: string) => void;
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useOnClickOutside(ref, (e?: MouseEvent | TouchEvent) => {
    const target = e?.target as Element;
    const filterButton = document.querySelector('#filter-btn');

    if (
      (target?.id && target.id === 'filter-btn') ||
      filterButton?.contains(target)
    )
      return;
    setIsOpen(false);
  });

  return (
    <div className="relative">
      <div
        onClick={() => setIsOpen(previous => !previous)}
        id="filter-btn"
        className={`flex h-[37px] items-center border bg-grayTwo px-6 ${
          isOpen ? 'border-primary text-primary' : ''
        } cursor-pointer`}
      >
        <FilterIcon
          className={`${isOpen ? 'stroke-primary' : 'stroke-grayTen'} mr-3`}
        />
        <p>Filter</p>
      </div>
      {isOpen && (
        <div ref={ref} className="absolute top-full z-[10] w-[303px] pt-[30px]">
          <CourseFilterSidebar
            handleCategoryRef={handleCategoryRef}
            filteredCategoryRefList={filteredCategoryRefList || []}
          />
        </div>
      )}
    </div>
  );
};
