import { toUpper } from 'lodash';
import { useCallback, useMemo, useState } from 'react';

import Slide<PERSON>ontroller from '../../../../../../../components/ui/CommonWidget/SlideController';
import PaginationNavigator from '../../../../../../../components/ui/PaginationNavigator/PaginationNavigator';
import { useAppContext } from '../../../../../../../context/event/AppEventContext';
import { useGetUpicks } from '../../../../../../../hooks/apiQueryHooks/eduQueryHooks';
import { useCustomMedia } from '../../../../../../../hooks/useCustomMedia';
import { useDebounce } from '../../../../../../../hooks/useDebounce';
import { useHandleQueryParams } from '../../../../../../../hooks/useHandleQueryParams';
import { usePaginationHandler } from '../../../../../../../hooks/usePaginationHandler';
import useScrollToTop from '../../../../../../../hooks/useScrollToTop';
import { CourseCategoryList, UpickContainers } from '../../../../../../Home';
import { COURSE_SECTIONS } from '../../../../../data/constant';
import {
  useGetCourseraCourses,
  useGetMyCourses,
  useGetUdacityCourses,
  useGetUdemyCourses,
} from '../../../../../hooks/apiQueryHooks/eduQueryHooks';
import { StudentCourseTypes } from '../../../../../types';
import { MyCoursesFilter } from './ui/MyCoursesFilter';
import { StudentCourseCategoryLayout } from './ui/StudentCourseCategoryLayout';
import { StudentCourseEmptyState } from './ui/StudentCourseEmptyState';
import StudentCourseTypeCase from './ui/StudentCourseTypeCase';

export function StudentDashboardCourses() {
  const { screenSize } = useCustomMedia();
  const { scrollToTop } = useScrollToTop();
  const { query } = useHandleQueryParams();
  const { currentAccountType } = useAppContext();
  const { currentPage, handlePageChange } = usePaginationHandler();
  const [myLearningProvider, setMyLernaingProvider] =
    useState<StudentCourseTypes>(COURSE_SECTIONS.UDEMY);

  const handleMyLearningProvider = useCallback(
    (provider: StudentCourseTypes) => setMyLernaingProvider(provider),
    [myLearningProvider],
  );

  const [filteredCategoryRefList, setFilteredCategoryRefList] = useState<
    string[]
  >([]);

  const handleCategoryRef = useCallback((value: string) => {
    setFilteredCategoryRefList(previous => {
      if (previous.includes(value)) {
        const updatedArray = previous.filter(element => element !== value);
        return updatedArray;
      }
      return [...previous, value];
    });
  }, []);

  const courseType = query.get('courseType') as StudentCourseTypes;

  const keyword = useDebounce(query.get('q'));

  const isValidCourseType = useMemo(() => {
    return courseType && Object.values(COURSE_SECTIONS).includes(courseType);
  }, [courseType]);

  const pageSize =
    isValidCourseType && courseType !== COURSE_SECTIONS.ALL ? 16 : 10;

  const queryParams = useMemo(
    () => ({
      pageSize,
      page: currentPage,
      search: keyword,
      categoryRefList: filteredCategoryRefList,
    }),
    [pageSize, currentPage, keyword, filteredCategoryRefList],
  );

  const getUdemyCouresQuery = useGetUdemyCourses(queryParams, {
    enabled:
      courseType == null ||
      courseType === COURSE_SECTIONS.UDEMY ||
      courseType === COURSE_SECTIONS.ALL,
  });

  const getCourseraCoursesQuery = useGetCourseraCourses(queryParams, {
    enabled:
      courseType == null ||
      courseType === COURSE_SECTIONS.COURSERA ||
      courseType === COURSE_SECTIONS.ALL,
  });

  const getUdacityCoursesQuery = useGetUdacityCourses(queryParams, {
    enabled:
      courseType == null ||
      courseType === COURSE_SECTIONS.UDACITY ||
      courseType === COURSE_SECTIONS.ALL,
  });

  const getMyCoursesQuery = useGetMyCourses(
    { ...queryParams, provider: toUpper(myLearningProvider) },
    {
      enabled:
        courseType == null ||
        courseType === COURSE_SECTIONS.MY_LEARNING ||
        courseType !== COURSE_SECTIONS.ALL,
    },
  );

  const getUPickQuery = useGetUpicks(queryParams, {
    enabled:
      courseType == null ||
      courseType === COURSE_SECTIONS.UPICK ||
      courseType === COURSE_SECTIONS.ALL,
  });

  const currentQuery = useMemo(() => {
    switch (courseType) {
      case COURSE_SECTIONS.UDEMY: {
        return getUdemyCouresQuery;
      }
      case COURSE_SECTIONS.MY_LEARNING: {
        return getMyCoursesQuery;
      }
      case COURSE_SECTIONS.UDACITY: {
        return getUdacityCoursesQuery;
      }
      case COURSE_SECTIONS.COURSERA: {
        return getCourseraCoursesQuery;
      }
      case COURSE_SECTIONS.UPICK: {
        return getUPickQuery;
      }
      default: {
        return;
      }
    }
  }, [
    courseType,
    getMyCoursesQuery,
    getUdemyCouresQuery,
    getUdacityCoursesQuery,
    getCourseraCoursesQuery,
  ]);

  const courseList = useMemo(() => {
    switch (courseType) {
      case COURSE_SECTIONS.UDEMY: {
        return getUdemyCouresQuery?.data?.data?.UdemyCourses;
      }
      case COURSE_SECTIONS.COURSERA: {
        return getCourseraCoursesQuery?.data?.data?.CourseraCourses;
      }
      case COURSE_SECTIONS.UDACITY: {
        return getUdacityCoursesQuery?.data?.data?.UdacityCourses;
      }
      case COURSE_SECTIONS.MY_LEARNING: {
        return getMyCoursesQuery?.data?.data?.courses;
      }
      default: {
        return [];
      }
    }
  }, [
    courseType,
    getMyCoursesQuery,
    getUdemyCouresQuery,
    getCourseraCoursesQuery,
    getUdacityCoursesQuery,
  ]);

  return (
    <StudentCourseCategoryLayout
      courseType={courseType}
      isValidCourseType={isValidCourseType}
      handleCategoryRef={handleCategoryRef}
      filteredCategoryRefList={filteredCategoryRefList}
    >
      <div className="flex flex-col gap-9">
        {isValidCourseType && courseType !== COURSE_SECTIONS.ALL ? (
          <>
            <StudentCourseTypeCase
              type={courseType}
              hideButton
              isLoading={currentQuery?.isLoading}
              headerChildren={
                courseType === COURSE_SECTIONS.MY_LEARNING && (
                  <MyCoursesFilter
                    selectedProvider={myLearningProvider}
                    handleProvider={handleMyLearningProvider}
                  />
                )
              }
            >
              <div className="bg-[#FBF2EF] px-2.5 py-3">
                {courseType === COURSE_SECTIONS.UPICK ? (
                  <UpickContainers
                    courseList={getUPickQuery?.data?.data?.upick || []}
                    watchRoute={`/${currentAccountType}/dashboard-watch`}
                    className="flex-wrap overflow-visible"
                  />
                ) : (courseList || [])?.length > 0 ? (
                  <CourseCategoryList
                    courseList={courseList || []}
                    watchRoute={`/${currentAccountType}/dashboard-watch`}
                    className="flex-wrap overflow-visible"
                  />
                ) : (
                  <StudentCourseEmptyState />
                )}
              </div>
            </StudentCourseTypeCase>
            <PaginationNavigator
              isLoading={!!currentQuery?.isLoading}
              currentPage={currentPage}
              pagination={currentQuery?.data?.data?.pagination}
              handlePageChange={page => {
                handlePageChange(page);
                scrollToTop();
              }}
            />
          </>
        ) : (
          <>
            {/* {courseType !== COURSE_SECTIONS.ALL && (
              <StudentCourseTypeCase
                type={COURSE_SECTIONS.MY_LEARNING}
                isLoading={getMyCoursesQuery.isLoading}
                hideButton={
                  (getMyCoursesQuery?.data?.data?.courses || [])?.length < 1
                }
                headerChildren={
                  <MyCoursesFilter
                    selectedProvider={myLearningProvider}
                    handleProvider={handleMyLearningProvider}
                  />
                }
              >
                <div className="px-2.5 py-3 bg-[#FBF2EF]">
                  {(getMyCoursesQuery?.data?.data?.courses || [])?.length >
                  0 ? (
                    <CourseCategoryList
                      courseList={getMyCoursesQuery?.data?.data?.courses || []}
                      watchRoute={`/${currentAccountType}/dashboard-watch`}
                    />
                  ) : (
                    <StudentCourseEmptyState />
                  )}
                </div>
              </StudentCourseTypeCase>
            )} */}
            <SlideController
              dataLength={
                (getUdemyCouresQuery?.data?.data?.UdemyCourses || [])?.length
              }
              isMobile={screenSize < 768}
            >
              <StudentCourseTypeCase
                hideTypeAsText
                headerChildren={
                  <h1 className="mb-[16px] self-start text-left text-[clamp(16px,5vw,18px)] font-semibold text-blackFive max-md:max-w-full">
                    Our uPick Youtube Winners
                  </h1>
                }
                type={COURSE_SECTIONS.UPICK}
                isLoading={getUPickQuery.isLoading}
                hideButton={
                  (getUPickQuery?.data?.data?.upick || [])?.length < 1
                }
              >
                <div className="px-2.5 py-3">
                  {getUPickQuery?.data?.data?.upick || [] ? (
                    <UpickContainers
                      courseList={getUPickQuery?.data?.data?.upick || []}
                      watchRoute={`/${currentAccountType}/dashboard-watch`}
                    />
                  ) : (
                    <></>
                  )}
                </div>
              </StudentCourseTypeCase>
            </SlideController>
            {/* <SlideController
              dataLength={
                (getUdemyCouresQuery?.data?.data?.UdemyCourses || [])?.length
              }
              isMobile={screenSize < 768}
            >
              <StudentCourseTypeCase
                type={COURSE_SECTIONS.UDEMY}
                isLoading={getUdemyCouresQuery.isLoading}
                hideButton={
                  (getUdemyCouresQuery?.data?.data?.UdemyCourses || [])
                    ?.length < 1
                }
              >
                <div className="px-2.5 py-3">
                  {getUdemyCouresQuery?.data?.data?.UdemyCourses || [] ? (
                    <CourseCategoryList
                      courseList={
                        getUdemyCouresQuery?.data?.data?.UdemyCourses || []
                      }
                      watchRoute={`/${currentAccountType}/dashboard-watch`}
                    />
                  ) : (
                    <></>
                  )}
                </div>
              </StudentCourseTypeCase>
            </SlideController>
            <SlideController
              dataLength={
                (getCourseraCoursesQuery?.data?.data?.CourseraCourses || [])
                  ?.length
              }
              isMobile={screenSize < 768}
            >
              <StudentCourseTypeCase
                type={COURSE_SECTIONS.COURSERA}
                isLoading={getCourseraCoursesQuery.isLoading}
                hideButton={
                  (getCourseraCoursesQuery?.data?.data?.CourseraCourses || [])
                    ?.length < 1
                }
              >
                <div className="px-2.5 py-3">
                  {getCourseraCoursesQuery?.data?.data?.CourseraCourses ||
                  [] ? (
                    <CourseCategoryList
                      courseList={
                        getCourseraCoursesQuery?.data?.data?.CourseraCourses ||
                        []
                      }
                      watchRoute={`/${currentAccountType}/dashboard-watch`}
                    />
                  ) : (
                    <></>
                  )}
                </div>
              </StudentCourseTypeCase>
            </SlideController>
            <SlideController
              dataLength={
                (getUdacityCoursesQuery?.data?.data?.UdacityCourses || [])
                  ?.length
              }
              isMobile={screenSize < 768}
            >
              <StudentCourseTypeCase
                type={COURSE_SECTIONS.UDACITY}
                isLoading={getUdacityCoursesQuery.isLoading}
                hideButton={
                  (getUdacityCoursesQuery?.data?.data?.UdacityCourses || [])
                    ?.length < 1
                }
              >
                <div className="px-2.5 py-3 ">
                  {getUdacityCoursesQuery?.data?.data?.UdacityCourses || [] ? (
                    <CourseCategoryList
                      courseList={
                        getUdacityCoursesQuery?.data?.data?.UdacityCourses || []
                      }
                      watchRoute={`/${currentAccountType}/dashboard-watch`}
                    />
                  ) : (
                    <></>
                  )}
                </div>
              </StudentCourseTypeCase>
            </SlideController> */}
          </>
        )}
      </div>
    </StudentCourseCategoryLayout>
  );
}
