import { forwardRef, ReactNode, Ref, useState } from 'react';

import CourseCategoryCardPlaceholder from '../../../../../../../../components/ui/CategoryWidget/Courses/CourseCategoryCardPlaceholder';
import ViewMore from '../../../../../../../../components/ui/CommonWidget/ViewMore';
import { useHandleQueryParams } from '../../../../../../../../hooks/useHandleQueryParams';
import { Helper } from '../../../../../../../../utils/helpers';
import { StudentCourseTypes } from '../../../../../../types';

export default forwardRef(function StudentCourseTypeCase(
  {
    type,
    children,
    hideButton = false,
    isLoading,
    headerChildren,
    hideTypeAsText = false,
  }: {
    type: StudentCourseTypes;
    children?: ReactNode;
    hideButton?: boolean;
    isLoading?: boolean;
    hideTypeAsText?: boolean;
    headerChildren?: ReactNode;
  },
  ref: Ref<HTMLDivElement>,
) {
  const [start, setStart] = useState(false);

  const { handleQuery } = useHandleQueryParams();

  return (
    <div>
      <div className="mb-5 flex justify-between gap-2">
        <div className="flex flex-wrap gap-6">
          {!hideTypeAsText && (
            <p className="text-[20px] font-semibold text-black">
              {Helper.capitalizeString(type)}
            </p>
          )}
          {headerChildren}
        </div>
        {!hideButton && (
          <div
            onMouseEnter={() => setStart(true)}
            onMouseLeave={() => setStart(false)}
          >
            <ViewMore
              onClick={() => handleQuery({ courseType: type })}
              message="Browse All"
              start={start}
            />
          </div>
        )}
      </div>
      {isLoading ? (
        <div className="flex flex-wrap gap-3 pb-4 max-md:items-stretch">
          {Array.from({ length: 4 }, (_, index) => (
            <CourseCategoryCardPlaceholder key={index} />
          ))}
        </div>
      ) : (
        <div
          ref={ref}
          className="w-full overflow-x-auto max-md:max-w-full sm:overflow-x-hidden"
        >
          {children}
        </div>
      )}
    </div>
  );
});
