import { Helper } from '../../../../../../../../utils/helpers';
import { COURSE_SECTIONS } from '../../../../../../data/constant';
import { StudentCourseTypes } from '../../../../../../types';

export const MyCoursesFilter = ({
  selectedProvider,
  handleProvider,
}: {
  selectedProvider: StudentCourseTypes;
  handleProvider: (value: StudentCourseTypes) => void;
}) => {
  const isActive = (value: StudentCourseTypes) =>
    value === selectedProvider ? 'bg-primary text-white rounded-[3px]' : '';

  return (
    <div className="flex items-center gap-2 bg-[#BAB9B929] text-[14px] font-medium text-grayNine">
      <button
        onClick={() => handleProvider(COURSE_SECTIONS.UDEMY)}
        className={`p-1.5 ${isActive(COURSE_SECTIONS.UDEMY)}`}
      >
        {Helper.capitalizeString(COURSE_SECTIONS.UDEMY)}
      </button>{' '}
      <button
        onClick={() => handleProvider(COURSE_SECTIONS.COURSERA)}
        className={`p-1.5 ${isActive(COURSE_SECTIONS.COURSERA)}`}
      >
        {Helper.capitalizeString(COURSE_SECTIONS.COURSERA)}
      </button>{' '}
      <button
        onClick={() => handleProvider(COURSE_SECTIONS.UDACITY)}
        className={`p-1.5 ${isActive(COURSE_SECTIONS.UDACITY)}`}
      >
        {Helper.capitalizeString(COURSE_SECTIONS.UDACITY)}
      </button>
    </div>
  );
};
