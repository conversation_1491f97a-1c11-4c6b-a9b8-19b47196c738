import { useCallback, useRef, useState } from 'react';
import { Link } from 'react-router-dom';

import { noImagePlaceholder } from '../../../../../../../../assets/images';
import { Spinner } from '../../../../../../../../components/ui/CommonWidget/Loader';
import { useOnClickOutside } from '../../../../../../../../hooks/useOnClickOutside';
import { Helper } from '../../../../../../../../utils/helpers';
import { HeartIcon } from '../../../../../../assets/icons';
import { useGetStudentWishlists } from '../../../../../../hooks/apiQueryHooks/eduQueryHooks';

export const StudentWishlist = () => {
  const [isOpen, setIsOpen] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useOnClickOutside(ref, (e?: MouseEvent | TouchEvent) => {
    const target = e?.target as Element;
    if (target?.id && target.id === 'wishlist-icon') return;
    setIsOpen(false);
  });

  const { data, isLoading } = useGetStudentWishlists();

  const courses = data?.data?.courses || [];

  const toggle = useCallback(() => setIsOpen(previous => !previous), []);
  return (
    <div className="group relative">
      <HeartIcon
        height={30}
        width={30}
        id="wishlist-icon"
        className={`cursor-pointer ${isOpen ? 'stroke-primary' : ''}`}
        onClick={toggle}
      />
      {!isOpen && (
        <span className="absolute top-10 scale-0 rounded bg-primary p-2 text-xs text-white transition-all group-hover:scale-100">
          Wishlist
        </span>
      )}

      {isOpen && (
        <div
          ref={ref}
          className="absolute left-0 top-full z-10 w-[303px] md:right-0"
        >
          <div className="flex max-h-[303px] w-full flex-col items-center gap-6 overflow-y-auto bg-[#fff] px-5 py-6 text-[14px] ">
            {isLoading ? (
              <Spinner className="" />
            ) : courses.length > 0 ? (
              courses.map(course => (
                <Link
                  to={`/student/dashboard-watch?c_ref=${course.courseRef}&provider=${course.courseProvider}&system_category_ref=course`}
                  key={course.courseRef}
                >
                  <div className="flex h-[45px] w-full max-w-[260px] items-center gap-1.5">
                    <img
                      src={course?.courseImageUrl || noImagePlaceholder}
                      srcSet={course?.courseImageSrcSet || ''}
                      className="h-full w-[58px] object-cover"
                    />
                    <p className="font-medium leading-[21px] text-black">
                      {Helper.truncateText(course?.title, 42)}
                    </p>
                  </div>
                </Link>
              ))
            ) : (
              <>
                <p className="font-medium text-grayThirteen">
                  Your wishlist is empty!
                </p>
                <Link
                  to="/student/dashboard-courses-learn"
                  className="text-[18px] text-primary underline"
                  onClick={toggle}
                >
                  Explore courses
                </Link>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
