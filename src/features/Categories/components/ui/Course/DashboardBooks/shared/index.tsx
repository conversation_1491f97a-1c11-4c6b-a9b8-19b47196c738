import { use<PERSON>allback, useMemo, useState } from 'react';

import Slide<PERSON>ontroller from '../../../../../../../components/ui/CommonWidget/SlideController';
import PaginationNavigator from '../../../../../../../components/ui/PaginationNavigator/PaginationNavigator';
import { useAppContext } from '../../../../../../../context/event/AppEventContext';
import { useGetBooks } from '../../../../../../../hooks/apiQueryHooks/eduQueryHooks';
import { useCustomMedia } from '../../../../../../../hooks/useCustomMedia';
import { useDebounce } from '../../../../../../../hooks/useDebounce';
import { useHandleQueryParams } from '../../../../../../../hooks/useHandleQueryParams';
import { usePaginationHandler } from '../../../../../../../hooks/usePaginationHandler';
import useScrollToTop from '../../../../../../../hooks/useScrollToTop';
import { COURSE_SECTIONS } from '../../../../../data/constant';

import { StudentCourseTypes } from '../../../../../types';
import { StudentBooksCategoryLayout } from './ui/StudentBooksCategoryLayout';
import { BookContainers } from '@/features/Home/components/ui/BookCategory/BookContainers';
import StudentBooksTypeCase from './ui/StudentBooksTypeCase';

export function SharedDashboardBooks() {
  const { screenSize } = useCustomMedia();
  const { scrollToTop } = useScrollToTop();
  const { query } = useHandleQueryParams();
  const { currentAccountType } = useAppContext();
  const { currentPage, handlePageChange } = usePaginationHandler();

  const [filteredCategoryRefList, setFilteredCategoryRefList] = useState<
    string[]
  >([]);

  const handleCategoryRef = useCallback((value: string) => {
    setFilteredCategoryRefList(previous => {
      if (previous.includes(value)) {
        const updatedArray = previous.filter(element => element !== value);
        return updatedArray;
      }
      return [...previous, value];
    });
  }, []);

  const courseType = query.get('courseType') as StudentCourseTypes;

  const keyword = useDebounce(query.get('q'));

  const isValidCourseType = useMemo(() => {
    return courseType && Object.values(COURSE_SECTIONS).includes(courseType);
  }, [courseType]);

  const pageSize =
    isValidCourseType && courseType !== COURSE_SECTIONS.ALL ? 16 : 10;

  const queryParams = useMemo(
    () => ({
      pageSize,
      page: currentPage,
      search: keyword,
      categoryRefs: filteredCategoryRefList,
    }),
    [pageSize, currentPage, keyword, filteredCategoryRefList],
  );

  const { data: getBookData, isLoading: isBookLoading } =
    useGetBooks(queryParams);

  return (
    <StudentBooksCategoryLayout
      courseType={courseType}
      isValidCourseType={isValidCourseType}
      handleCategoryRef={handleCategoryRef}
      filteredCategoryRefList={filteredCategoryRefList}
    >
      <div className="flex flex-col gap-9">
        {isValidCourseType && courseType !== COURSE_SECTIONS.ALL ? (
          <>
            <StudentBooksTypeCase
              type={courseType}
              hideButton
              isLoading={isBookLoading}
            >
              <div className="bg-[#FBF2EF] px-2.5 py-3">
                <BookContainers
                  bookList={getBookData?.data?.books || []}
                  watchRoute={`/${currentAccountType}/dashboard-read`}
                  className="flex-wrap overflow-visible"
                />
              </div>
            </StudentBooksTypeCase>
            <PaginationNavigator
              isLoading={!!isBookLoading}
              currentPage={currentPage}
              pagination={getBookData?.data?.pagination}
              handlePageChange={page => {
                handlePageChange(page);
                scrollToTop();
              }}
            />
          </>
        ) : (
          <SlideController
            dataLength={(getBookData?.data?.books || [])?.length}
            isMobile={screenSize < 768}
          >
            <StudentBooksTypeCase
              hideTypeAsText
              headerChildren={
                <h1 className="mb-[16px] self-start text-left text-[clamp(16px,5vw,18px)] font-semibold text-blackFive max-md:max-w-full">
                  uBooks
                </h1>
              }
              type={COURSE_SECTIONS.UBOOK}
              isLoading={isBookLoading}
              hideButton={(getBookData?.data?.books || [])?.length < 1}
            >
              <div className="px-2.5 py-3">
                {getBookData?.data?.books || [] ? (
                  <BookContainers
                    bookList={getBookData?.data?.books || []}
                    watchRoute={`/${currentAccountType}/dashboard-read`}
                  />
                ) : (
                  <></>
                )}
              </div>
            </StudentBooksTypeCase>
          </SlideController>
        )}
      </div>
    </StudentBooksCategoryLayout>
  );
}
