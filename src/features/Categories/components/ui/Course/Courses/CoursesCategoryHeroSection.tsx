import { useNavigate } from 'react-router-dom';

import Button from '../../../../../../components/ui/ButtonComponent';
import LazyLoadImageContainer from '../../../../../../components/ui/CommonWidget/LazyLoadImageContainer';
import { useUserContext } from '../../../../../../context/user/UserContext';
import useLazyLoadingHandler from '../../../../../../hooks/useLazyLoadingHandler';
import { learnImageBig } from '../../../../assets/images';

export default function CoursesCategoryHeroSection() {
  const navigate = useNavigate();
  const { imgRefs, loaded } = useLazyLoadingHandler();
  const { isLoggedIn } = useUserContext();
  return (
    <section className="container_max_width">
      <div className="mt-[96px] bg-[#FFF5F0] px-5 lg:px-28">
        <div className="flex gap-5 max-md:flex-col">
          <article className="max-w-[689px] self-center py-[45px] pr-5 max-md:w-full xs:pt-[90px]">
            <div className="flex grow flex-col max-md:max-w-full">
              <h1 className="max-w-[648px] text-[clamp(24px,7vw,40px)] font-bold sm:leading-[60px]">
                Elevate & Innovate:
                <br />A Knowledge & Skills Guide
              </h1>
              <p className="mt-4 text-[clamp(14px,5vw,18px)] font-[400] text-subText">
                Navigate technology learning with online videos and our expertly
                curated 12-chapter guidelines. From emerging trends to practical
                solutions, these videos and guidelines are your key to
                understanding and mastering the digital and innovation landscape
                and acquiring future skills.
              </p>
              {!isLoggedIn && (
                <Button className="mt-[32px] w-full max-w-[181px] bg-primary   text-white">
                  <p onClick={() => navigate('/signup')}>Get Started</p>
                </Button>
              )}
            </div>
          </article>
          <article className="flex flex-1 justify-center md:justify-end ">
            <div className="relative aspect-[0.95] w-[450px]">
              <LazyLoadImageContainer
                lazyBackgroundImage={''}
                loaded={loaded}
                className="absolute z-[2] m-auto h-full w-full"
              >
                <img
                  ref={element => (imgRefs.current[0] = element!)}
                  loading="lazy"
                  src={learnImageBig}
                  className={`z-[2] h-full w-full object-contain object-center ${
                    loaded ? 'opacity-100' : 'opacity-0'
                  } duration-500 ease-in`}
                  alt="Image description"
                />
              </LazyLoadImageContainer>
            </div>
          </article>
        </div>
      </div>
    </section>
  );
}
