import { useSearchParams } from 'react-router-dom';

import { RectangleThreeImage } from '../../../../../../assets/images';
// import { TopCategorySection } from "./TopCategorySection/TopCategorySection";
import SlideController from '../../../../../../components/ui/CommonWidget/SlideController';
import {
  useGetBooks,
  useGetUpicks,
} from '../../../../../../hooks/apiQueryHooks/eduQueryHooks';
import { useCustomMedia } from '../../../../../../hooks/useCustomMedia';
import { CourseCategoryCase, UpickContainers } from '../../../../../Home';
import CoursesCategoryHeroSection from './CoursesCategoryHeroSection';
import { BookContainers } from '@/features/Home/components/ui/BookCategory/BookContainers';

export default function Courses() {
  const { screenSize } = useCustomMedia();
  const [searchParams] = useSearchParams();
  // const currentUdemyCourseCategory = searchParams.get("cc_udemy");
  // const currentUdacityCourseCategory = searchParams.get("cc_udacity");
  // const currentCourseraCourseCategory = searchParams.get("cc_coursera");
  const currentUPickCourseCategory = searchParams.get('c_upick');
  const currentBookCourseCategory = searchParams.get('c_books');

  // const { data: udemyData, isLoading: isUdemyLoading } = useGetUdemyCourses({
  //   pageSize: 10,
  //   categoryRefList: [currentUdemyCourseCategory || ""],
  // });

  // const { data: courseraData, isLoading: isCourseraLoading } =
  //   useGetCourseraCourses({
  //     pageSize: 10,
  //     categoryRefList: [currentCourseraCourseCategory || ""],
  //   });

  // const { data: udacityData, isLoading: isUdacityLoading } =
  //   useGetUdacityCourses({
  //     pageSize: 10,
  //     categoryRefList: [currentUdacityCourseCategory || ""],
  //   });

  const { data: getUPickData, isLoading: isUpickLoading } = useGetUpicks({
    pageSize: 10,
    categoryRefList: [currentUPickCourseCategory || ''],
  });

  const { data: getBookData, isLoading: isBookLoading } = useGetBooks({
    pageSize: 10,
    categoryRefs: [currentBookCourseCategory || ''],
  });

  return (
    <section className="bg-white">
      <CoursesCategoryHeroSection />
      <section
        style={{
          backgroundImage: `url(${RectangleThreeImage})`,
          backgroundPosition: 'center -105%',
        }}
        className="section-x-padding-alt mx-auto max-w-full overflow-hidden bg-[#fafafa] bg-cover bg-no-repeat pt-[91px]"
      >
        <div className="container_max_width">
          {/* <TopCategorySection /> */}
          <SlideController
            dataLength={getUPickData?.data?.upick?.length}
            isMobile={screenSize < 768}
          >
            <CourseCategoryCase
              hideTypeAsText
              headerChildren={
                <h1 className="mb-4 w-full self-center text-center text-[clamp(16px,5vw,24px)] font-semibold text-blackFive max-md:max-w-full md:mb-10">
                  Our uPick Youtube Winners
                </h1>
              }
              viewAllCoursesRoute="/courses-learn/filter?courseType=uPick"
              tabQueryName={`c_upick`}
              thirdPartyLogo={''}
              isLoading={isUpickLoading}
            >
              <UpickContainers
                courseList={getUPickData?.data?.upick || []}
                watchRoute={`/watch`}
              />
            </CourseCategoryCase>
          </SlideController>
          <SlideController
            dataLength={getBookData?.data?.books?.length}
            isMobile={screenSize < 768}
          >
            <CourseCategoryCase
              hideTypeAsText
              headerChildren={
                <div className="mb-4 w-full self-center text-center md:mb-10">
                  <h1 className="mb-4 text-[clamp(16px,5vw,24px)] font-semibold text-blackFive max-md:max-w-full">
                    Explore <span className="text-primary">uBooks</span>
                  </h1>
                  <p className="text-[16px] font-normal text-subText">
                    Uncover our wide selection of guidelines to build enduring
                    skills for the future.
                  </p>
                </div>
              }
              viewAllCoursesRoute="/courses-learn/filter?courseType=uBook"
              tabQueryName={`c_books`}
              thirdPartyLogo={''}
              isLoading={isBookLoading}
            >
              <BookContainers
                bookList={getBookData?.data?.books || []}
                watchRoute={`/read`}
              />
            </CourseCategoryCase>
          </SlideController>

          {/* <SlideController
            dataLength={udemyData?.data?.UdemyCourses.length}
            isMobile={screenSize < 768}
          >
            <CourseCategoryCase
              viewAllCoursesRoute="/courses-learn/filter?courseType=Udemy"
              tabQueryName={`cc_udemy`}
              thirdPartyLogo={udemyLogo}
              isLoading={isUdemyLoading}
            >
              <CourseCategoryList
                watchRoute={`/watch`}
                courseList={udemyData?.data?.UdemyCourses || []}
              />
            </CourseCategoryCase>
          </SlideController> */}
          {/* <SlideController
            dataLength={courseraData?.data?.CourseraCourses.length}
            isMobile={screenSize < 768}
          >
            <CourseCategoryCase
              viewAllCoursesRoute="/courses-learn/filter?courseType=Coursera"
              tabQueryName={`cc_coursera`}
              thirdPartyLogo={courseraLogo}
              isLoading={isCourseraLoading}
            >
              <CourseCategoryList
                watchRoute={`/watch`}
                courseList={courseraData?.data?.CourseraCourses || []}
              />
            </CourseCategoryCase>
          </SlideController>
          <SlideController
            dataLength={udacityData?.data?.UdacityCourses.length}
            isMobile={screenSize < 768}
          >
            <CourseCategoryCase
              viewAllCoursesRoute="/courses-learn/filter?courseType=Udacity"
              tabQueryName={`cc_udacity`}
              thirdPartyLogo={udacityLogo}
              isLoading={isUdacityLoading}
            >
              <CourseCategoryList
                watchRoute={`/watch`}
                courseList={udacityData?.data?.UdacityCourses || []}
              />
            </CourseCategoryCase>
          </SlideController> */}
        </div>
      </section>
    </section>
  );
}
