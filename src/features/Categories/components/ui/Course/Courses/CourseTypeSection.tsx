import { forwardRef, ReactNode, Ref, useMemo, useState } from 'react'
import { useNavigate } from 'react-router-dom'

import { Spinner } from '../../../../../../components/ui/CommonWidget/Loader'
import ViewMore from '../../../../../../components/ui/CommonWidget/ViewMore'
import { CourseIcons } from '../../../../data/data'

/* eslint-disable react/display-name */
export const CourseTypeSection = forwardRef(
  (
    {
      type,
      children,
      isLoading,
    }: {
      type: keyof typeof CourseIcons
      children: ReactNode
      isLoading?: boolean
    },
    ref: Ref<HTMLDivElement>
  ) => {
    const [start, setStart] = useState(false)
    const navigate = useNavigate()

    const CourseIcon = useMemo(() => CourseIcons[type], [])

    if (isLoading)
      return (
        <div className="flex items-center justify-center h-[200px] w-full">
          <Spinner className="" />
        </div>
      )

    return (
      <div className="flex flex-col pb-[37px]">
        <div className="py-2 flex items-center justify-between mb-0 lg:mb-[8px]">
          <p className="capitalize font-semibold text-[24px] hidden md:block">
            Courses
          </p>
          <CourseIcon />
        </div>
        <div
          ref={ref}
          className="overflow-x-auto sm:overflow-x-hidden w-full max-md:max-w-full"
        >
          {children}
        </div>
        <div
          onMouseEnter={() => setStart(true)}
          onMouseLeave={() => setStart(false)}
          className="mt-[24px] px-6 py-2 bg-white w-fit"
        >
          <ViewMore
            onClick={() => navigate(`/courses-learn/filter?courseType=${type}`)}
            message="Browse All Courses"
            start={start}
          />
        </div>
      </div>
    )
  }
)
