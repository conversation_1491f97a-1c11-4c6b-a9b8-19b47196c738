import { Spinner } from '../../../../../../../components/ui/CommonWidget/Loader';
import { useGetAllCourseCategoriesCount } from '../../../../../../../hooks/apiQueryHooks/eduQueryHooks';
import useCreateListForCourses from '../../../../../../../hooks/useCreateListForCourses';
import BrowseCourseCategoryContainer from '../../../../../../Home/components/ui/BrowseCourseCategory/BrowseCourseCategoryContainer';

export const TopCategoryContainer = () => {
  const { data, isLoading } = useGetAllCourseCategoriesCount({
    staleTime: 1000 * 60 * 60 * 24,
    cacheTime: 1000 * 60 * 60 * 25,
  });
  if (isLoading)
    return (
      <div className="flex h-[250px] w-full items-center justify-center">
        <Spinner className="" />
      </div>
    );
  return (
    <div className="flex w-full flex-col  items-stretch pb-[56px] sm:pb-[80px] md:gap-y-12">
      <BrowseCourseCategoryContainer
        courseCategoryGroup={
          useCreateListForCourses(
            data?.data?.courseCategoryCountList || [],
          )?.slice(0, 4) || []
        }
      />
      <BrowseCourseCategoryContainer
        courseCategoryGroup={
          useCreateListForCourses(
            data?.data?.courseCategoryCountList || [],
          )?.slice(4, 8) || []
        }
      />
    </div>
  );
};
