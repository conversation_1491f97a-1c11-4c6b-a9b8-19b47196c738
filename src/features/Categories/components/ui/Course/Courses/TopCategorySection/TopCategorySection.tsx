import {
  CourseraIcon,
  UdacityIcon,
  UdemyIcon,
} from '../../../../../../../assets/icons'
import { TopCategoryContainer } from './TopCategoryContainer'

export const TopCategorySection = () => {
  return (
    <div className="flex flex-col py-[40px] sm:pt-[80px] sm:pb-[58px] mx-auto w-full max-w-[1188px]">
      <p className="text-center text-[40px] font-semibold mb-4">
        Browse <span className="text-primary">Top</span> Category
      </p>
      <p className="text-center text-[20px] leading-[30px] mb-6">
        Explore our diverse array of courses and cultivate a skill that will
        endure a lifetime.
      </p>
      <TopCategoryContainer />
      <p className="text-primary text-center font-bold text-[24px] leading-10 pb-[17px]">
        Our Partners
      </p>
      <div className="flex justify-center gap-4 lg:gap-6 flex-wrap">
        <div className="w-[calc(50%-16px)] md:w-auto bg-[#fff] py-4 md:py-1 px-4 h-auto md:h-[32px]">
          <UdacityIcon width="auto" height={24} />
        </div>
        <div className="w-[calc(50%-16px)] md:w-auto bg-[#fff] py-4 md:py-0 px-4 h-auto md:h-[32px]">
          <UdemyIcon width="auto" height={29} />
        </div>
        <div className="w-[calc(50%-16px)] md:w-auto bg-[#fff] py-5 md:py-[7px] px-4 h-auto md:h-[32px]">
          <CourseraIcon width="auto" height={17.92} />
        </div>
      </div>
    </div>
  )
}
