import { ReactNode } from 'react';

import CourseCategoryCardPlaceholder from '../../../../../../components/ui/CategoryWidget/Courses/CourseCategoryCardPlaceholder';

export const CourseCategoryTypeCase = ({
  icon: Icon,
  children,
  isLoading,
}: {
  icon?: React.FunctionComponent<React.SVGProps<SVGSVGElement>>;
  children?: ReactNode;
  isLoading?: boolean;
}) => {
  return (
    <>
      {Icon && (
        <div className="mb-4 bg-grayTwo px-8 py-2 max-sm:px-5">uPick</div>
      )}
      {isLoading ? (
        <div className="flex flex-wrap gap-3 pb-4 max-md:items-stretch">
          {Array.from({ length: 10 }, (_, index) => (
            <CourseCategoryCardPlaceholder key={index} />
          ))}
        </div>
      ) : (
        children
      )}
    </>
  );
};
