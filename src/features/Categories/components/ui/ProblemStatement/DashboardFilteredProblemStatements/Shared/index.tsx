import { useCallback, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import ProblemStatementCategoryList from '../../../../../../../components/ui/CategoryWidget/ProblemStatement/ProblemStatementCategoryList';
import { useAppContext } from '../../../../../../../context/event/AppEventContext';
import { useGetProblemStatements } from '../../../../../../../hooks/apiQueryHooks/eduQueryHooks';
import { usePaginationHandler } from '../../../../../../../hooks/usePaginationHandler';
import DashboardProblemStatementCategoryLayout from '../../../../../layout/DashboardProblemStatementCategoryLayout';
import { ProblemStatementEmptyState } from '../../ProblemStatementEmptyState';
import ProblemStatementFilterSectionCase from '../../ProblemStatements/ProblemStatementFilterSectionCase';

export function SharedDashboardFilteredProblemStatements() {
  // TODO: refactor to reduce prop drilling. use context api
  const navigate = useNavigate();
  const { currentPage } = usePaginationHandler();
  const { currentAccountType } = useAppContext();
  const [filter, setFilter] = useState('');
  const [searchValue, setSearchValue] = useState('');
  const onChange = (value: string) => {
    setFilter(value);
    navigate(`?q=${value}`);
  };
  const setSearchValueHandler = useCallback((value: string) => {
    setSearchValue(value);
  }, []);
  const { data, isLoading } = useGetProblemStatements({
    sortBy: filter,
    search: searchValue,
    page: currentPage,
  });
  return (
    <DashboardProblemStatementCategoryLayout
      setSearchValue={setSearchValueHandler}
      onChange={onChange}
    >
      <div className="flex-1 max-sm:px-5">
        <ProblemStatementFilterSectionCase
          pagination={data?.data?.pagination}
          isLoading={isLoading}
        >
          {(data?.data?.problem_statement || [])?.length > 0 ? (
            <ProblemStatementCategoryList
              categoryList={data?.data?.problem_statement || []}
              watchRoute={`/${currentAccountType}/dashboard-watch`}
              isAddProjectStateVariant={true}
              className="flex-wrap"
            />
          ) : (
            <ProblemStatementEmptyState title="Problem Statement" />
          )}
        </ProblemStatementFilterSectionCase>
      </div>
    </DashboardProblemStatementCategoryLayout>
  );
}
