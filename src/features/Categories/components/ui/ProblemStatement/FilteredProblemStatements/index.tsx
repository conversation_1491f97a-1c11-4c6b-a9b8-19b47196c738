import { useCallback, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import ProblemStatementCategoryList from '../../../../../../components/ui/CategoryWidget/ProblemStatement/ProblemStatementCategoryList';
import { useGetProblemStatements } from '../../../../../../hooks/apiQueryHooks/eduQueryHooks';
import { usePaginationHandler } from '../../../../../../hooks/usePaginationHandler';
import ProblemStatementCategoryLayout from '../../../../layout/ProblemStatementCategoryLayout';
import { ProblemStatementEmptyState } from '../ProblemStatementEmptyState';
import ProblemStatementFilterSectionCase from '../ProblemStatements/ProblemStatementFilterSectionCase';
import { useAppContext } from '@/context/event/AppEventContext';

export default function FilteredProblemStatements() {
  // TODO: refactor to reduce prop drilling. use context api
  const { filters } = useAppContext();
  const { currentPage } = usePaginationHandler();
  const navigate = useNavigate();
  const [filter, setFilter] = useState('');
  const [searchValue, setSearchValue] = useState('');
  const onChange = useCallback((value: string) => {
    setFilter(value);
    navigate(`?q=${value.toLowerCase()}`);
  }, []);
  const setSearchValueHandler = useCallback((value: string) => {
    setSearchValue(value);
  }, []);
  const { data, isLoading } = useGetProblemStatements({
    sortBy: filter,
    subCategoryRefList: filters,
    search: searchValue,
    page: currentPage,
  });
  return (
    <ProblemStatementCategoryLayout
      onChange={onChange}
      setSearchValue={setSearchValueHandler}
    >
      <div className="flex-1 max-sm:px-5 sm:pl-4 sm:pr-8">
        <ProblemStatementFilterSectionCase
          pagination={data?.data?.pagination}
          isLoading={isLoading}
        >
          {(data?.data?.problem_statement || [])?.length > 0 ? (
            <ProblemStatementCategoryList
              categoryList={data?.data?.problem_statement || []}
              watchRoute={`/watch`}
              className="flex-wrap"
            />
          ) : (
            <ProblemStatementEmptyState title="Problem Statement" />
          )}
        </ProblemStatementFilterSectionCase>
      </div>
    </ProblemStatementCategoryLayout>
  );
}
