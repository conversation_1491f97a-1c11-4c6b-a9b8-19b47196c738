import { useCallback, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import ProblemStatementCategoryList from '../../../../../../../components/ui/CategoryWidget/ProblemStatement/ProblemStatementCategoryList';
import { useAppContext } from '../../../../../../../context/event/AppEventContext';
import {
  useGetProblemStatements,
  useGetSDGs,
} from '../../../../../../../hooks/apiQueryHooks/eduQueryHooks';
import { usePaginationHandler } from '../../../../../../../hooks/usePaginationHandler';
import { ProblemStatementCategoryNameType } from '../../../../../../../types';
import { Helper } from '../../../../../../../utils/helpers';
import ProjectCategoryLayout from '../../../../../layout/ProjectCategoryLayout';
import { ProblemStatementEmptyState } from '../../ProblemStatementEmptyState';
import ProblemStatementFilterSectionCase from '../../ProblemStatements/ProblemStatementFilterSectionCase';

export function SharedChooseProblemStatementForProject() {
  // TODO: refactor to reduce prop drilling. use context api
  const { currentAccountType } = useAppContext();
  const [searchValue, setSearchValue] = useState('');
  const { currentPage } = usePaginationHandler();
  const {
    data: probleStatementCategoriesResponse,
    isLoading: isLoadingCategories,
  } = useGetSDGs();
  const navigate = useNavigate();
  const [filter, setFilter] = useState<ProblemStatementCategoryNameType | ''>(
    '',
  );
  const setSearchValueHandler = useCallback((value: string) => {
    setSearchValue(value);
  }, []);
  const onChange = (value: ProblemStatementCategoryNameType) => {
    setFilter(value);
    navigate(`?q=${value}`);
  };
  const sdgFilter = Helper.getCategoryRef(
    probleStatementCategoriesResponse,
    filter,
  );
  const { data, isLoading } = useGetProblemStatements({
    categoryRef: sdgFilter ? [sdgFilter] : [],
    search: searchValue,
    page: currentPage,
  });
  return (
    <ProjectCategoryLayout
      setSearchValue={setSearchValueHandler}
      onChange={onChange}
    >
      <ProblemStatementFilterSectionCase
        pagination={data?.data?.pagination}
        isLoading={isLoading || isLoadingCategories}
      >
        {(data?.data?.problem_statement || [])?.length > 0 ? (
          <ProblemStatementCategoryList
            categoryList={data?.data?.problem_statement || []}
            watchRoute={`/${currentAccountType}/dashboard-watch`}
            isAddProjectStateVariant={true}
            className="flex-wrap"
          />
        ) : (
          <ProblemStatementEmptyState title="Problem Statement" />
        )}
      </ProblemStatementFilterSectionCase>
    </ProjectCategoryLayout>
  );
}
