import { useNavigate } from 'react-router-dom'

import CommonDashedBorderBoxWithActions from '../../../../../components/ui/CommonWidget/CommonDashedBorderBoxWithActions'
import { useAppContext } from '../../../../../context/event/AppEventContext'

type Props = {
  title: string
}

export function ProblemStatementEmptyState({ title }: Props) {
  const { currentAccountType } = useAppContext()
  const navigate = useNavigate()

  const redirect = () =>
    navigate(
      `/${currentAccountType}/dashboard-statements/add-problem-statement`
    )

  return (
    <div className="">
      <h3 className="text-stone-950 text-base font-medium leading-8 mb-[14px]">
        {title}
      </h3>
      <CommonDashedBorderBoxWithActions
        buttonText="Add A Problem Statement"
        message="No statements added yet"
        buttonAction={redirect}
      />
    </div>
  )
}
