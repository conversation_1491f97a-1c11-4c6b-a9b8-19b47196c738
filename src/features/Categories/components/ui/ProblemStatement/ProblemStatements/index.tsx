import bgImage from '../../../../../../assets/images/rectangleTwo.png'
import AllProblemSection from './AllProblemSection'
import ProblemStatementCategoryHeroSection from './ProblemStatementCategoryHeroSection'
import RecentlyAddedCategorySection from './RecentlyAddedCategorySection'
export default function ProblemStatements() {
  return (
    <section className="">
      <ProblemStatementCategoryHeroSection />
      <section
        style={{ backgroundImage: `url(${bgImage})` }}
        className={`
      overflow-hidden mx-auto max-w-full bg-cover pb-16 sm:pb-20 section-x-padding-alt `}
      >
        <div className="container_max_width">
          <RecentlyAddedCategorySection />
          <AllProblemSection />
        </div>
      </section>
    </section>
  )
}
