import ProblemStatementCategoryCase from '../../../../../../components/ui/CategoryWidget/ProblemStatement/ProblemStatementCategoryCase';
import ProblemStatementCategoryList from '../../../../../../components/ui/CategoryWidget/ProblemStatement/ProblemStatementCategoryList';
import SlideController from '../../../../../../components/ui/CommonWidget/SlideController';
import { categoryByStatus } from '../../../../../../data/constants';
import { useGetProblemStatements } from '../../../../../../hooks/apiQueryHooks/eduQueryHooks';
import { useCustomMedia } from '../../../../../../hooks/useCustomMedia';
import { useHandleQueryParams } from '../../../../../../hooks/useHandleQueryParams';
import { usePaginationHandler } from '../../../../../../hooks/usePaginationHandler';

export default function RecentlyAddedCategorySection() {
  const { query } = useHandleQueryParams();
  const sdgFilter = query.get('s_d_g_recent') || '';
  const { currentPage } = usePaginationHandler();
  const { screenSize } = useCustomMedia();
  const { data, isLoading } = useGetProblemStatements({
    sortBy: categoryByStatus.recentlyAdded,
    page: currentPage,
    categoryRef: sdgFilter ? [sdgFilter] : [],
  });
  return (
    <>
      <h1 className="mb-[16px] px-4 text-[clamp(20px,5vw,24px)] font-semibold capitalize text-primary sm:px-8">
        Recently added{' '}
        <span className="text-blackFive">problem statements</span>
      </h1>
      <SlideController
        dataLength={data?.data?.problem_statement.length}
        isMobile={screenSize < 768}
      >
        <ProblemStatementCategoryCase
          isLoading={isLoading}
          viewAllStatementRoute={`filter?q=${categoryByStatus.recentlyAdded.toLocaleLowerCase()}`}
          tabQueryName="s_d_g_recent"
        >
          <ProblemStatementCategoryList
            categoryList={data?.data?.problem_statement || []}
            watchRoute={`/watch`}
          />
        </ProblemStatementCategoryCase>
      </SlideController>
    </>
  );
}
