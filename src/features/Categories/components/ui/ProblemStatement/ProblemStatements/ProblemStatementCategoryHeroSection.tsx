import { useNavigate } from 'react-router-dom';

import Button from '../../../../../../components/ui/ButtonComponent';
import LazyLoadImageContainer from '../../../../../../components/ui/CommonWidget/LazyLoadImageContainer';
import { useUserContext } from '../../../../../../context/user/UserContext';
import useLazyLoadingHandler from '../../../../../../hooks/useLazyLoadingHandler';
import { categoryHeroImageBig, u } from '../../../../assets/images';
import categoryheroImageSmall from '../../../../assets/images/categoryheroImageSmall.png';

export default function ProblemStatementCategoryHeroSection() {
  const navigate = useNavigate();
  const { imgRefs, loaded } = useLazyLoadingHandler();
  const { isLoggedIn } = useUserContext();
  return (
    <section className="container_max_width">
      <div className="section-x-padding mt-[80px] pb-[90px]">
        <div className="flex gap-5 max-lg:flex-col">
          <article className="max-w-[689px] self-center py-[45px] pr-5 max-md:w-full xs:py-[90px]">
            <div className="flex grow flex-col max-md:max-w-full">
              <h1 className="text-[clamp(24px,7vw,37px)] font-bold sm:leading-[70px]">
                Decoding Dilemmas: Where Challenges Meet Transformative
                Solutions
              </h1>
              <p className="mt-4 text-[clamp(14px,5vw,24px)] font-[400] text-subText">
                Dive into problem statement videos and catalyze change by
                forming school project teams to tackle{' '}
                <br className="max-md:hidden" />
                real-world challenges head-on.
              </p>
              {!isLoggedIn && (
                <Button className="mt-[32px] w-full max-w-[181px] bg-primary   text-white">
                  <p onClick={() => navigate('/signup')}>Get Started</p>
                </Button>
              )}
            </div>
          </article>
          <article className="flex flex-1 justify-center lg:justify-end ">
            <div className="relative aspect-[0.82] w-[482px]">
              <img
                loading="lazy"
                src={u}
                className={`h-[80%]" alt="Image description absolute bottom-[50%] right-0 z-[1] w-[60%] translate-y-[75%]
                object-contain object-center ${
                  loaded ? 'opacity-100' : 'opacity-0'
                } duration-500 ease-in`}
              />
              <LazyLoadImageContainer
                lazyBackgroundImage={categoryheroImageSmall}
                loaded={loaded}
                className="absolute z-[2] h-full w-full"
              >
                <img
                  ref={element => (imgRefs.current[0] = element!)}
                  loading="lazy"
                  src={categoryHeroImageBig}
                  className={`absolute top-0 z-[2] h-full w-full object-contain object-center ${
                    loaded ? 'opacity-100' : 'opacity-0'
                  } duration-500 ease-in`}
                  alt="Image description"
                />
              </LazyLoadImageContainer>
            </div>
          </article>
        </div>
      </div>
    </section>
  );
}
