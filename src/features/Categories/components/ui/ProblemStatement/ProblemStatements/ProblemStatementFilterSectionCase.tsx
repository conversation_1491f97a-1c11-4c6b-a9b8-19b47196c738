import { forwardRef, ReactNode, Ref, useState } from 'react'
import { Link } from 'react-router-dom'

import ProblemStatementCategoryCardPlaceholder from '../../../../../../components/ui/CategoryWidget/ProblemStatement/ProblemStatementCategoryCardPlaceholder'
import ViewMore from '../../../../../../components/ui/CommonWidget/ViewMore'
import PaginationNavigator from '../../../../../../components/ui/PaginationNavigator/PaginationNavigator'
import { usePaginationHandler } from '../../../../../../hooks/usePaginationHandler'
import { PaginationType } from '../../../../../../types'

type Props = {
  descriptionText?: ReactNode
  needViewMore?: boolean
  route?: string
  children: ReactNode
  isLoading: boolean
  pagination?: PaginationType
  noPagination?: boolean
}

export default forwardRef(function ProblemStatementFilterSectionCase(
  {
    descriptionText,
    needViewMore,
    noPagination = false,
    route,
    children,
    isLoading,
    pagination,
  }: Props,
  ref: Ref<HTMLDivElement>
) {
  const [start, setStart] = useState(false)
  const { currentPage, handlePageChange } = usePaginationHandler()
  return (
    <section
      onMouseEnter={() => setStart(true)}
      onMouseLeave={() => setStart(false)}
      className="items-stretch flex flex-col mb-12"
    >
      <div className="flex justify-between items-center gap-x-2 gap-y-8 flex-wrap-reverse">
        {descriptionText}
        {needViewMore && (
          <Link className="flex" to={route || ''}>
            <ViewMore start={start} />
          </Link>
        )}
      </div>
      <div
        ref={ref}
        className="w-full max-md:max-w-full overflow-x-auto sm:overflow-x-hidden"
      >
        {isLoading ? (
          <div className="gap-3 flex max-md:items-stretch pb-4 flex-wrap">
            {Array.from({ length: 8 }, (_, index) => (
              <ProblemStatementCategoryCardPlaceholder key={index} />
            ))}
          </div>
        ) : (
          children
        )}
      </div>
      {!noPagination && (
        <div className="mt-12">
          <PaginationNavigator
            isLoading={isLoading}
            currentPage={currentPage}
            pagination={pagination}
            handlePageChange={(page) => handlePageChange(page)}
          />
        </div>
      )}
    </section>
  )
})
