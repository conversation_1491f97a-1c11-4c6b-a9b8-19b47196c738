import { useNavigate, useParams } from 'react-router-dom';

import { ArrowBackIcon } from '../../../../../../../assets/icons';
import { useAppContext } from '../../../../../../../context/event/AppEventContext';
import SelectMyProblemStatementToProjectResourceLoader from '@/features/Categories/components/resourceLoaders/SelectMyProblemStatementToProjectResourceLoader';
import AddProblemStatementToProjectModal from '@/features/projectManagementGoals/components/ui/Modal/AddProblemStatementToProjectModal';

export function SharedAddProblemStatementToProject() {
  const navigate = useNavigate();
  const { currentAccountType } = useAppContext();
  const { projectRef } = useParams();
  return (
    <>
      <div>
        <div className="mb-4 flex items-center gap-x-3">
          <ArrowBackIcon
            className="cursor-pointer"
            onClick={() =>
              navigate(
                `/${currentAccountType}/dashboard-projects/${projectRef}`,
              )
            }
          />
          <header
            className={`self-stretch text-[20px] font-semibold capitalize leading-10 
          tracking-tight text-neutral-800`}
          >
            Problem Statements
          </header>
        </div>
        <SelectMyProblemStatementToProjectResourceLoader />
      </div>
      {/* <div className="mt-6 sm:mt-10">
        <header
          className={`mb-4 self-stretch text-[20px] font-semibold capitalize 
          leading-10 tracking-tight text-neutral-800 sm:px-8`}
        >
          Available Problem Statements
        </header>
        <InviteExternalTeamToProjectResourceLoader />
      </div> */}
      <AddProblemStatementToProjectModal />
    </>
  );
}
