import { toLower } from 'lodash';
import { ProfileInformationContainer } from './ProfileInformationContainer';
import { SingleInformation } from './SingleInformation';
import { useAppContext } from '@/context/event/AppEventContext';
import { useGetUserByUserId } from '@/hooks/apiQueryHooks/userQueryHooks';
import { useParams } from 'react-router-dom';

export const PersonalInformationSection = () => {
  const { currentAccountType } = useAppContext();
  const { projectMemberId } = useParams();

  const { data: profile } = useGetUserByUserId({
    id: projectMemberId || '',
  });

  const accountType = toLower(currentAccountType);

  const universityInfo = profile?.accountInfo?.[`${accountType}`];

  return (
    <ProfileInformationContainer className="gap-3">
      <div className="flex justify-between">
        <p className="font-[700]">Personal Information</p>
      </div>
      <SingleInformation
        title="Institution"
        value={universityInfo?.university?.name || 'Not Available'}
      />
      <SingleInformation
        title="Faculty"
        value={universityInfo?.faculty || 'Not Available'}
      />
      <SingleInformation
        title="Department"
        value={universityInfo?.department || 'Not Available'}
      />
    </ProfileInformationContainer>
  );
};
