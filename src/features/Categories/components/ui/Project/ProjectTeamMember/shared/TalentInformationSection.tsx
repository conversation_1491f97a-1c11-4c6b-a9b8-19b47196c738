import { useParams } from 'react-router-dom';

import { Spinner } from '@/components/ui/CommonWidget/Loader';
import { ProfileInformationContainer } from './ProfileInformationContainer';

import { useGetUserByUserId } from '@/hooks/apiQueryHooks/userQueryHooks';
import { Helper } from '@/utils/helpers';
import { DocIcon, PdfIcon } from '@/features/Chat/assets/icons';
import { GoalsHelper } from '@/features/projectManagementGoals/utils/helper';
import { noImagePlaceholder } from '@/assets/images';
import { SingleInformation } from './SingleInformation';
import { useGetTalent } from '@/features/Talent/hooks/apiQueryhooks/talentQueryHook';

export const TalentInformationSection = () => {
  const { projectMemberId } = useParams();

  const { data: profile } = useGetUserByUserId({
    id: projectMemberId || '',
  });

  const userId = profile?.user?.userId as string;

  const { data, isLoading } = useGetTalent(userId);

  const skills = data?.data?.skills || [];
  const resume = data?.data?.resumeUrl || '';

  return (
    <ProfileInformationContainer>
      <div className="flex justify-between gap-5">
        <p className="font-[700]">Talent Skills</p>
      </div>

      {isLoading ? (
        <Spinner />
      ) : (
        <>
          {!data?.data?.user?.userId ? (
            <>No Talent Information Found</>
          ) : (
            <>
              {' '}
              <SingleInformation
                title="Focus Country"
                value={data?.data?.country || 'N/A'}
              />
              <SingleInformation
                title="Institution"
                value={data?.data?.universityDTO?.name || 'N/A'}
              />
              <SingleInformation
                title="Department"
                value={data?.data?.department || 'N/A'}
              />
              {data?.data?.linkedinUrl && (
                <SingleInformation
                  title="Linkedin Profile"
                  className="flex-col items-start"
                >
                  <a
                    href={data?.data?.linkedinUrl}
                    target="_blank"
                    rel="noreferrer"
                  >
                    {data?.data?.linkedinUrl}
                  </a>
                </SingleInformation>
              )}
              <SingleInformation
                title="Skills"
                className="flex-col items-start"
              >
                <div className="value flex flex-col gap-3 text-[16px]">
                  {skills?.length > 0
                    ? skills?.map(skill => (
                        <p key={skill?.categoryRef}>{skill?.name}</p>
                      ))
                    : 'No Skills Found'}
                </div>
              </SingleInformation>
              {resume ? (
                <div className="relative rounded-[4px] p-2">
                  <div className="w-full min-w-[85px] max-w-[200px]">
                    <div className=" rounded-[4px] bg-white max-md:w-full">
                      {GoalsHelper.isImage(resume) && (
                        <>
                          <img
                            loading="lazy"
                            src={resume || noImagePlaceholder}
                            className="max-h-full max-w-full  self-start overflow-hidden object-cover object-center"
                          />
                          <a
                            target="_blank"
                            href={resume}
                            className="absolute inset-0 z-[3]"
                            rel="noreferrer"
                          />
                        </>
                      )}
                      {GoalsHelper.isPdf(resume) && (
                        <div className=" flex max-h-full w-full max-w-full items-center">
                          <PdfIcon className="h-10 w-10 shrink-0" />
                          <a
                            target="_blank"
                            href={resume}
                            className="absolute inset-0 z-[3]"
                            rel="noreferrer"
                          />
                        </div>
                      )}
                      {GoalsHelper.isDoc(resume) && (
                        <div className="flex max-h-full w-full max-w-full items-center">
                          <DocIcon className="h-10 w-10 shrink-0" />
                          <a
                            target="_blank"
                            href={resume}
                            className="absolute inset-0 z-[3]"
                            rel="noreferrer"
                          />
                        </div>
                      )}
                    </div>
                  </div>
                  <p className="mt-2 text-[12px] italic">
                    {Helper.truncateAndKeepExtension(resume || '', 30)}
                    <a
                      target="_blank"
                      href={resume}
                      className="absolute inset-0 z-[3]"
                      rel="noreferrer"
                    />
                  </p>
                </div>
              ) : null}
            </>
          )}
        </>
      )}
    </ProfileInformationContainer>
  );
};
