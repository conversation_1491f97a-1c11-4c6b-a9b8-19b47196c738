import { TalentInformationSection } from './TalentInformationSection';
import { PersonalInformationSection } from './PersonalInformationSection';
import { UserInformationSection } from './UserInformationSection';
import { ArrowRightTailless } from '@/assets/icons';
import { useNavigate, useParams } from 'react-router-dom';
import { useAppContext } from '@/context/event/AppEventContext';
import { useGetTeam } from '@/features/Teams/hooks/apiQueryhooks/eduQueryHooks';
import { useHandleQueryParams } from '@/hooks/useHandleQueryParams';
import { HashLink } from '@/components/ui/HashLink/HasLink';

export default function SharedProjectTeamMember() {
  const navigate = useNavigate();
  const { currentAccountType } = useAppContext();
  const { teamRef: teamRefParam } = useParams();
  const { query } = useHandleQueryParams();
  const teamRef = teamRefParam || query.get('team_ref');
  const { data: team } = useGetTeam(teamRef || query.get('team_ref') || '');
  const { projectRef } = useParams();
  return (
    <div className="max-w-[740px]">
      <div className="flex flex-wrap items-center gap-x-1 py-4">
        <p
          onClick={() => navigate(`/${currentAccountType}/dashboard-projects`)}
          className={` cursor-pointer`}
        >
          Explore Projects
        </p>
        <>
          <ArrowRightTailless className="h-[24px] w-[24px] fill-black" />
        </>
        <HashLink
          to={`/${currentAccountType}/dashboard-projects/${projectRef}/#project-teams`}
          className={` cursor-pointer`}
        >
          Project Teams
        </HashLink>
        <>
          <ArrowRightTailless className="h-[24px] w-[24px] fill-black" />
        </>
        <p
          onClick={() =>
            navigate(
              `/${currentAccountType}/dashboard-projects/${projectRef}/team/${teamRef}`,
            )
          }
          className="cursor-pointer text-primary"
        >
          {team?.data?.teamName}
        </p>
      </div>
      <div className="flex flex-col gap-4">
        <UserInformationSection />
        <PersonalInformationSection />
        <TalentInformationSection />
      </div>
    </div>
  );
}
