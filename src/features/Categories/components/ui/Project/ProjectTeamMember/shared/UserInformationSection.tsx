import LazyLoadImageContainer from '@/components/ui/CommonWidget/LazyLoadImageContainer';
import { ProfileInformationContainer } from './ProfileInformationContainer';
import useLazyLoadingHandler from '@/hooks/useLazyLoadingHandler';
import { profilePlaceholder } from '@/assets/images';
import { useGetUserByUserId } from '@/hooks/apiQueryHooks/userQueryHooks';
import { useNavigate, useParams } from 'react-router-dom';
import useGetTeamLevelBasedAccess from '@/hooks/useGetTeamLevelBasedAccess';
import { toLower } from 'lodash';
import { useAppContext } from '@/context/event/AppEventContext';
import { ChatIcon2 } from '@/assets/icons';
import Button from '@/components/ui/ButtonComponent';
import { useCreateChatMessage } from '@/hooks/apiQueryHooks/chatQueryHooks';
import useHandleApiFeebackWithToast from '@/hooks/useHandleApiFeebackWithToast';
import { DataResponse } from '@/types';

export const UserInformationSection = () => {
  const navigate = useNavigate();
  const { currentAccountType } = useAppContext();
  const { loaded, imgRefs } = useLazyLoadingHandler();
  const { projectMemberId } = useParams();
  const next = (res: DataResponse<any>) => {
    navigate(`/${currentAccountType}/chat?chat_room_id=${res?.data?._id}`);
  };
  const { mutate: createChatRoom } = useCreateChatMessage({
    ...useHandleApiFeebackWithToast({ next }),
  });

  const { data: profile } = useGetUserByUserId({
    id: projectMemberId || '',
  });
  const { isTeamLeadAtEachMemberLevel, isLoggedInUserThisTeamMember } =
    useGetTeamLevelBasedAccess({
      teamMemberUserId: projectMemberId || '',
    });

  const accountType = toLower(currentAccountType);

  const universityInfo = profile?.accountInfo?.[`${accountType}`];
  return (
    <ProfileInformationContainer className="flex items-center justify-between gap-[33px] sm:flex-row">
      <div className="flex items-center justify-between gap-[33px] sm:flex-row">
        <div className="max-w-[150px]">
          <LazyLoadImageContainer
            loaded={loaded}
            className="group relative mb-[9px] aspect-square h-full max-h-[100px] w-full max-w-[100px] flex-shrink-0 cursor-pointer"
          >
            <img
              ref={element => (imgRefs.current[0] = element!)}
              loading="lazy"
              src={profile?.user?.profilePicture || profilePlaceholder}
              alt=""
              className={`h-full w-full rounded-full transition duration-300 ease-in-out sm:object-cover ${
                loaded ? 'opacity-100' : 'opacity-0'
              }`}
            />
          </LazyLoadImageContainer>
          <p className="mx-auto bg-lightOrangeTwo p-2 text-[14px] leading-[22px]">
            {isTeamLeadAtEachMemberLevel ? 'Team Lead' : 'Team Member'}
          </p>
        </div>
        <div className="flex flex-1 flex-col gap-3">
          <p className="text-[16px] font-[700]">{`${profile?.user?.firstName ? profile?.user?.firstName : 'N/A'} ${profile?.user?.lastName ? profile?.user?.lastName : 'N/A'}`}</p>
          <p className=" text-[16px]">
            {universityInfo?.faculty || 'Not Available'}
          </p>
          <p className=" text-[16px]">
            {universityInfo?.university?.name || 'Not Available'}
          </p>
        </div>
      </div>
      {!isLoggedInUserThisTeamMember && (
        <div
          onClick={() => createChatRoom({ receiverId: projectMemberId || '' })}
          className=" px-5 pb-5 pt-5"
        >
          <Button className="group mx-auto flex h-[33px] w-full max-w-[145px] items-center gap-x-1 whitespace-nowrap rounded-[2.7px] border-[1px] border-grayTen bg-transparent text-[12px] hover:border-primary hover:bg-primary hover:text-white">
            <ChatIcon2 className="h-5 w-5 cursor-pointer fill-graySixteen group-hover:fill-white" />
            Send Message
          </Button>
        </div>
      )}
    </ProfileInformationContainer>
  );
};
