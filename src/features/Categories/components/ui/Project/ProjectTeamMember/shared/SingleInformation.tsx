import { cn } from '@/lib/twMerge/cn';
import { ReactNode } from 'react';

export const SingleInformation = ({
  title,
  value,
  children,
  className,
}: {
  title: string;
  value?: string | undefined | null;
  children?: ReactNode;
  className?: string;
}) => {
  return (
    <div className={cn(`flex items-center gap-2 ${className}`)}>
      <div className="flex flex-col gap-[9px]">
        <p className="text-[15px] text-grayTen">{title}</p>
        {value && <p className="value text-[15px]">{value}</p>}
      </div>
      {children}
    </div>
  );
};
