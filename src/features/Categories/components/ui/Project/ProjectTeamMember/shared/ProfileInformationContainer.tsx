import { cn } from '@/lib/twMerge/cn';
import { ReactNode } from 'react';

export const ProfileInformationContainer = ({
  children,
  className,
}: {
  children?: ReactNode;
  className?: string;
}) => {
  return (
    <div
      className={cn(
        `flex w-full flex-col gap-6 bg-[#fff] px-5 py-4 shadow-[0px_2px_30px_0px_#BAB9B933] ${className}`,
      )}
    >
      {children}
    </div>
  );
};
