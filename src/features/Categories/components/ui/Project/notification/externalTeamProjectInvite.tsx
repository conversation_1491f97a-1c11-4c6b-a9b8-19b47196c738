import { useNavigate } from 'react-router-dom';

import Button from '../../../../../../components/ui/ButtonComponent';
import ProblemStatementDescriptions from '../../../../../../components/ui/CategoryWidget/ProblemStatement/ProblemStatementDescriptions';
import ProjectCategoryCard, {
  ProjectName,
} from '../../../../../../components/ui/CategoryWidget/Project/ProjectCategoryCard';
import ProjectDescription from '../../../../../../components/ui/CategoryWidget/Project/ProjectDescription';
import CreatorWidget from '../../../../../../components/ui/CommonWidget/CreatorWidget';
import { Spinner } from '../../../../../../components/ui/CommonWidget/Loader';
import Player from '../../../../../../components/ui/WatchWidget/player';
import { useAppContext } from '../../../../../../context/event/AppEventContext';
import { useGetProblemStatement } from '../../../../../../hooks/apiQueryHooks/eduQueryHooks';
import useHandleApiFeebackWithToast from '../../../../../../hooks/useHandleApiFeebackWithToast';
import { useHandleQueryParams } from '../../../../../../hooks/useHandleQueryParams';
import {
  useGetProject,
  useProcessExternalTeamInvite,
} from '../../../../hooks/apiQueryHooks/eduQueryHooks';
import TeamsPreview from '../Project/Shared/TeamsPreview';
import { ExternalTeamProjectInviteProps } from '@/types';
export default function ExternalTeamProjectInvite({
  msg,
  teamRef,
  projectRef,
}: ExternalTeamProjectInviteProps) {
  const { query } = useHandleQueryParams();
  const { currentAccountType } = useAppContext();
  const navigate = useNavigate();
  const next = () => {
    navigate(`/${currentAccountType}/teams/${teamRef}`);
  };
  const { mutate, isLoading: isLoadingExternalTeamInvite } =
    useProcessExternalTeamInvite({
      ...useHandleApiFeebackWithToast({
        next,
      }),
    });
  const handleConfirmAcceptanceStatus = (value: boolean) => {
    mutate({
      teamRef,
      projectRef: projectRef || '',
      accept: value,
    });
  };

  const { data: project, isLoading } = useGetProject(
    projectRef || query.get('project_ref') || '',
  );
  const { data: problemStatement } = useGetProblemStatement(
    project?.data?.problemStatementRef || '',
    {
      enabled: !!project?.data?.problemStatementRef,
    },
  );
  if (isLoading)
    return (
      <div className="flex h-[400px] w-full items-center justify-center">
        <Spinner className="" />
      </div>
    );
  return (
    <div>
      <h5>{msg}</h5>
      <div className="mt-6">
        <p>Choose an option</p>
        <div className="mt-4 flex items-center gap-x-[14px]">
          <Button
            disabled={isLoadingExternalTeamInvite}
            type="button"
            onClick={() => handleConfirmAcceptanceStatus(true)}
            className={`group w-full max-w-[140px] bg-primary text-white
            disabled:border  disabled:border-primary disabled:bg-white   disabled:text-primary`}
          >
            <p className="text-[12px]">Accept</p>
          </Button>
          <Button
            type="button"
            onClick={() => handleConfirmAcceptanceStatus(false)}
            className="w-full max-w-[140px] border border-primary bg-white 
            text-primary   hover:bg-primary hover:text-white"
          >
            <p className="text-[12px]">Decline</p>
          </Button>
        </div>
      </div>
      <div className="mt-6 sm:mt-12">
        <CreatorWidget createdBy={project?.data?.createdByName || ''} />
      </div>
      <div className="flex justify-between">
        <div className="mb-12 w-full max-w-[500px] bg-lightOrangeTwo p-6 shadow-md">
          <div className="mb-2">
            <ProjectCategoryCard
              project={{
                ...project?.data,
                projectImageUrl: project?.data?.projectImageUrl || '',
                projectName: project?.data?.projectName || 'N/A',
                projectRef: project?.data?.projectRef || '',
              }}
              key={project?.data?.projectRef}
              hideToolTip
            >
              <ProjectName />
            </ProjectCategoryCard>
          </div>
          <div className="">
            <div className="mt-12 overflow-auto rounded-[8px] bg-lightOrangeTwo p-6">
              <div className="max-h-[400px]">
                <TeamsPreview />
              </div>
            </div>
          </div>
        </div>
        <div className="w-full max-w-[502px]">
          <div className="">
            <ProjectDescription
              hideEdit
              description={project?.data?.projectDescription}
            />
          </div>
          {problemStatement && (
            <div className="mt-12">
              <h5 className=" mb-4 text-[16px] leading-[27px]">
                Problem statement
                <br />
                <span className="text-primary">
                  {problemStatement?.data.title}
                </span>
              </h5>
              <div className="relative aspect-[2] max-w-[502px] rounded-[10px]">
                <Player url={problemStatement?.data?.descVidUrl || ''} />
              </div>
              <ProblemStatementDescriptions
                description={problemStatement?.data?.description}
                fileTitle={problemStatement?.data.title || ''}
                fileUrl={problemStatement?.data.descDocUrl || ''}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
