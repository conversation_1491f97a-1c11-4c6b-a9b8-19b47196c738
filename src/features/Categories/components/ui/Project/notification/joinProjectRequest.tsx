import { useNavigate } from 'react-router-dom';

import Button from '../../../../../../components/ui/ButtonComponent';
import { Spinner } from '../../../../../../components/ui/CommonWidget/Loader';
import { useAppContext } from '../../../../../../context/event/AppEventContext';
import useHandleApiFeebackWithToast from '../../../../../../hooks/useHandleApiFeebackWithToast';
import { useHandleQueryParams } from '../../../../../../hooks/useHandleQueryParams';
import { ProjectByTeamRefResourceLoader } from '../../../..';

import TeamImageAndDescription from '../../../../../Teams/components/ui/Team/Shared/TeamImageAndDescription';
import TeamMemberList from '../../../../../Teams/components/ui/TeamMembers/Shared/TeamMemberList';
import { useGetTeam } from '@/features/Teams/hooks/apiQueryhooks/eduQueryHooks';
import { useProcessJoinRequest } from '@/features/Categories/hooks/apiQueryHooks/eduQueryHooks';
import { JoinProjectRequestProps } from '@/types';

export default function JoinProjectRequest({
  msg,
  teamRef,
  projectRef,
}: JoinProjectRequestProps) {
  const { query } = useHandleQueryParams();
  const { currentAccountType } = useAppContext();
  const navigate = useNavigate();
  const next = () => {
    navigate(`/${currentAccountType}/dashboard-projects/${projectRef}`);
  };
  const { mutate, isLoading: isLoadingExternalTeamInvite } =
    useProcessJoinRequest({
      ...useHandleApiFeebackWithToast({
        next,
      }),
    });
  const handleConfirmAcceptanceStatus = (value: boolean) => {
    mutate({
      teamRef: teamRef || query.get('team_ref') || '',
      projectRef: projectRef || '',
      accept: value,
    });
  };

  const { data: team, isLoading } = useGetTeam(
    teamRef || query.get('team_ref') || '',
  );

  if (isLoading)
    return (
      <div className="flex h-[400px] w-full items-center justify-center">
        <Spinner className="" />
      </div>
    );
  return (
    <div>
      <h5>{msg}</h5>
      <div className="mt-6">
        <p>Choose an option</p>
        <div className="mt-4 flex items-center gap-x-[14px]">
          <Button
            disabled={isLoadingExternalTeamInvite}
            type="button"
            onClick={() => handleConfirmAcceptanceStatus(true)}
            className={`group w-full max-w-[140px] bg-primary text-white
            disabled:border  disabled:border-primary disabled:bg-white   disabled:text-primary`}
          >
            <p className="text-[12px]">Accept</p>
          </Button>
          <Button
            type="button"
            onClick={() => handleConfirmAcceptanceStatus(false)}
            className="w-full max-w-[140px] border border-primary bg-white 
            text-primary   hover:bg-primary hover:text-white"
          >
            <p className="text-[12px]">Decline</p>
          </Button>
        </div>
      </div>
      <div className="mt-6 sm:mt-12">
        <TeamImageAndDescription
          image={team?.data?.image || ''}
          teamDescription={team?.data?.teamDescription || ''}
          teamRef={team?.data?.teamRef || ''}
        />
      </div>
      <div className="mt-14 max-h-[500px] overflow-auto pb-16">
        <TeamMemberList />
      </div>
      <div className="rounded-[8px] border border-grayEigtheen bg-grayTwo px-10 py-6">
        <ProjectByTeamRefResourceLoader />
      </div>
    </div>
  );
}
