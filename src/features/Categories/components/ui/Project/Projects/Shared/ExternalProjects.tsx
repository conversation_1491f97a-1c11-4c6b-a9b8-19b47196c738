import { forwardRef, ReactNode, Ref } from 'react';
import SectionCase from '../../../../../../../components/ui/CommonWidget/SectionCase';
import ProjectCategoryCard, {
  ProjectName,
  ProjectSdgs,
  ProjectCountry,
  ProjectInstitution,
} from '../../../../../../../components/ui/CategoryWidget/Project/ProjectCategoryCard';
import SlideController from '../../../../../../../components/ui/CommonWidget/SlideController';

import { useCustomMedia } from '../../../../../../../hooks/useCustomMedia';
import { useHandleQueryParams } from '@/hooks/useHandleQueryParams';

import { ServerProjects } from '../../../../../../../types';
import { InfiniteData } from '@tanstack/react-query';
import ProjectCategoryListForInfinityQuery from './ProjectCategoryListForInfinityQuery';

type Props = {
  pages: InfiniteData<ServerProjects>['pages'];
  children: ReactNode;
};

export default forwardRef(function ExternalProjects(
  { children, pages }: Props,
  ref: Ref<HTMLDivElement>,
) {
  const { screenSize } = useCustomMedia();
  const { handleQuery } = useHandleQueryParams();
  return (
    <SlideController
      dataLength={pages?.[0].data.projects.length}
      isMobile={screenSize < 768}
    >
      <SectionCase
        route={() => handleQuery({ project: 'my_external_projects' })}
        descriptionText="My External Projects"
      >
        <div className="mt-4">
          <ProjectCategoryListForInfinityQuery pages={pages} ref={ref}>
            <ProjectCategoryCard>
              <ProjectName />
              <ProjectSdgs />
              <ProjectInstitution />
              <ProjectCountry />
              {children}
            </ProjectCategoryCard>
          </ProjectCategoryListForInfinityQuery>
        </div>
      </SectionCase>
    </SlideController>
  );
});
