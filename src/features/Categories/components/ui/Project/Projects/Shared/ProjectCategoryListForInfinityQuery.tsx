import React, { ComponentProps, forwardRef, ReactElement, Ref } from 'react';

import { InfiniteData } from '@tanstack/react-query';
import { ServerProjects } from '@/types';
import { Project } from '@/features/Categories/types';
import { cn } from '@/lib/twMerge/cn';

interface Props extends ComponentProps<'div'> {
  pages: InfiniteData<ServerProjects>['pages'];
  children: ReactElement<{
    project: Project;
    pageIndex: number;
    index: number;
    ref: Ref<HTMLDivElement>;
  }>;
  isAvailableProjectVariant?: boolean;
  className?: string;
}

export default forwardRef(function ProjectCategoryListForInfinityQuery(
  { children, pages, className }: Props,
  ref: Ref<HTMLDivElement>,
) {
  // Flatten all projects from all pages and remove duplicates
  const allProjects =
    pages
      ?.flatMap(page => page?.data?.projects || [])
      .filter(
        (project, index, array) =>
          array.findIndex(p => p.projectRef === project.projectRef) === index,
      ) || [];

  return (
    <div
      className={cn(`flex gap-3 px-2 pb-4 max-md:items-stretch ${className}`)}
    >
      {allProjects.map((project, index) => {
        // Only add ref to the last item for infinite scroll
        const isLastItem = index === allProjects.length - 1;

        return React.cloneElement(children, {
          project,
          pageIndex: 0,
          index: index + 1,
          key: project?.projectRef,
          ...(isLastItem && { ref }),
        });
      })}
    </div>
  );
});
