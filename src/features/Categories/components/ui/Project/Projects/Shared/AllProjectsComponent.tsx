import AvailableProjectResourceLoader from '@/features/Categories/components/resourceLoaders/AvailableProjectsResourceLoader';
import ExternalProjectResourceLoader from '@/features/Categories/components/resourceLoaders/ExternalProjectResourceLoader';
import ProjectsResourceLoader from '@/features/Categories/components/resourceLoaders/ProjectsResourceLoader';
import {
  useGetAvailableProjectsInfiniteQuery,
  useGetExternalProjectsInfiniteQuery,
  useGetProjectsInfiniteQuery,
} from '@/features/Categories/hooks/apiQueryHooks/eduQueryHooks';

export default function AllProjectsComponent() {
  return (
    <>
      <ProjectsResourceLoader
        useGetProjects={() => useGetProjectsInfiniteQuery()}
      />
      <section className="mt-6 sm:mt-10">
        <ExternalProjectResourceLoader
          useGetProjects={() => useGetExternalProjectsInfiniteQuery()}
        />
      </section>
      <section className="mt-6 sm:mt-10">
        <AvailableProjectResourceLoader
          useGetProjects={() => useGetAvailableProjectsInfiniteQuery()}
        />
      </section>
    </>
  );
}
