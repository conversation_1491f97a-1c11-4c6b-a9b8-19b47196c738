import { forwardRef, ReactNode, Ref } from 'react';

import ProjectCategoryCard, {
  ProjectCountry,
  ProjectInstitution,
  ProjectName,
  ProjectSdgs,
} from '../../../../../../../components/ui/CategoryWidget/Project/ProjectCategoryCard';
import SectionCase from '../../../../../../../components/ui/CommonWidget/SectionCase';
import SlideController from '../../../../../../../components/ui/CommonWidget/SlideController';
import { useCustomMedia } from '../../../../../../../hooks/useCustomMedia';
import { ServerProjects } from '../../../../../../../types';
import { useHandleQueryParams } from '@/hooks/useHandleQueryParams';
import { InfiniteData } from '@tanstack/react-query';
import ProjectCategoryListForInfinityQuery from './ProjectCategoryListForInfinityQuery';

type Props = {
  pages: InfiniteData<ServerProjects>['pages'];
  children: ReactNode;
};

export default forwardRef(function MyProjects(
  { children, pages }: Props,
  ref: Ref<HTMLDivElement>,
) {
  const { screenSize } = useCustomMedia();
  const { handleQuery } = useHandleQueryParams();

  return (
    <SlideController
      dataLength={pages?.[0].data.projects.length}
      isMobile={screenSize < 768}
    >
      <SectionCase
        route={() => handleQuery({ project: 'my_internal_projects' })}
        descriptionText="My Internal Projects"
      >
        <div className="mt-4">
          <ProjectCategoryListForInfinityQuery pages={pages} ref={ref}>
            <ProjectCategoryCard>
              <ProjectName />
              <ProjectSdgs />
              <ProjectInstitution />
              <ProjectCountry />
              {children}
            </ProjectCategoryCard>
          </ProjectCategoryListForInfinityQuery>
        </div>
      </SectionCase>
    </SlideController>
  );
});
