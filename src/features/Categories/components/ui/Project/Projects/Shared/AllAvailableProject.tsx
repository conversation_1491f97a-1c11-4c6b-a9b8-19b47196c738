import { ReactNode } from 'react';

import {
  ProjectCategoryCardForAvailableProject,
  ProjectCountry,
  ProjectInstitution,
  ProjectName,
  ProjectSdgs,
} from '../../../../../../../components/ui/CategoryWidget/Project/ProjectCategoryCard';
import SectionCase from '../../../../../../../components/ui/CommonWidget/SectionCase';
import { PaginationType } from '../../../../../../../types';
import { Project } from '../../../../../types';
import ProjectCategoryList from './ProjectCategoryList';

type Props = {
  projects: { projects: Project[]; pagination: PaginationType };
  children: ReactNode;
};

export default function AllAvailableProject({ children, projects }: Props) {
  return (
    <SectionCase pagination={projects?.pagination}>
      <div className="mt-4">
        <ProjectCategoryList
          className="flex-wrap"
          projectList={projects?.projects}
        >
          <ProjectCategoryCardForAvailableProject>
            <ProjectName />
            <ProjectSdgs />
            <ProjectInstitution />
            <ProjectCountry />
            {children}
          </ProjectCategoryCardForAvailableProject>
        </ProjectCategoryList>
      </div>
    </SectionCase>
  );
}
