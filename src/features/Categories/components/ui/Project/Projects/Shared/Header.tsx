import { useMemo } from 'react';

import { CloseIcon } from '@/assets/icons';

import SearchBar from '@/components/ui/SearchBar';
import { FilterByCountryAndSDG } from '../../../../../../../components/ui/CommonWidget/FilterByCountryAndSDG';

import { useAppContext } from '@/context/event/AppEventContext';
import { useHandleQueryParams } from '@/hooks/useHandleQueryParams';

export default function Header() {
  const { query, handleQuery } = useHandleQueryParams();
  const { filters, countryFilters, resetFilters, resetCountriesFilters } =
    useAppContext();

  const clearFilters = () => {
    resetFilters();
    resetCountriesFilters();
  };

  const isFiltered = useMemo(
    () => countryFilters.length > 0 || filters.length > 0,
    [countryFilters, filters],
  );

  return (
    <div className="flex gap-x-4">
      <SearchBar
        setSearchValue={value => handleQuery({ q: value })}
        defaultValue={query.get('q') || ''}
        className="rounded"
      />
      <FilterByCountryAndSDG
        className={`rounded ${isFiltered ? '!bg-primary !text-white [&_svg]:stroke-white' : ''}`}
      />
      <div
        onClick={clearFilters}
        className={`flex h-[37px]  cursor-pointer items-center gap-3 whitespace-nowrap  transition-all duration-150 ${isFiltered ? 'w-36 opacity-100' : 'w-0 opacity-0'}`}
      >
        clear filter <CloseIcon className="h-3 w-3 cursor-pointer" />
      </div>
    </div>
  );
}
