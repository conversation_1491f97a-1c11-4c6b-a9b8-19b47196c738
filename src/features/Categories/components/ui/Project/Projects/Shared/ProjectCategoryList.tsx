import React, { ReactElement } from 'react';

import { Project } from '../../../../../types';
import { cn } from '@/lib/twMerge/cn';

type Props = {
  projectList: Project[];
  isAvailableProjectVariant?: boolean;
  children: ReactElement<{ project: Project; index: number }>;
  className?: string;
};

export default function ProjectCategoryList({
  className,
  children,
  projectList,
}: Props) {
  return (
    <div
      className={cn(`flex gap-3 px-2 pb-4 max-md:items-stretch ${className}`)}
    >
      {projectList?.map((project, index) => {
        return React.cloneElement(children, {
          project,
          index: index + 1,
          key: project?.projectRef,
        });
      })}
    </div>
  );
}
