import { forwardRef, ReactNode, Ref } from 'react';
import SectionCase from '../../../../../../../components/ui/CommonWidget/SectionCase';
import {
  ProjectName,
  ProjectSdgs,
  ProjectCountry,
  ProjectInstitution,
  ProjectCategoryCardForAvailableProject,
} from '../../../../../../../components/ui/CategoryWidget/Project/ProjectCategoryCard';
import SlideController from '../../../../../../../components/ui/CommonWidget/SlideController';

import { useCustomMedia } from '../../../../../../../hooks/useCustomMedia';
import { useHandleQueryParams } from '@/hooks/useHandleQueryParams';

import { ServerProjects } from '../../../../../../../types';
import { InfiniteData } from '@tanstack/react-query';
import ProjectCategoryListForInfinityQuery from './ProjectCategoryListForInfinityQuery';

type Props = {
  pages: InfiniteData<ServerProjects>['pages'];
  children: ReactNode;
};

export default forwardRef(function AvailableProjects(
  { children, pages }: Props,
  ref: Ref<HTMLDivElement>,
) {
  const { screenSize } = useCustomMedia();
  const { handleQuery } = useHandleQueryParams();
  return (
    <SlideController
      dataLength={pages?.[0].data.projects.length}
      isMobile={screenSize < 768}
    >
      <SectionCase
        route={() => handleQuery({ project: 'available_projects' })}
        descriptionText="Available Projects"
      >
        <div className="mt-4">
          <ProjectCategoryListForInfinityQuery pages={pages} ref={ref}>
            <ProjectCategoryCardForAvailableProject>
              <ProjectName />
              <ProjectSdgs />
              <ProjectInstitution />
              <ProjectCountry />
              {children}
            </ProjectCategoryCardForAvailableProject>
          </ProjectCategoryListForInfinityQuery>
        </div>
      </SectionCase>
    </SlideController>
  );
});
