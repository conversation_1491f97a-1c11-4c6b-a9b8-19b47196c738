import { useHandleCanCreateProject } from '@/features/Categories/hooks/useHandleCanCreateProject';
import CommonDashedBorderBoxWithActions from '../../../../../../../components/ui/CommonWidget/CommonDashedBorderBoxWithActions';
import { useAppContext } from '../../../../../../../context/event/AppEventContext';

type Props = {
  title: string;
};

export default function ProjectEmptyState({ title }: Props) {
  const { setShowModalHandler } = useAppContext();

  const {
    handleTrigger: projectHandleTrigger,
    isFetching: isFetchingCanCreateProject,
  } = useHandleCanCreateProject({
    onSuccess: () => {
      setShowModalHandler('ShouldSkipChooseProblemStatement');
    },
  });

  const handleProjectsClick = () => {
    projectHandleTrigger();
  };

  return (
    <div className="px-8">
      <h3 className="mb-[14px] text-base font-medium leading-8 text-stone-950">
        {title}
      </h3>
      <CommonDashedBorderBoxWithActions
        buttonText="Create Project"
        message="No project available yet."
        buttonAction={handleProjectsClick}
        isLoading={isFetchingCanCreateProject}
      />
    </div>
  );
}
