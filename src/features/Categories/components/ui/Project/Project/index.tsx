import withProjectRouteNotificationDetails from '@/features/Categories/hoc/withProjectRouteNotificationDetails';
import DynamicRoutingShell from '../../../../../../components/ui/template/DynamicRoutingShell';
import { NotificationProviderFactory } from '../../../../../../providers/notificationProviderFactory';
import { SharedProject } from './Shared';
import Header, { SeeMoreAboutProjectForGoalsHeader } from './Shared/Header';
import { NotificationRoutesValues } from '@/types';
import { useHandleQueryParams } from '@/hooks/useHandleQueryParams';

export default function Project() {
  const { query } = useHandleQueryParams();
  const notificationType = query.get(
    'notificationType',
  ) as NotificationRoutesValues | null;

  const CurrentRender = notificationType
    ? withProjectRouteNotificationDetails(
        new NotificationProviderFactory().getFactoryMap()[notificationType],
      )
    : function FallbackRender() {
        return (
          <SharedProject
            header={({ projectName, projectStatus, visibility }) => (
              <Header
                projectStatus={projectStatus}
                projectName={projectName}
                visibility={visibility}
              />
            )}
          />
        );
      };
  return (
    <DynamicRoutingShell
      practitioner={<CurrentRender />}
      faculty={<CurrentRender />}
      student={<CurrentRender />}
    />
  );
}

export const SeeMoreAboutProjectForGoals = () => {
  return (
    <SharedProject
      header={({ projectName }) => (
        <SeeMoreAboutProjectForGoalsHeader projectName={projectName} />
      )}
    />
  );
};
