import { useCallback } from 'react';

import { PencilIcon } from '@/features/Profile/assets';

import { useAppContext } from '@/context/event/AppEventContext';
import { useGetSDGs } from '@/hooks/apiQueryHooks/eduQueryHooks';

import { cn } from '@/lib/twMerge/cn';
import { Project } from '../../../../../types';
import { projectLevels } from '@/features/Categories/data/data';
import useGetProjectLevelBasedAccess from '@/hooks/useGetProjectLevelBasedAccess';

import { Helper } from '@/utils/helpers';

type Props = { project: Project };

export default function ProjectExtraInfomation({ project }: Props) {
  const { setShowModalHandler } = useAppContext();

  const { data } = useGetSDGs({
    staleTime: 1000 * 60 * 60 * 24,
    cacheTime: 1000 * 60 * 60 * 25,
  });

  const categories = (data?.data || []).filter(item =>
    project?.categoryRefs.includes(item.categoryRef),
  );

  const projectLevel = projectLevels.find(
    level => level.value === project?.projectLevel,
  );

  const handleClick = useCallback(() => {
    setShowModalHandler('UpdateProjectInformationModal');
  }, []);

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-row gap-4 sm:gap-8">
        <InfoPills
          label="Project Level"
          value={projectLevel?.label || 'Project Level'}
          onClick={handleClick}
        />
        <InfoPills
          label="Country Focus"
          value={
            project?.projectCountries?.length > 0
              ? project?.projectCountries.join(', ')
              : 'Add Country Focus'
          }
          onClick={handleClick}
        />
      </div>
      <div className="flex gap-4 sm:gap-8">
        <InfoPills
          label="Goals Categories (Up to 2)"
          value={
            categories?.length > 0
              ? categories.map(category => category?.categoryName).join(', ')
              : 'Add Supported SDGs'
          }
          onClick={handleClick}
        />

        <InfoPills
          label="Project Duration"
          value={
            project?.startDate && project?.endDate
              ? `${Helper.formatDateUsingJavascriptApi(project?.startDate, {
                  month: 'short',
                  day: '2-digit',
                  year: 'numeric',
                })} - ${Helper.formatDateUsingJavascriptApi(project?.endDate, {
                  month: 'short',
                  day: '2-digit',
                  year: 'numeric',
                })}`
              : 'Add Project Duration'
          }
          onClick={handleClick}
        />
      </div>
    </div>
  );
}

const InfoPills = ({
  label,
  value,
  onClick,
  className,
}: {
  label: string;
  value: string;
  className?: string;
  onClick?: () => void;
}) => {
  const {
    isCreatorAtProjectLevel,
    isLoggedInUserAProjectAdmin,
    isLoggedInUserAProjectMember,
  } = useGetProjectLevelBasedAccess();
  return (
    <div className={cn('min-w-[154px]', className)}>
      <p className="mb-3 text-[16px] font-semibold">{label}</p>
      <div
        className={cn(
          'flex gap-2 rounded border border-primary bg-[#F7C1931A] px-2.5 py-1.5 text-[16px] text-grayNine',
          {
            'rounded-none border-0 bg-[#F8F8F8] text-black':
              !isLoggedInUserAProjectMember && !isLoggedInUserAProjectAdmin,
          },
        )}
      >
        {value}
        {(isCreatorAtProjectLevel || isLoggedInUserAProjectAdmin) && (
          <PencilIcon
            className="cursor-pointer stroke-grayNine hover:stroke-primary"
            onClick={onClick}
          />
        )}
      </div>
    </div>
  );
};
