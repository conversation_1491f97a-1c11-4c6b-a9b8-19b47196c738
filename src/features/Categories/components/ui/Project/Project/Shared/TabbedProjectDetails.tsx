import { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import cn from 'classnames';
import { Button } from '@/components/ui/ButtonComponent/button';
import { PencilIcon } from '@/features/Profile/assets';
import { useAppContext } from '@/context/event/AppEventContext';
import useGetProjectLevelBasedAccess from '@/hooks/useGetProjectLevelBasedAccess';

const TABS = [
  { key: 'description', label: 'Description' },
  { key: 'teams', label: 'Project Teams' },
  { key: 'files', label: 'Attached Files' },
  { key: 'tasks', label: 'Tasks' },
  { key: 'problem', label: 'Attached Problem Statement' },
];

export type TabKey = (typeof TABS)[number]['key'];

export default function TabbedProjectDetails({
  activeTab,
  onTabChange,
  children,
}: {
  activeTab: TabKey;
  onTabChange: (tab: TabKey) => void;
  children: React.ReactNode;
}) {
  const navigate = useNavigate();
  const location = useLocation();
  const { setShowModalHandler } = useAppContext();
  const { isLoggedInUserAProjectAdmin, isCreatorAtProjectLevel } =
    useGetProjectLevelBasedAccess();

  // Sync tab with URL
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    if (params.get('tab') !== activeTab) {
      params.set('tab', activeTab);
      navigate({ search: params.toString() }, { replace: true });
    }
    // eslint-disable-next-line
  }, [activeTab]);

  return (
    <div className="flex w-full max-w-[1106px] flex-col gap-4">
      <div className="flex w-full items-center gap-6">
        <div className="flex w-full gap-4 overflow-x-auto border-b border-gray-200">
          {TABS.map(tab => (
            <button
              key={tab.key}
              className={cn(
                'relative flex-shrink-0 px-4 py-2 text-sm font-medium transition-colors duration-200',
                activeTab === tab.key
                  ? 'text-primary'
                  : 'text-gray-500 hover:text-primary',
              )}
              onClick={() => onTabChange(tab.key as TabKey)}
              aria-selected={activeTab === tab.key}
              aria-controls={`tabpanel-${tab.key}`}
              role="tab"
            >
              {tab.label}
              {activeTab === tab.key && (
                <motion.div
                  layoutId="tab-underline"
                  className="absolute -bottom-0.5 left-0 right-0 h-0.5 rounded-full bg-primary"
                  transition={{ type: 'spring', stiffness: 400, damping: 30 }}
                />
              )}
            </button>
          ))}
        </div>
        {/* {isLoggedInUserAProjectAdmin && activeTab === 'problem' && (
          <Button
            onClick={() => navigate('add-problem-statement-to-project')}
            type="button"
            className="flex cursor-pointer items-center gap-2 bg-[#979797] leading-none text-white hover:bg-[#979797]/80"
          >
            Update
            <PencilIcon className="size-4 shrink-0 stroke-white" />
          </Button>
        )} */}
        {activeTab === 'description' && (
          <>
            {(isLoggedInUserAProjectAdmin || isCreatorAtProjectLevel) && (
              <Button
                className="flex cursor-pointer items-center gap-2 bg-[#979797] leading-none text-white hover:bg-[#979797]/80"
                onClick={() =>
                  setShowModalHandler('UpdateProjectInformationModal')
                }
              >
                Edit <PencilIcon className="h-4 w-4 stroke-white" />
              </Button>
            )}
          </>
        )}
      </div>
      <AnimatePresence mode="wait">
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.25 }}
          role="tabpanel"
          id={`tabpanel-${activeTab}`}
        >
          {children}
        </motion.div>
      </AnimatePresence>
    </div>
  );
}
