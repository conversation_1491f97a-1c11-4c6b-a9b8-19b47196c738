import { useNavigate, useParams } from 'react-router-dom';

import { AddIcon } from '../../../../../../../assets/icons';
import { Spinner } from '../../../../../../../components/ui/CommonWidget/Loader';
import { TeamPreviewCard } from '../../../../../../../components/ui/TeamsWidget/TeamPreviewCard';
import { useAppContext } from '../../../../../../../context/event/AppEventContext';
import useGetProjectLevelBasedAccess from '../../../../../../../hooks/useGetProjectLevelBasedAccess';
import { useHandleQueryParams } from '../../../../../../../hooks/useHandleQueryParams';
import { useGetProjectTeams } from '../../../../..';
import Button from '@/components/ui/ButtonComponent';
import ViewTeamModal from '@/features/projectManagementGoals/components/ui/Modal/ViewTeamModal';
import { TooltipProvider } from '@/components/ui/tooltip';
import { DialogWithTooltip } from '@/components/ui/CommonWidget/DialogWithTooltip';

export default function TeamsPreview() {
  const navigate = useNavigate();
  const { projectRef } = useParams();
  const { query, handleQuery } = useHandleQueryParams();
  const projectId = projectRef || query.get('project_ref') || '';

  const { currentAccountType, setShowModalHandler } = useAppContext();
  const { data: teams, isLoading: isLoadingTeams } = useGetProjectTeams({
    projectRef: projectId,
  });
  const { isLoadingUser, isCreatorAtProjectLevel } =
    useGetProjectLevelBasedAccess();
  if (isLoadingTeams || isLoadingUser)
    return (
      <div
        className="flex h-[100px] w-full max-w-[444px]  items-center 
        justify-center  self-stretch"
      >
        <Spinner />
      </div>
    );
  return (
    <TooltipProvider>
      <div>
        {teams?.data && teams?.data?.length > 0 ? (
          <>
            {teams?.data?.slice(0, 2)?.map(({ teamRef, teamName }) => (
              <div key={teamRef} className="mb-6">
                <TeamPreviewCard
                  teamRef={teamRef}
                  teamName={teamName}
                  isPreviewState
                />
              </div>
            ))}
            {teams?.data?.length > 2 && (
              <Button
                onClick={() => {
                  setShowModalHandler('ViewTeamModal');
                  handleQuery({
                    project_ref: projectId,
                    viewTeams: 'view_teams',
                  });
                }}
                className="group h-[30px] min-h-[30px] w-full max-w-[154px] whitespace-nowrap border-[1px] border-primary bg-transparent py-0.5 text-xs text-primary hover:border-primary hover:bg-primary hover:text-white"
              >
                See All Teams
              </Button>
            )}
          </>
        ) : (
          <div className="mb-6 flex items-center gap-x-10 pt-4 text-[14px]">
            <p className="">No team yet</p>
            {isCreatorAtProjectLevel && (
              <div className="group relative">
                <DialogWithTooltip
                  Icon={
                    <AddIcon
                      onClick={() =>
                        navigate(
                          `/${currentAccountType}/dashboard-projects/${projectRef}/add-team-to-project`,
                        )
                      }
                      className="cursor-pointer"
                    />
                  }
                  title="Add team"
                />
              </div>
            )}
          </div>
        )}
        <ViewTeamModal />
      </div>
    </TooltipProvider>
  );
}
