import { useMemo, useState } from 'react';
import { Download, FileText } from 'lucide-react';
import { noImagePlaceholder } from '../../../../../../../assets/images';
import { useAppContext } from '@/context/event/AppEventContext';
import { ConversationHelper } from '@/features/Conversation/utils/helper';
import { ToolTipWrapper } from '@/components/ui/TooltipWidget/TooltipWidget';
import {
  DeleteIcon2,
  DeleteIconCustom,
  DocIcon,
  PdfIcon,
} from '@/assets/icons';
import Player from '@/components/ui/WatchWidget/player';
const DownloadIconTwo = ({ className }: { className: string }) => (
  <Download className={className} />
);

interface ProjectAttachment {
  fileRef: string;
  url: string;
  name: string;
  size: string;
  createdAt: string;
}

interface ProjectFilesTabProps {
  projectName: string;
  videoUrls: ProjectAttachment[];
  documents: ProjectAttachment[];
  isCreatorAtProjectLevel?: boolean;
  isLoggedInUserAProjectAdmin?: boolean;
  handleFileDelete: (attachment: ProjectAttachment, type: string) => void;
}

export function ProjectFilesTab({
  projectName,
  videoUrls = [],
  documents = [],
  isCreatorAtProjectLevel = false,
  isLoggedInUserAProjectAdmin = false,
  handleFileDelete,
}: ProjectFilesTabProps) {
  const { setShowModalHandler } = useAppContext();

  // Combine all files for Download All
  const allFiles = useMemo(() => {
    return [...videoUrls, ...documents];
  }, [videoUrls, documents]);

  // Separate files by type
  const imageFiles = documents.filter(file =>
    ConversationHelper.isImage(file.url),
  );

  const docFiles = documents.filter(
    file =>
      ConversationHelper.isPdf(file.url) || ConversationHelper.isDoc(file.url),
  );

  const hasFiles = allFiles.length > 0;

  return (
    <div className="w-full rounded-lg border border-gray-200 p-4">
      {hasFiles && (
        <div className="flex flex-col gap-6">
          {/* Download All Button - Matches design with loading state */}
          {/* <button
            className="border-1 flex w-fit items-center gap-2 rounded-lg border border-primary bg-transparent px-6 py-2.5 text-sm font-medium leading-none text-primary transition-all duration-200 hover:bg-primary hover:text-white disabled:cursor-not-allowed disabled:opacity-50"
            onClick={handleDownloadAll}
            disabled
          >
            {isDownloading ? (
              <>
                <Spinner className="w-4 h-4" />
                {downloadProgress > 0
                  ? `Downloading... ${Math.round(downloadProgress)}%`
                  : 'Preparing...'}
              </>
            ) : (
              <>
                Download All
                <DownloadIconTwo className="w-4 h-4" />
              </>
            )}
          </button> */}

          {/* Images/Videos Grid - 3-column layout */}
          {imageFiles && imageFiles.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {imageFiles.map((file, idx) => {
                const [imgLoaded, setImgLoaded] = useState(false);
                return (
                  <div
                    key={file.fileRef || idx}
                    className="group relative aspect-square h-[83px] w-[108px] overflow-hidden"
                  >
                    {!imgLoaded && (
                      <div className="absolute inset-0 z-10 animate-pulse bg-gray-200" />
                    )}
                    <img
                      src={file.url || noImagePlaceholder}
                      alt={file.name || 'Image file'}
                      className={`h-full w-full object-cover text-xs transition-opacity duration-300 ${imgLoaded ? 'opacity-100' : 'opacity-0'}`}
                      onLoad={() => setImgLoaded(true)}
                    />
                    {/* Download Button - Always visible, visible color */}
                    {imgLoaded && (
                      <div className="absolute right-1 top-1 z-40 flex items-center justify-center gap-1">
                        <ToolTipWrapper
                          text="Download"
                          className="group relative flex h-5 w-5 cursor-pointer items-center justify-center p-[2px] group-hover:bg-white"
                        >
                          <a
                            href={file.url}
                            download={file.url.split('/')[-1]}
                            rel={'noopener noreferrer'}
                            className="h-full"
                          >
                            <DownloadIconTwo className="h-3 w-3 text-white group-hover:text-black" />
                          </a>
                        </ToolTipWrapper>
                        {/* Delete Button - Hidden but logic preserved */}
                        {(isCreatorAtProjectLevel ||
                          isLoggedInUserAProjectAdmin) && (
                          <ToolTipWrapper
                            text="Delete"
                            className="group relative flex h-5 w-5 cursor-pointer items-center justify-center  p-[2px] group-hover:bg-red-500"
                          >
                            <button
                              type="button"
                              className="disabled:cursor-not-allowed disabled:opacity-50"
                              onClick={() => handleFileDelete(file, 'file')}
                            >
                              <DeleteIconCustom className="h-3 w-3 text-white" />
                            </button>
                          </ToolTipWrapper>
                        )}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}

          {videoUrls &&
            videoUrls.length > 0 &&
            videoUrls.map((file, idx) => {
              return (
                <div
                  key={file.fileRef || idx}
                  className="flex w-full max-w-[245px] flex-col justify-center bg-white p-4"
                >
                  <div className="group/first relative mb-2.5 h-[130px] rounded-[4px] bg-white max-md:w-full">
                    <Player
                      url={videoUrls?.[videoUrls?.length - 1]?.url || ''}
                    />
                    <div className="absolute right-1 top-1 z-[1] flex items-center gap-1 opacity-0 duration-500 ease-in-out group-hover/first:opacity-100">
                      {/* <ToolTipWrapper
                        className="group relative h-5 w-5 cursor-pointer bg-white p-[2px]"
                        text="Download Video"
                      >
                        <button
                          onClick={() => handleSingleFileDownload(file)}
                          disabled={downloadingFiles.has(
                            file.fileRef || file.url,
                          )}
                          className="disabled:cursor-not-allowed disabled:opacity-50"
                        >
                          {downloadingFiles.has(file.fileRef || file.url) ? (
                            <Spinner className="w-4 h-4" />
                          ) : (
                            <DownloadIconTwo className="w-4 h-4" />
                          )}
                        </button>
                      </ToolTipWrapper> */}
                      <ToolTipWrapper
                        className="group relative h-5 w-5 cursor-pointer bg-white p-[2px]"
                        text="Delete File"
                      >
                        <span
                          onClick={() =>
                            handleFileDelete(
                              videoUrls?.[videoUrls?.length - 1],
                              'video',
                            )
                          }
                        >
                          <DeleteIcon2 className="h-4 w-4" />
                        </span>
                      </ToolTipWrapper>
                    </div>
                  </div>{' '}
                  <p className="mb-0 text-center text-[14px] font-medium">
                    {projectName} video pitch
                  </p>
                </div>
              );
            })}

          {/* Documents List - Horizontal cards matching design */}
          {docFiles.length > 0 && (
            <div className="space-y-3">
              {docFiles.map(attachment => (
                <div
                  key={attachment.fileRef}
                  className="group flex h-[45px] w-fit items-center gap-4 rounded-lg bg-[#EAEAEA] p-4 transition-colors duration-200 hover:bg-gray-100"
                >
                  {/* File Type Indicator - Matches design */}
                  <div className="flex h-[26px] w-[22px] items-center justify-center rounded bg-red-100 text-xs font-medium text-red-600">
                    {ConversationHelper.isPdf(attachment.url) ? (
                      <PdfIcon />
                    ) : (
                      <DocIcon />
                    )}
                  </div>
                  {/* File Info */}
                  <div className="min-w-0 flex-1">
                    <p className="truncate text-sm font-normal text-gray-900">
                      {attachment.name}
                    </p>
                    {attachment.size && (
                      <p className="hidden text-xs text-gray-500">
                        {attachment.size}
                      </p>
                    )}
                  </div>
                  {/* Download Button */}
                  <ToolTipWrapper text="Download">
                    <a
                      href={attachment.url}
                      download={attachment.url.split('/')[-1]}
                      rel={'noopener noreferrer'}
                      className="flex h-6 w-6 items-center justify-center rounded bg-gray-200 text-subText transition-colors duration-200"
                    >
                      <DownloadIconTwo className="h-3 w-3" />
                    </a>
                  </ToolTipWrapper>
                  {/* Delete Button - Hidden but logic preserved */}
                  {(isCreatorAtProjectLevel || isLoggedInUserAProjectAdmin) && (
                    <ToolTipWrapper text="Delete">
                      <button
                        className="flex h-6 w-6 items-center justify-center rounded bg-red-100 text-red-500 transition-colors duration-200"
                        onClick={() => handleFileDelete(attachment, 'file')}
                      >
                        <DeleteIconCustom className="h-3 w-3" />
                      </button>
                    </ToolTipWrapper>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      )}
      {/* Empty State - Clean and minimal */}
      {!hasFiles && (
        <div className="py-16 text-center">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gray-100">
            <FileText className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className="mb-2 text-lg font-medium text-gray-900">
            No files yet
          </h3>
          <p className="mb-6 text-gray-500">Upload files to get started</p>
          {isLoggedInUserAProjectAdmin && (
            <ToolTipWrapper text="Add Files">
              <button
                className="rounded border border-gray-300 px-4 py-2 text-gray-700 transition-colors duration-200 hover:bg-gray-50"
                onClick={() =>
                  setShowModalHandler('UpdateProjectAttachmentModal')
                }
              >
                Add Files
              </button>
            </ToolTipWrapper>
          )}
        </div>
      )}
    </div>
  );
}
