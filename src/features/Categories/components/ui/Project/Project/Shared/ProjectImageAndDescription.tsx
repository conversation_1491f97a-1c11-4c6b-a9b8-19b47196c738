import { useAppContext } from '@/context/event/AppEventContext';
import ProjectCategoryCard from '../../../../../../../components/ui/CategoryWidget/Project/ProjectCategoryCard';
import ProjectDescription from '../../../../../../../components/ui/CategoryWidget/Project/ProjectDescription';
import { Project } from '../../../../../types';
import { UpdateProjectImageModal } from '../../../Modals/UpdateProjectImageModal';
import { PencilIcon } from 'lucide-react';
import useGetProjectLevelBasedAccess from '@/hooks/useGetProjectLevelBasedAccess';

type Props = { project: Project };

export default function ProjectImageAndDescription({ project }: Props) {
  const { setShowModalHandler } = useAppContext();
  const { isLoggedInUserAProjectAdmin, isCreatorAtProjectLevel } =
    useGetProjectLevelBasedAccess();

  return (
    <div className="mb-6 flex gap-12 max-sm:flex-col">
      <div>
        <div className=" mb-2 mt-3 w-full max-w-[340px] self-start truncate break-all text-base font-bold capitalize text-stone-950">
          {project?.projectName}
        </div>
        <div className="mb-2">
          <ProjectCategoryCard
            project={{
              ...project,
              projectImageUrl: project?.projectImageUrl || '',
              projectName: project?.projectName || 'N/A',
              projectRef: project?.projectRef || '',
            }}
            key={project?.projectRef}
            hideToolTip
          >
            {(isCreatorAtProjectLevel || isLoggedInUserAProjectAdmin) && (
              <p
                onClick={e => {
                  e.preventDefault();
                  e.stopPropagation();
                  setShowModalHandler('UpdateProjectImageModal');
                }}
                className="group mt-2 flex cursor-pointer justify-center gap-2 text-center text-[15px] text-grayTen transition duration-500 ease-in-out hover:text-primary"
              >
                Edit photo
                <PencilIcon className="inline stroke-grayTen transition duration-500 ease-in-out group-hover:stroke-primary" />
              </p>
            )}
          </ProjectCategoryCard>
        </div>
      </div>
      <div className="flex-grow-2">
        <ProjectDescription description={project?.projectDescription} />
      </div>
      <UpdateProjectImageModal />
    </div>
  );
}
