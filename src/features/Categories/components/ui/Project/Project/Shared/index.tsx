import { useMemo } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { useNavigate, useParams } from 'react-router-dom';
import { useState, useEffect } from 'react';

import {
  DuplicateTaskModal,
  GoalsListPresenceContainer,
  MoveTaskToObjectiveModal,
} from '@/features/projectManagementGoals';

import { notFound } from '../../../../../../../assets/images';
import ProblemStatementDescriptions from '../../../../../../../components/ui/CategoryWidget/ProblemStatement/ProblemStatementDescriptions';
import CreatorWidget from '../../../../../../../components/ui/CommonWidget/CreatorWidget';
import { Spinner } from '../../../../../../../components/ui/CommonWidget/Loader';
import Player from '../../../../../../../components/ui/WatchWidget/player';
import { useAppContext } from '../../../../../../../context/event/AppEventContext';
import {
  useGetProblemStatement,
  useGetSDGs,
} from '../../../../../../../hooks/apiQueryHooks/eduQueryHooks';
import useGetProjectLevelBasedAccess from '../../../../../../../hooks/useGetProjectLevelBasedAccess';
import { useHandleQueryParams } from '../../../../../../../hooks/useHandleQueryParams';
import {
  ViewAndEditTaskCard,
  ViewAndEditTaskPresenceContainer,
} from '../../../../../../projectManagementGoals';
import { useGetProject } from '../../../../../hooks/apiQueryHooks/eduQueryHooks';
import TeamsPreview from './TeamsPreview';
import { DeleteProjectModal } from '@/features/projectManagementGoals';
import { ProjectObjectiveList } from '@/features/projectManagementGoals';
import { AddOrEditObjectivesModal } from '@/features/projectManagementGoals';
import { DeleteObjectiveModal } from '@/features/projectManagementGoals';
import Button from '@/components/ui/ButtonComponent';
import { AddProblemStatementToProjectModal } from '@/features/projectManagementGoals';
import {
  ProjectAttachment,
  ProjectStatusValues,
  VisibilityType,
} from '@/types';
import { DeleteProjectFileModal } from '@/features/projectManagementGoals';
import { DeleteTaskModal } from '@/features/projectManagementGoals';
import TabbedProjectDetails, { TabKey } from './TabbedProjectDetails';
import ProjectDescription from '@/components/ui/CategoryWidget/Project/ProjectDescription';
import { countryFlags } from '@/utils/helpers/country-flags-data';
import { ProjectFilesTab } from './FilesTab';
import UpdateProjectVisibilityModal from '../../../Modals/UpdateProjectVisibilityModal';
import { GoalsHelper } from '@/features/projectManagementGoals/utils/helper';
import { X } from 'lucide-react';
import { cn } from '@/lib/twMerge/cn';
import { useUpdateProject } from '@/features/Categories/hooks/apiQueryHooks/eduQueryHooks';
import { useCustomToast } from '@/hooks/useToast';

export function SharedProject({
  header: Header,
}: {
  header: React.FC<{
    projectName: string;
    visibility: VisibilityType;
    projectStatus?: ProjectStatusValues;
  }>;
}) {
  const { successToast } = useCustomToast();
  const { projectRef } = useParams();
  const { query, handleQuery } = useHandleQueryParams();
  const parameterProjectRef = query.get('project_ref');
  const ref = projectRef || parameterProjectRef;
  const navigate = useNavigate();
  const {
    isCreatorAtProjectLevel,
    isLoggedInUserAProjectAdmin,
    isLoggedInUserAProjectMember,
  } = useGetProjectLevelBasedAccess();
  const { setShowModalHandler, showModal } = useAppContext();
  const {
    data: project,
    isLoading,
    refetch: refetchProject,
  } = useGetProject(projectRef || parameterProjectRef || '');

  const { data: problemStatement } = useGetProblemStatement(
    project?.data?.problemStatementRef || '',
    {
      enabled: !!project?.data?.problemStatementRef,
    },
  );

  const videoUrls = useMemo(() => {
    return project?.data?.descriptionVideoUrls || [];
  }, [project]);

  const documents = useMemo(() => {
    return project?.data?.documents || [];
  }, [project]);

  const handleFileDelete = (attachment: ProjectAttachment, type: string) => {
    handleQuery({
      type,
      project_ref: projectRef,
      file_name: attachment?.name,
      attachement_ref: attachment?.fileRef,
    });
    setShowModalHandler('DeleteProjectFileModal');
  };
  const location = typeof window !== 'undefined' ? window.location : undefined;
  const getInitialTab = () => {
    if (!location) return 'description';
    const params = new URLSearchParams(location.search);
    return (params.get('tab') as TabKey) || 'description';
  };
  const [activeTab, setActiveTab] = useState<TabKey>(getInitialTab());
  useEffect(() => {
    const params = new URLSearchParams(location?.search || '');
    const urlTab = (params.get('tab') as TabKey) || 'description';
    if (urlTab !== activeTab) setActiveTab(urlTab);
    // eslint-disable-next-line
  }, [location?.search]);

  // Sidebar data
  const projectImage = project?.data?.projectImageUrl || notFound;
  const projectLevel = project?.data?.projectLevel || 'N/A';
  const projectCountries = project?.data?.projectCountries || [];
  const categoryRefs = project?.data?.categoryRefs || [];

  const { data: sdgs, isLoading: isLoadingSdgs } = useGetSDGs({
    staleTime: 1000 * 60 * 60 * 24,
    cacheTime: 1000 * 60 * 60 * 25,
  });

  const sdgOptions = GoalsHelper.createCategoryOptionsArr(
    sdgs?.data || [],
  ).filter(item => categoryRefs.includes(item.value));

  const [deletingCountry, setDeletingCountry] = useState<string | null>(null);
  const [deletingIndustry, setDeletingIndustry] = useState<string | null>(null);

  // Mutation for updating project (single delete)
  const updateProjectMutation = useUpdateProject({
    onSuccess: () => {
      refetchProject();
    },
  });

  const handleDeleteCountry = async (country: string) => {
    setDeletingCountry(country);
    const updatedCountries = projectCountries.filter(
      (c: string) => c !== country,
    );
    const payload = {
      projectCountries: updatedCountries,
      sdgCategoryRefs: categoryRefs,
      projectRef: project?.data?.projectRef,
    };
    await updateProjectMutation.mutateAsync(payload);
    successToast('Country deleted successfully');
    setDeletingCountry(null);
  };

  const handleDeleteIndustry = async (industry: string) => {
    setDeletingIndustry(industry);
    const updatedIndustries = categoryRefs.filter(
      (c: string) => c !== industry,
    );
    const payload = {
      projectCountries: projectCountries,
      sdgCategoryRefs: updatedIndustries,
      projectRef: project?.data?.projectRef,
    };
    await updateProjectMutation.mutateAsync(payload);
    successToast('Industry deleted successfully');
    setDeletingIndustry(null);
  };

  if (isLoading)
    return (
      <div className="flex h-[400px] w-full items-center justify-center">
        <Spinner className="" />
      </div>
    );

  return (
    <div className="flex min-w-0 flex-1 flex-col">
      {/* Header Row */}
      <div className="mb-2 flex flex-col gap-2 md:flex-row md:items-center md:justify-between">
        <div className="flex min-w-0 flex-col items-start">
          <Header
            projectStatus={project?.data?.projectStatus}
            projectName={project?.data?.projectName || 'N/A'}
            visibility={project?.data.visibility || 'PRIVATE'}
          />
          <CreatorWidget createdBy={project?.data?.createdByName || ''} />
        </div>
      </div>
      {/* Tabs and Edit Button Row */}
      <div className="flex-1">
        <TabbedProjectDetails activeTab={activeTab} onTabChange={setActiveTab}>
          {activeTab === 'description' && (
            <section className="flex max-h-[calc(100vh-220px)] flex-col items-start gap-4 overflow-y-auto pb-16 md:flex-row">
              {/* Main Description Content */}
              <div className="w-full min-w-0 flex-1">
                {project ? (
                  <ProjectDescription
                    hideEdit
                    title={
                      <h5 className="text-sm font-semibold text-gray-900 sm:text-base">
                        Project Description
                      </h5>
                    }
                    description={project?.data?.projectDescription}
                  />
                ) : (
                  <div className="flex h-[100px] w-full items-center justify-center">
                    <Spinner />
                  </div>
                )}
              </div>
              {/* Sidebar (only in Description tab) */}
              <aside className="w-full flex-shrink-0 md:w-[308px]">
                <div className="sticky top-4">
                  <div className="flex flex-col gap-6 rounded-lg border border-[#D9D9D9] p-4">
                    {/* Project Image */}
                    <div className="flex h-[200px] w-[276px] justify-center">
                      <img
                        src={projectImage}
                        alt="Project"
                        className="h-full w-full rounded-md border object-cover"
                      />
                    </div>

                    {/* Project Level */}
                    <div className="space-y-3">
                      <h3 className="text-sm font-medium text-gray-900">
                        Minimum Project Level
                      </h3>
                      <div className="rounded-lg border p-2 text-sm font-medium">
                        {projectLevel}
                      </div>
                    </div>

                    {/* Focus Countries */}
                    <div className="space-y-3">
                      <h3 className="text-sm font-medium text-gray-900">
                        Focus Countries
                      </h3>
                      <div className="flex flex-wrap gap-2 rounded-[10px] border border-[#D9D9D9] p-2">
                        {projectCountries.length > 0 ? (
                          projectCountries.map((country: string) => (
                            <div
                              key={country}
                              className={cn(
                                'flex w-fit items-center justify-between gap-2 rounded-lg border border-[#D9D9D9] px-1.5 py-1',
                                deletingCountry === country &&
                                  'pointer-events-none opacity-50',
                              )}
                            >
                              <div className="flex items-center gap-2">
                                <span className="text-sm">
                                  {countryFlags[country] || '🌍'}
                                </span>
                                <span className="text-sm font-medium text-gray-700">
                                  {country}
                                </span>
                              </div>
                              {projectCountries.length > 1 && (
                                <button
                                  className="flex items-center justify-center rounded-full bg-black p-[2px] text-white"
                                  onClick={() => handleDeleteCountry(country)}
                                  disabled={
                                    deletingCountry === country ||
                                    updateProjectMutation.isPending
                                  }
                                >
                                  {deletingCountry === country &&
                                  updateProjectMutation.isPending ? (
                                    <Spinner className="h-3 w-3" />
                                  ) : (
                                    <X className="h-3 w-3" />
                                  )}
                                  <span className="sr-only">
                                    Remove {country}
                                  </span>
                                </button>
                              )}
                            </div>
                          ))
                        ) : (
                          <p className="text-sm italic text-gray-400">
                            No countries selected
                          </p>
                        )}
                      </div>
                    </div>

                    {/* Focus Industries */}
                    <div className="space-y-3">
                      <h3 className="text-sm font-medium text-gray-900">
                        Focus Industries
                      </h3>
                      <div className="flex flex-wrap gap-2 rounded-[10px] border border-[#D9D9D9] p-2">
                        {isLoadingSdgs ? (
                          Array.from({ length: 3 }).map((_, index) => (
                            <div
                              key={index}
                              className="h-10 w-28 rounded-lg bg-gray-200"
                            />
                          ))
                        ) : sdgOptions.length > 0 ? (
                          sdgOptions.map(
                            (cat: { label: string; value: string }) => (
                              <div
                                key={cat.value}
                                className={cn(
                                  'flex w-fit items-center justify-between gap-2 rounded-lg border border-[#D9D9D9] px-1.5 py-1 font-spartan',
                                  deletingIndustry === cat.value &&
                                    'pointer-events-none opacity-50',
                                )}
                              >
                                <span className="line-clamp-1 text-sm font-medium">
                                  {cat.label}
                                </span>
                                {sdgOptions.length > 1 && (
                                  <button
                                    className="flex items-center justify-center rounded-full bg-black p-[2px]"
                                    onClick={() =>
                                      handleDeleteIndustry(cat.value)
                                    }
                                    disabled={
                                      deletingIndustry === cat.value ||
                                      updateProjectMutation.isPending
                                    }
                                  >
                                    {deletingIndustry === cat.value &&
                                    updateProjectMutation.isPending ? (
                                      <Spinner className="h-3 w-3 text-white" />
                                    ) : (
                                      <X className="h-3 w-3 text-white" />
                                    )}
                                    <span className="sr-only">
                                      Remove {cat.label}
                                    </span>
                                  </button>
                                )}
                              </div>
                            ),
                          )
                        ) : (
                          <p className="text-sm italic text-gray-400">
                            No industries selected
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </aside>
            </section>
          )}
          {activeTab === 'teams' && (
            <div className="w-full max-w-[525px] bg-white p-6 shadow-md">
              <div className="overflow-auto">
                <div id="project-teams" className="max-h-[400px]">
                  <TeamsPreview />
                </div>
              </div>
            </div>
          )}
          {activeTab === 'files' && (
            <>
              <ProjectFilesTab
                projectName={project?.data?.projectName || ''}
                videoUrls={videoUrls}
                documents={documents}
                isCreatorAtProjectLevel={isCreatorAtProjectLevel}
                isLoggedInUserAProjectAdmin={isLoggedInUserAProjectAdmin}
                handleFileDelete={handleFileDelete}
              />
            </>
          )}
          {activeTab === 'tasks' && isLoggedInUserAProjectMember && (
            <div className="flex flex-col gap-2">
              <GoalsListPresenceContainer className="flex-1 !px-0">
                <ProjectObjectiveList projectRef={ref || ''} />
                <motion.span
                  initial={{ opacity: 0, x: '-30px' }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.5, type: 'tween' }}
                  className="my-4 flex cursor-pointer items-center text-[12px] max-xs:justify-between max-xs:text-[10px] xs:gap-x-3 xs:px-8 sm:gap-x-6"
                >
                  <p
                    onClick={() => {
                      handleQuery({
                        project_ref: projectRef,
                      });
                      setShowModalHandler('AddOrEditObjectiveModal');
                    }}
                    className="duration-200 ease-out hover:text-primary"
                  >
                    Add Objective
                  </p>
                </motion.span>
              </GoalsListPresenceContainer>
              <ViewAndEditTaskPresenceContainer>
                <ViewAndEditTaskCard />
              </ViewAndEditTaskPresenceContainer>
            </div>
          )}
          {activeTab === 'problem' && (
            <div className="w-full">
              {problemStatement ? (
                <>
                  <div className="mb-4 text-[16px] leading-[27px]">
                    <div className="mb-4 flex w-full items-center justify-between">
                      <span className="font-semibold">Problem statement</span>
                    </div>
                    <span className="text-primary">
                      {problemStatement?.data.title}
                      {problemStatement?.data.title}
                    </span>
                  </div>
                  <div className="relative aspect-[2] max-w-[502px] rounded-[10px]">
                    <Player url={problemStatement?.data?.descVidUrl || ''} />
                  </div>
                  <ProblemStatementDescriptions
                    description={problemStatement?.data?.description}
                    fileTitle={problemStatement?.data.title || ''}
                    fileUrl={problemStatement?.data.descDocUrl || ''}
                  />
                </>
              ) : (
                <div
                  className={` whitespace-nowrap rounded-sm border
                      border-dashed border-grayNine bg-[#FCFBFF] bg-opacity-10 p-6  max-md:px-5`}
                >
                  <h3 className="mb-4 font-semibold">Problem statement</h3>
                  <div className="flex items-center justify-center px-16 text-center text-sm text-neutral-600">
                    <div>
                      <img
                        src={notFound}
                        alt=" not found"
                        className="mx-auto mb-[2rem]"
                      />
                      <div className="mb-4 text-center text-sm font-medium text-neutral-600">
                        {'No problem statement added to this project'}
                      </div>
                      {isLoggedInUserAProjectAdmin && (
                        <Button
                          onClick={() =>
                            navigate('add-problem-statement-to-project')
                          }
                          className="group w-full min-w-[154px] whitespace-nowrap border-[1px] border-grayTen bg-transparent hover:border-primary  hover:bg-primary hover:text-white"
                        >
                          Add a problem statement
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
          <DeleteProjectFileModal />
          <DeleteObjectiveModal />
          <AddOrEditObjectivesModal />
          <DeleteTaskModal />
          <AddProblemStatementToProjectModal />
          <AnimatePresence>
            {showModal === 'DeleteProjectModal' && <DeleteProjectModal />}
          </AnimatePresence>
          <UpdateProjectVisibilityModal />
          <DuplicateTaskModal />
          <MoveTaskToObjectiveModal />
        </TabbedProjectDetails>
      </div>
    </div>
  );
}
