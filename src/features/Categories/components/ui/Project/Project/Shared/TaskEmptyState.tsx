import { notFound } from '../../../../../../../assets/images';
import Button from '../../../../../../../components/ui/ButtonComponent';
import { useAppContext } from '../../../../../../../context/event/AppEventContext';

export default function TaskEmptyState() {
  const { setShowModalHandler } = useAppContext();

  const redirect = () => setShowModalHandler('AddTaskModal');

  return (
    <div
      className={`whitespace-nowrap rounded-sm border
  border-dashed border-grayNine bg-[#FCFBFF] bg-opacity-10 p-6  max-md:px-5`}
    >
      <div className="flex items-center justify-center px-16  text-center text-sm text-neutral-600">
        <div>
          <img src={notFound} alt=" not found" className="mx-auto mb-[2rem]" />
          <div className="mb-4 text-center text-sm font-medium text-neutral-600">
            {'No task yet'}
          </div>
          <div className="mx-auto my-auto mt-auto w-full rounded-[5px] border border-solid border-black text-[16px] hover:border-[color:transparent]">
            <Button
              onClick={() => redirect()}
              className="w-full bg-transparent text-black hover:bg-primary hover:text-white "
            >
              <p>{'Create A Task'}</p>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
