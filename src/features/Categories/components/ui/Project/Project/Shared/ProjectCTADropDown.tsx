import { useNavigate, useParams } from 'react-router-dom';

import { useAppContext } from '@/context/event/AppEventContext';
import { useHandleQueryParams } from '@/hooks/useHandleQueryParams';
import useGetProjectLevelBasedAccess from '@/hooks/useGetProjectLevelBasedAccess';

export default function ProjectCTADropDown() {
  const navigate = useNavigate();
  const { projectRef } = useParams();
  const { currentAccountType, setShowModalHandler } = useAppContext();
  const { handleQuery } = useHandleQueryParams();

  const { isLoggedInUserAProjectAdmin } = useGetProjectLevelBasedAccess();

  return (
    <div className="relative min-h-[100px] w-[300px] rounded-[8px] bg-white py-4 text-black shadow-lg">
      <ul>
        <li
          onClick={() => {
            handleQuery({
              project_ref: projectRef,
            });
            setShowModalHandler('AddOrEditObjectiveModal');
          }}
          className="mb-2 cursor-pointer border-b-black px-4 py-1 hover:bg-gray-5 hover:text-primary"
        >
          Add Objective
        </li>
        {isLoggedInUserAProjectAdmin && (
          <>
            <li
              onClick={() =>
                navigate(
                  `/${currentAccountType}/dashboard-projects/${projectRef}/add-team-to-project`,
                )
              }
              className="mb-2 cursor-pointer border-b-black px-4 py-1 hover:bg-gray-5 hover:text-primary"
            >
              Add Project Team
            </li>
            <li
              onClick={() => setShowModalHandler('DeleteProjectModal')}
              className="mb-2 cursor-pointer border-b-black px-4 py-1 hover:bg-gray-5 hover:text-primary"
            >
              Delete Project
            </li>
          </>
        )}
      </ul>
    </div>
  );
}
