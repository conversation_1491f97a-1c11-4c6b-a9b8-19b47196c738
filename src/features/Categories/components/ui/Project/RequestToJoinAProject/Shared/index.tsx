import { useNavigate, useParams } from 'react-router-dom';

import { ArrowBackIcon } from '../../../../../../../assets/icons';

import {
  InviteMyTeamToAvailableProjectResourceLoader,
  ShouldProceedToAddTeamToProjectModal,
} from '@/features/Teams';

import { useHandleQueryParams } from '@/hooks/useHandleQueryParams';
import { useAppContext } from '../../../../../../../context/event/AppEventContext';

export function SharedRequestToJoinAProject() {
  const navigate = useNavigate();
  const { query } = useHandleQueryParams();
  const { currentAccountType } = useAppContext();
  const { projectRef } = useParams();
  return (
    <>
      <div>
        <div className="mb-4 flex items-center gap-x-3">
          <ArrowBackIcon
            className="cursor-pointer"
            onClick={() =>
              navigate(
                `/${currentAccountType}/dashboard-projects/${projectRef}`,
              )
            }
          />
          <header className="self-stretch text-[20px] font-semibold leading-10 tracking-tight text-neutral-800">
            Select from your team to join project {query.get('projectName')}
          </header>
        </div>
        <InviteMyTeamToAvailableProjectResourceLoader />
      </div>
      <ShouldProceedToAddTeamToProjectModal />
    </>
  );
}
