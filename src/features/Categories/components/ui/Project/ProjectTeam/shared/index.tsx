import { Spinner } from '@/components/ui/CommonWidget/Loader';
import { useHandleQueryParams } from '@/hooks/useHandleQueryParams';
import { useNavigate, useParams } from 'react-router-dom';
import DetailedTeamMemberContainer from './DetailedProjectMemberContainer';
import { useGetTeamMembers } from '@/features/Teams';
import { ArrowRightTailless } from '@/assets/icons';
import { useAppContext } from '@/context/event/AppEventContext';
import { useGetTeam } from '@/features/Teams/hooks/apiQueryhooks/eduQueryHooks';
import { HashLink } from '@/components/ui/HashLink/HasLink';

export default function SharedProjectTeam() {
  const { currentAccountType } = useAppContext();
  const navigate = useNavigate();
  const { teamRef: teamRefParam } = useParams();
  const { projectRef } = useParams();
  const { query } = useHandleQueryParams();

  const teamRef = teamRefParam || query.get('team_ref');

  const { data: teamMembers, isLoading: isLoadingMember } = useGetTeamMembers({
    teamRef: teamRef || '',
    inviteStatus: 'JOINED',
  });

  const { data: team } = useGetTeam(teamRef || query.get('team_ref') || '');

  if (isLoadingMember)
    return (
      <div className="flex h-[400px] w-full items-center justify-center">
        <Spinner />
      </div>
    );
  return (
    <>
      <div className="flex flex-wrap items-center gap-x-1 py-4">
        <p
          onClick={() => navigate(`/${currentAccountType}/dashboard-projects`)}
          className={` cursor-pointer`}
        >
          Explore Projects
        </p>
        <>
          <ArrowRightTailless className="h-[24px] w-[24px] fill-black" />
        </>
        <HashLink
          to={`/${currentAccountType}/dashboard-projects/${projectRef}/#project-teams`}
          className={` cursor-pointer`}
        >
          Project Teams
        </HashLink>
        <>
          <ArrowRightTailless className="h-[24px] w-[24px] fill-black" />
        </>
        <p className="text-primary">{team?.data?.teamName}</p>
      </div>
      {teamMembers?.data && teamMembers?.data.length > 0 ? (
        <DetailedTeamMemberContainer teamMembers={teamMembers?.data || []} />
      ) : (
        <div>
          <p className="mb-4 text-center">No team members found</p>
        </div>
      )}
    </>
  );
}
