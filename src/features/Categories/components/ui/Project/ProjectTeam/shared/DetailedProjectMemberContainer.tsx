import { DetailedProjectMemberCard } from '@/components/ui/CategoryWidget/Project/DetailedProjectMemberCard';
import { ExtractData, ServerTeamMembers } from '@/types';

type Props = {
  teamMembers: ExtractData<ServerTeamMembers>;
};

export default function DetailedProjectMemberContainer({ teamMembers }: Props) {
  return (
    <div className="flex flex-wrap gap-3 max-xl:justify-center max-md:flex-col max-md:items-stretch">
      {teamMembers.map((member, index) => (
        <DetailedProjectMemberCard
          userId={member.userId}
          key={member.userDTO.firstName + index}
          teamRole={member.teamRole}
          firstName={member.userDTO.firstName}
          lastName={member.userDTO.lastName}
        />
      ))}
    </div>
  );
}
