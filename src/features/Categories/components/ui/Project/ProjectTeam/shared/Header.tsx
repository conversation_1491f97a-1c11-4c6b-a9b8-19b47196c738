import CommonHeaderWithBackArrow from '@/components/ui/CommonWidget/CommonHeaderWithBackArrow';
import { useAppContext } from '@/context/event/AppEventContext';
import { useGetProject } from '@/features/Categories/hooks/apiQueryHooks/eduQueryHooks';
import { useParams } from 'react-router-dom';

export default function Header() {
  const { projectRef } = useParams();
  const { currentAccountType } = useAppContext();
  const { data: project } = useGetProject(projectRef || '', {
    enabled: !!projectRef,
  });
  return (
    <div className="mb-4">
      <CommonHeaderWithBackArrow
        route={`/${currentAccountType}/dashboard-projects/${projectRef}}`}
      >
        <h4 className="max-w-[788px] text-[20px] font-[500] text-black">
          {project?.data?.projectName}
        </h4>
      </CommonHeaderWithBackArrow>
    </div>
  );
}
