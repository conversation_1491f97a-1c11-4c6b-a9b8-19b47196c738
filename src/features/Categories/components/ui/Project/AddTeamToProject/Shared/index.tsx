import { useNavigate, useParams } from 'react-router-dom';

import { ArrowBackIcon } from '../../../../../../../assets/icons';
import { useAppContext } from '../../../../../../../context/event/AppEventContext';
import {
  SelectMyTeamToProjectResourceLoader,
  InviteExternalTeamToProjectResourceLoader,
} from '../../../../../../Teams';

export function SharedAddTeamToProject() {
  const navigate = useNavigate();
  const { currentAccountType } = useAppContext();
  const { projectRef } = useParams();
  return (
    <>
      <div>
        <div className="mb-4 flex items-center gap-x-3">
          <ArrowBackIcon
            className="cursor-pointer"
            onClick={() =>
              navigate(
                `/${currentAccountType}/dashboard-projects/${projectRef}`,
              )
            }
          />
          <header
            className={`self-stretch text-[20px] font-semibold capitalize leading-10 
          tracking-tight text-neutral-800`}
          >
            Internal Teams
          </header>
        </div>
        <SelectMyTeamToProjectResourceLoader />
      </div>
      <div className="mt-6 sm:mt-10">
        <header
          className={`mb-4 self-stretch text-[20px] font-semibold capitalize 
          leading-10 tracking-tight text-neutral-800 sm:px-8`}
        >
          External Teams
        </header>
        <InviteExternalTeamToProjectResourceLoader />
      </div>
    </>
  );
}
