import { UseQueryResult } from '@tanstack/react-query';

import { DataResourceLoader } from '../../../../components/resourceLoader/dataResourceLoader';
import { SeeMoreProjectButton } from '../../../../components/ui/CategoryWidget/Project/ProjectCategoryCard';
import { ServerProjects } from '../../../../types';
import CommonDashedBorderBox from '@/components/ui/CommonWidget/CommonDashedBorderBox';
import { notFound } from '@/assets/images';
import { useHandleQueryParams } from '@/hooks/useHandleQueryParams';
import { useAppContext } from '@/context/event/AppEventContext';
import AllProjectByType from '../ui/Project/Projects/Shared/AllProjectByType';
import { usePaginationHandler } from '@/hooks/usePaginationHandler';

export default function AllProjectByTypeResourceLoader({
  useGetAllProjectsByType,
}: {
  useGetAllProjectsByType: (
    parameter?: {},
    options?: {},
  ) => UseQueryResult<ServerProjects, any>;
}) {
  const { currentPage } = usePaginationHandler();
  const { query } = useHandleQueryParams();
  const { filters, countryFilters } = useAppContext();
  const searchKeyword = query.get('q');

  return (
    <DataResourceLoader
      useApiQueryHook={() =>
        useGetAllProjectsByType({
          page: currentPage,
          categoryRefs: filters,
          countries: countryFilters,
          search: searchKeyword || '',
        })
      }
      render={({ resource }) => {
        return resource?.data?.projects?.length > 0 ? (
          <AllProjectByType projects={resource.data || []}>
            <div className="mt-auto pt-3 text-[12px] ">
              <SeeMoreProjectButton />
            </div>
          </AllProjectByType>
        ) : (
          <CommonDashedBorderBox className="mt-12">
            <div>
              <img
                src={notFound}
                alt=" not found"
                className="mx-auto mb-[2rem]"
              />
              <div className="mb-4 text-center text-sm font-medium text-neutral-600">
                No project available for this query
              </div>
            </div>
          </CommonDashedBorderBox>
        );
      }}
    />
  );
}
