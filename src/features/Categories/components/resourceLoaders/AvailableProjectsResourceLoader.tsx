import { InfiniteData, UseInfiniteQueryResult } from '@tanstack/react-query';
import { SeeMoreProjectButton } from '../../../../components/ui/CategoryWidget/Project/ProjectCategoryCard';
import { ServerProjects } from '../../../../types';
import AvailableProjects from '../ui/Project/Projects/Shared/AvailableProjects';
import { DataResourceLoaderWithInfiniteQuery } from '@/components/resourceLoader/dataResourceLoaderWithInfiniteQuery';
import { forwardRef, Ref } from 'react';

export default function AvailableProjectResourceLoader({
  useGetProjects,
}: {
  useGetProjects: (
    parameter?: {},
    options?: {},
  ) => UseInfiniteQueryResult<ServerProjects, any>;
}) {
  return (
    <DataResourceLoaderWithInfiniteQuery
      useApiInfiniteQueryHook={() => useGetProjects()}
      render={forwardRef(function MyAvailableProjectsRender(
        {
          resource,
        }: {
          resource: InfiniteData<ServerProjects>['pages'];
        },
        ref: Ref<HTMLDivElement>,
      ) {
        return resource?.[0].data?.projects?.length > 0 ? (
          <AvailableProjects pages={resource || []} ref={ref}>
            <div className="mt-auto pt-3 text-[12px] ">
              <SeeMoreProjectButton />
            </div>
          </AvailableProjects>
        ) : (
          <></>
        );
      })}
    />
  );
}
