import { InfiniteData, UseInfiniteQueryResult } from '@tanstack/react-query';

import { SeeMoreProjectButton } from '../../../../components/ui/CategoryWidget/Project/ProjectCategoryCard';
import { ServerProjects } from '../../../../types';
import MyProjects from '../ui/Project/Projects/Shared/MyProjects';
import ProjectEmptyState from '../ui/Project/Projects/Shared/ProjectEmptyState';
import { DataResourceLoaderWithInfiniteQuery } from '@/components/resourceLoader/dataResourceLoaderWithInfiniteQuery';
import { forwardRef, Ref } from 'react';

export default function ProjectsResourceLoader({
  useGetProjects,
}: {
  useGetProjects: (
    parameter?: {},
    options?: {},
  ) => UseInfiniteQueryResult<ServerProjects, any>;
}) {
  return (
    <DataResourceLoaderWithInfiniteQuery
      useApiInfiniteQueryHook={() => useGetProjects()}
      render={forwardRef(function MyProjectsRender(
        {
          resource,
        }: {
          resource: InfiniteData<ServerProjects>['pages'];
        },
        ref: Ref<HTMLDivElement>,
      ) {
        return resource?.[0].data?.projects?.length > 0 ? (
          <MyProjects pages={resource || []} ref={ref}>
            <div className="mt-auto pt-3 text-[12px] ">
              <SeeMoreProjectButton />
            </div>
          </MyProjects>
        ) : (
          <ProjectEmptyState title={'My internal projects'} />
        );
      })}
    />
  );
}
