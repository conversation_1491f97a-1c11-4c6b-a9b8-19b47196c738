import { DataResourceLoader } from '../../../../components/resourceLoader/dataResourceLoader';
import ProjectCategoryCard, {
  ProjectCountry,
  ProjectInstitution,
  ProjectName,
  ProjectSdgs,
  SeeMoreProjectFromGoalsButton,
  SelectProjectButton,
} from '../../../../components/ui/CategoryWidget/Project/ProjectCategoryCard';
import SectionCase from '../../../../components/ui/CommonWidget/SectionCase';
import SlideController from '../../../../components/ui/CommonWidget/SlideController';
import { useCustomMedia } from '../../../../hooks/useCustomMedia';
import { useHandleQueryParams } from '../../../../hooks/useHandleQueryParams';
import { usePaginationHandler } from '../../../../hooks/usePaginationHandler';
import { useGetProjects } from '../../hooks/apiQueryHooks/eduQueryHooks';
import ProjectCategoryList from '../ui/Project/Projects/Shared/ProjectCategoryList';
import ProjectEmptyState from '../ui/Project/Projects/Shared/ProjectEmptyState';

export default function SelectProjectForGoalResourceLoader() {
  const { screenSize } = useCustomMedia();
  const { currentPage } = usePaginationHandler();
  const { query } = useHandleQueryParams();
  const searchKeyword = query.get('q');

  return (
    <DataResourceLoader
      useApiQueryHook={() =>
        useGetProjects({ page: currentPage, search: searchKeyword })
      }
      render={({ resource }) => {
        return resource.data?.projects?.length > 0 ? (
          <SlideController
            dataLength={resource.data?.projects?.length}
            isMobile={screenSize < 768}
          >
            <SectionCase pagination={resource.data?.pagination}>
              <div className="mt-4">
                <ProjectCategoryList projectList={resource?.data?.projects}>
                  <ProjectCategoryCard>
                    <ProjectName />
                    <ProjectSdgs />
                    <ProjectInstitution />
                    <ProjectCountry />
                    <div className="mt-auto flex w-full items-center gap-x-4 pt-3 text-[12px]">
                      <SeeMoreProjectFromGoalsButton />
                      <SelectProjectButton />
                    </div>
                  </ProjectCategoryCard>
                </ProjectCategoryList>
              </div>
            </SectionCase>
          </SlideController>
        ) : (
          <ProjectEmptyState title={'Projects'} />
        );
      }}
    />
  );
}
