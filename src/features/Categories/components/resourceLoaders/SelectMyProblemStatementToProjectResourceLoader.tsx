import { useGetProblemStatements } from '@/hooks/apiQueryHooks/eduQueryHooks';
import { DataResourceLoader } from '../../../../components/resourceLoader/dataResourceLoader';
import SectionCase from '../../../../components/ui/CommonWidget/SectionCase';

import { useHandleQueryParams } from '../../../../hooks/useHandleQueryParams';
import { usePaginationHandler } from '../../../../hooks/usePaginationHandler';
import { ProblemStatementEmptyState } from '../ui/ProblemStatement/ProblemStatementEmptyState';
import ProblemStatementCategoryCard, {
  SelectProbmlemStatmentButton,
} from '@/components/ui/CategoryWidget/ProblemStatement/ProblemStatementCategoryCard';

export default function SelectMyProblemStatementToProjectResourceLoader() {
  const { query } = useHandleQueryParams();
  const searchKeyword = query.get('q');
  const { currentPage } = usePaginationHandler();
  const getProblemStatements = useGetProblemStatements({
    page: currentPage,
    search: searchKeyword || '',
  });
  return (
    <DataResourceLoader
      useApiQueryHook={() => getProblemStatements}
      render={({ resource }) => {
        resource?.data?.problem_statement;
        return resource?.data?.problem_statement?.length > 0 ? (
          <SectionCase pagination={resource.data?.pagination}>
            <div className="mt-4 flex flex-wrap gap-3 px-2 pb-4 max-md:items-stretch">
              {resource?.data?.problem_statement?.map(problemStatement => (
                <ProblemStatementCategoryCard
                  key={problemStatement?.problemStatementRef}
                  problemStatement={problemStatement}
                >
                  <div className="mt-auto flex w-full items-center gap-x-4 pt-3 text-[12px]">
                    <SelectProbmlemStatmentButton />
                  </div>
                </ProblemStatementCategoryCard>
              ))}
            </div>
          </SectionCase>
        ) : (
          <ProblemStatementEmptyState title="Problem Statement" />
        );
      }}
    />
  );
}
