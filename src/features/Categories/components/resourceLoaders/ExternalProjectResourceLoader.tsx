import { InfiniteData, UseInfiniteQueryResult } from '@tanstack/react-query';
import { SeeMoreProjectButton } from '../../../../components/ui/CategoryWidget/Project/ProjectCategoryCard';
import { ServerProjects } from '../../../../types';
import ExternalProjects from '../ui/Project/Projects/Shared/ExternalProjects';
import { forwardRef, Ref } from 'react';
import { DataResourceLoaderWithInfiniteQuery } from '@/components/resourceLoader/dataResourceLoaderWithInfiniteQuery';

export default function ExternalProjectResourceLoader({
  useGetProjects,
}: {
  useGetProjects: (
    parameter?: {},
    options?: {},
  ) => UseInfiniteQueryResult<ServerProjects, any>;
}) {
  return (
    <DataResourceLoaderWithInfiniteQuery
      useApiInfiniteQueryHook={() => useGetProjects()}
      render={forwardRef(function MyExternalProjectsRender(
        {
          resource,
        }: {
          resource: InfiniteData<ServerProjects>['pages'];
        },
        ref: Ref<HTMLDivElement>,
      ) {
        return resource?.[0].data?.projects?.length > 0 ? (
          <ExternalProjects pages={resource || []} ref={ref}>
            <div className="mt-auto pt-3 text-[12px] ">
              <SeeMoreProjectButton />
            </div>
          </ExternalProjects>
        ) : (
          <></>
        );
      })}
    />
  );
}
