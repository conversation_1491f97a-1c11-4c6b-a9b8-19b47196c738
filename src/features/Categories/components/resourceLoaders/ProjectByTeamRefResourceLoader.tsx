import { useParams } from 'react-router-dom';

import { notFound } from '../../../../assets/images';
import ProjectCategoryCard, {
  ProjectCountry,
  ProjectInstitution,
  ProjectName,
  ProjectSdgs,
  SeeMoreProjectButton,
} from '../../../../components/ui/CategoryWidget/Project/ProjectCategoryCard';
import CommonDashedBorderBox from '../../../../components/ui/CommonWidget/CommonDashedBorderBox';
import { useHandleQueryParams } from '../../../../hooks/useHandleQueryParams';
import { useGetProjectByTeamRefInfiniteQuery } from '../../hooks/apiQueryHooks/eduQueryHooks';
import { forwardRef, Ref } from 'react';
import { InfiniteData } from '@tanstack/react-query';
import { ServerProjects } from '@/types';
import { DataResourceLoaderWithInfiniteQuery } from '@/components/resourceLoader/dataResourceLoaderWithInfiniteQuery';
import SlideController from '@/components/ui/CommonWidget/SlideController';
import { useCustomMedia } from '@/hooks/useCustomMedia';
import SectionCase from '@/components/ui/CommonWidget/SectionCase';
import ProjectCategoryListForInfinityQuery from '../ui/Project/Projects/Shared/ProjectCategoryListForInfinityQuery';

export default function ProjectByTeamRefResourceLoader() {
  const { teamRef } = useParams();
  const { query } = useHandleQueryParams();
  const { screenSize } = useCustomMedia();
  return (
    <DataResourceLoaderWithInfiniteQuery
      useApiInfiniteQueryHook={() =>
        useGetProjectByTeamRefInfiniteQuery(
          {
            teamRef:
              teamRef || query.get('teamRef') || query.get('team_ref') || '',
          },
          {
            enabled:
              !!teamRef || !!query.get('teamRef') || !!query.get('team_ref'),
          },
        )
      }
      render={forwardRef(function MyProjectsRender(
        {
          resource,
        }: {
          resource: InfiniteData<ServerProjects>['pages'];
        },
        ref: Ref<HTMLDivElement>,
      ) {
        return resource?.[0]?.data?.projects?.length > 0 ? (
          <SlideController
            dataLength={resource?.[0].data.projects.length}
            isMobile={screenSize < 768}
          >
            <SectionCase descriptionText="Projects">
              <div className="mt-4">
                <ProjectCategoryListForInfinityQuery pages={resource} ref={ref}>
                  <ProjectCategoryCard>
                    <ProjectName />
                    <ProjectSdgs />
                    <ProjectInstitution />
                    <ProjectCountry />
                    <div className="mt-auto pt-3 text-[12px] ">
                      <SeeMoreProjectButton />
                    </div>
                  </ProjectCategoryCard>
                </ProjectCategoryListForInfinityQuery>
              </div>
            </SectionCase>
          </SlideController>
        ) : (
          <CommonDashedBorderBox>
            <div>
              <img
                src={notFound}
                alt="not found"
                className="mx-auto mb-[2rem]"
              />
              <div className="mb-4 text-center text-sm font-medium text-neutral-600">
                {'No project added to this team'}
              </div>
            </div>
          </CommonDashedBorderBox>
        );
      })}
    />
  );
}
