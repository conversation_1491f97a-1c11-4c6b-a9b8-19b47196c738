import React, { createContext, useContext } from 'react'

import useAuthManager from './useAuthManager'

type UserContextType = ReturnType<typeof useAuthManager>

export const UserContext = createContext({} as UserContextType)
export const useUserContext = () => {
  const context = useContext(UserContext)
  if (!context) {
    throw new Error(`Out of context`)
  }
  return context
}
export const UserProvider = ({ children }: { children: React.ReactNode }) => {
  return (
    <UserContext.Provider value={useAuthManager()}>
      {children}
    </UserContext.Provider>
  )
}
