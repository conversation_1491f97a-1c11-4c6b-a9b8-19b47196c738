import { useKeycloak } from '@react-keycloak/web';

import { userRole } from '../../data/constants';
import { useGetUser } from '../../hooks/apiQueryHooks/userQueryHooks';
import { ServerUserBasicInfoPayload } from '../../types';
import { isNotVenturecRole } from '@/utils/helpers/is-not-venturec-role';
import { filter } from 'ramda';
export default function useAuthManager() {
  const { keycloak } = useKeycloak();
  const { data, isLoading, ...getUserResults } =
    useGetUser<ServerUserBasicInfoPayload>();
  const isLoggedIn = keycloak.authenticated;
  const emailVerified = data?.user?.emailVerified;
  const phoneNumberVerified = data?.user?.phoneNumberVerified;
  const hasStudentRole = data?.user?.roles?.includes(userRole.student);
  const hasFacultyRole = data?.user?.roles?.includes(userRole.faculty);
  const hasPractitionerRole = data?.user?.roles?.includes(
    userRole.practitioner,
  );
  const userId = data?.user?.userId;
  const hasBenefactorRole = data?.user?.roles?.includes(userRole.benefactor);
  const hasAllAccountType =
    hasStudentRole &&
    hasFacultyRole &&
    hasPractitionerRole &&
    hasBenefactorRole;
  const hasAtleastOneAccountType =
    hasStudentRole ||
    hasFacultyRole ||
    hasPractitionerRole ||
    hasBenefactorRole;

  const isTalent = data?.user?.talent;
  const isMentor = data?.user?.mentor;
  const subscriptionType = data?.subscription?.plan?.subscriptionType;
  const subscriptionStatus = data?.subscription?.status;
  const subscription = data?.subscription;
  const roles = filter(isNotVenturecRole)(data?.user?.roles || []);

  return {
    isLoggedIn,
    emailVerified,
    phoneNumberVerified,
    isLoading,
    data,
    hasStudentRole,
    hasFacultyRole,
    isMentor,
    hasPractitionerRole,
    hasAllAccountType,
    hasAtleastOneAccountType,
    subscriptionType,
    hasBenefactorRole,
    subscriptionStatus,
    subscription,
    roles,
    isTalent,
    userId,
    ...getUserResults,
  };
}
