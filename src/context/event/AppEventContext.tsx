import { createContext, ReactNode, useContext } from 'react'

import useAppContextStateAndActions from './useAppContextStateAndActions'

type AppEventContextType = ReturnType<typeof useAppContextStateAndActions>

const AppEventContext = createContext({} as AppEventContextType)
export const useAppContext = () => {
  const context = useContext(AppEventContext)
  if (!context) {
    throw new Error(`Out of context`)
  }
  return context
}
export const AppEventProvider = ({ children }: { children: ReactNode }) => {
  return (
    <AppEventContext.Provider value={useAppContextStateAndActions()}>
      {children}
    </AppEventContext.Provider>
  )
}
