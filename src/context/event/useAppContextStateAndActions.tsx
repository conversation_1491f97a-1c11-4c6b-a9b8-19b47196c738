import { SetStateAction, useCallback, useMemo, useState } from 'react';

import { mode, ShowModal, UserRoleType } from '../../types';
import { Helper } from '../../utils/helpers';
import { IBillingPayload } from '@/features/Payment/types';
import { userRole } from '@/data/constants';
import useAuthManager from '../user/useAuthManager';
type UserRole = typeof userRole;

const toggleArrayElement = (value: string) => (previous: string[]) => {
  if (previous.includes(value)) {
    const updatedArray = previous.filter(element => element !== value);
    return updatedArray;
  }
  return [...previous, value];
};

const getFirstAccountTypeButNotUserAccountTypeFromUserData = (
  roles: UserRoleType[],
  userRole: UserRole,
) => {
  return roles.find((role: UserRoleType) => role !== userRole.user) || '';
};

export default function useAppContextStateAndActions() {
  const { roles } = useAuthManager();

  const [opened, setOpened] = useState(true);
  const [isUserAccountTypeChoice, setIsUserAccountTypeChoice] = useState(false);
  const [mode, setMode] = useState<mode>('false');
  const [showModal, setShowModal] = useState<ShowModal>('');
  const [screenSize, setScreenSize] = useState(window.innerWidth);
  const [assigneeDropdown, setAssigneeDropdown] = useState('');
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [currentAccountType, setCurrentAccountType] = useState<
    UserRoleType | ''
  >(Helper.getLocalUserAccountType() || '');

  const [billingInformationData, setBillingInformationData] =
    useState<IBillingPayload>(Helper.getBillingInformationData());
  const [grantRef, setGrantRef] = useState<string>(Helper.getGrantRef());
  const [previousUrl, setPreviousUrl] = useState<string | null>(
    Helper.getLocalPreviousUrl(),
  );
  const [filters, SetFilters] = useState<string[]>([]);
  const [countryFilters, setCountryFilters] = useState<string[]>([]);
  const [sdgFilters, setSdgFilters] = useState<string[]>([]);
  const [industryFilters, setIndustriesFilters] = useState<string[]>([]);
  const [facultyFilters, setFacultyFilters] = useState<string[]>([]);
  const [departmentFilters, setDepartmentFilters] = useState<string[]>([]);
  const [universityFilters, setUniversityFilters] = useState<string[]>([]);

  const toggleTheme = useCallback((mode: mode) => {
    if (mode === 'false') {
      setMode('true');
      localStorage.setItem('mode', 'true');
    } else {
      setMode('false');
      localStorage.setItem('mode', 'false');
    }
  }, []);
  const setCurrentAccountTypeHandler = useCallback(
    (value: React.SetStateAction<UserRoleType | ''>) => {
      setCurrentAccountType(value);
      Helper.setLocalUserAccountType(value as UserRoleType);
    },
    [],
  );
  const setBillingInformationHandler = useCallback(
    (value: React.SetStateAction<IBillingPayload>) => {
      setBillingInformationData(value);
      Helper.setBillingInformationData(value as IBillingPayload);
    },
    [],
  );
  const setGrantRefHandler = useCallback((value: string) => {
    setGrantRef(value);
    Helper.setGrantRef(value);
  }, []);

  const setPreviousUrlHandler = useCallback((value: string) => {
    setPreviousUrl(value);
    Helper.setLocalPreviousUrl(value);
  }, []);
  const setShowModalHandler = useCallback(
    (action: ShowModal) => setShowModal(action),
    [],
  );
  const setOpenedHandler = useCallback(
    (value: SetStateAction<boolean>) => setOpened(value),
    [],
  );
  const setIsUserAccountTypeChoiceHandler = useCallback(
    (value: SetStateAction<boolean>) => setIsUserAccountTypeChoice(value),
    [],
  );
  const setScreenSizeHandler = useCallback(
    (value: number) => setScreenSize(value),
    [],
  );
  const setUploadProgressHandler = useCallback(
    (value: SetStateAction<number>) => setUploadProgress(value),
    [],
  );

  const setAssigneeDropdownHandler = useCallback((value: string) => {
    setAssigneeDropdown(value);
  }, []);
  const setModeHandler = useCallback((action: mode) => setMode(action), []);

  const handleSetFilters = useCallback((value: string) => {
    SetFilters(toggleArrayElement(value));
  }, []);
  const handleSetCountriesFilters = useCallback((value: string) => {
    setCountryFilters(toggleArrayElement(value));
  }, []);
  const handleSetSdgFilters = useCallback((value: string) => {
    setSdgFilters(toggleArrayElement(value));
  }, []);
  const handleSetIndustriesFilters = useCallback((value: string) => {
    setIndustriesFilters(toggleArrayElement(value));
  }, []);
  const handleSetFacultyFilters = useCallback((value: string) => {
    setFacultyFilters(toggleArrayElement(value));
  }, []);
  const handleSetDepartmentFilters = useCallback((value: string) => {
    setDepartmentFilters(toggleArrayElement(value));
  }, []);
  const handleSetUniversitiesFilters = useCallback((value: string) => {
    setUniversityFilters(toggleArrayElement(value));
  }, []);
  const resetCountriesFilters = useCallback(() => {
    setCountryFilters([]);
  }, []);
  const resetDepartmentFilters = useCallback(() => {
    setDepartmentFilters([]);
  }, []);
  const resetFacultyFilters = useCallback(() => {
    setFacultyFilters([]);
  }, []);
  const resetUniversitiesFilters = useCallback(() => {
    setUniversityFilters([]);
  }, []);
  const resetFilters = useCallback(() => {
    SetFilters([]);
  }, []);
  const resetSdgFilters = useCallback(() => {
    setSdgFilters([]);
  }, []);

  const isStudentOrFaculty = useMemo(() => {
    if (
      currentAccountType === userRole.student ||
      currentAccountType === userRole.faculty
    ) {
      return true;
    }
    return false;
  }, [currentAccountType]);
  return {
    handleSetFilters,
    filters,
    isUserAccountTypeChoice,
    setIsUserAccountTypeChoiceHandler,
    screenSize,
    setScreenSizeHandler,
    toggleTheme,
    mode,
    setModeHandler,
    setShowModalHandler,
    showModal,
    setOpenedHandler,
    countryFilters,
    handleSetCountriesFilters,
    opened,
    currentAccountType:
      currentAccountType.toLowerCase() ||
      getFirstAccountTypeButNotUserAccountTypeFromUserData(
        roles,
        userRole,
      ).toLowerCase(),
    setCurrentAccountTypeHandler,
    setPreviousUrlHandler,
    previousUrl,
    assigneeDropdown,
    setAssigneeDropdownHandler,
    uploadProgress,
    setUploadProgressHandler,
    universityFilters,
    handleSetUniversitiesFilters,
    resetCountriesFilters,
    resetUniversitiesFilters,
    resetFilters,
    grantRef,
    setGrantRefHandler,
    setBillingInformationHandler,
    billingInformationData,
    isStudentOrFaculty,
    facultyFilters,
    handleSetFacultyFilters,
    resetFacultyFilters,
    departmentFilters,
    handleSetDepartmentFilters,
    resetDepartmentFilters,
    handleSetSdgFilters,
    sdgFilters,
    resetSdgFilters,
    industryFilters,
    handleSetIndustriesFilters,
  };
}
