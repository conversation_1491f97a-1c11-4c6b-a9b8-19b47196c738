import { ReactNode, createContext, useContext } from 'react';
import { useCartContextStateAndActions } from './useCartContextStateAndActions';
import { PlanType } from '@/types';

export interface CartState {
  items: PlanType[];
}
export const INITIAL_STATE: CartState = {
  items: [],
};

const CartContext = createContext(
  {} as ReturnType<typeof useCartContextStateAndActions>,
);

export const useCartContext = () => {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error(`Out of context`);
  }
  return context;
};

export const CartProvider = ({ children }: { children: ReactNode }) => {
  return (
    <CartContext.Provider value={useCartContextStateAndActions(INITIAL_STATE)}>
      {children}
    </CartContext.Provider>
  );
};
