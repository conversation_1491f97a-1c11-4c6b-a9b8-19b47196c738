import { CartActionType } from '@/enums/ActionType';
import { CartState } from './cartContext';
import { PlanType } from '@/types';

type CartAction =
  | { type: CartActionType.ADD_ITEM; payload: PlanType }
  | { type: CartActionType.REMOVE_ITEM; payload: PlanType }
  | { type: CartActionType.CLEAR_CART };

const addItemToCart = (
  state: CartState,
  action: { type: CartActionType.ADD_ITEM; payload: PlanType },
) => {
  if (state?.items?.find(item => item.name === action.payload.name) == null) {
    return [...state.items, { ...action.payload }];
  }
  return state?.items?.reduce((acc: PlanType[], item: PlanType) => {
    if (item.name === action.payload.name) {
      const quantity = item.quantity + action.payload.quantity;
      return [...acc, { ...item, quantity: quantity }];
    }
    return [...acc, { ...item }];
  }, []);
};

const removeItemFromCart = (
  state: CartState,
  action: {
    type: CartActionType.REMOVE_ITEM;
    payload: PlanType;
  },
) => {
  return state.items.reduce((acc: PlanType[], item: PlanType) => {
    if (item.name === action.payload.name) {
      const newQuantity = item.quantity - action.payload.quantity;

      return newQuantity > 0
        ? [...acc, { ...item, quantity: newQuantity }]
        : [...acc];
    }
    return [...acc, item];
  }, []);
};

export const reducer = (state: CartState, action: CartAction) => {
  switch (action.type) {
    case CartActionType.ADD_ITEM:
      return { ...state, items: addItemToCart(state, action) };
    case CartActionType.REMOVE_ITEM:
      return { ...state, items: removeItemFromCart(state, action) };
    case CartActionType.CLEAR_CART:
      return { ...state, items: [] };
    default:
      return state;
  }
};
