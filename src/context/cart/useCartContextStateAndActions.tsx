import { useCallback, useMemo, useReducer } from 'react';
import { reducer } from './reducer';
import { CartState } from './cartContext';
import { CartActionType } from '@/enums/ActionType';
import { PlanType } from '@/types';

const cartItemsTotalPrice = (items: PlanType[]): number => {
  if (items.length === 0) return 0;
  const itemCost = items?.reduce((total, item) => {
    return total + item.price * item.quantity;
  }, 0);
  return itemCost;
};

export const useCartContextStateAndActions = (initialCart: CartState) => {
  const [state, dispatch] = useReducer(reducer, initialCart);

  const addItemHandler = useCallback(
    (item: Omit<PlanType, 'quantity'>, quantity = 1) => {
      dispatch({
        type: CartActionType.ADD_ITEM,
        payload: { ...item, quantity },
      });
    },
    [],
  );

  const removeItemHandler = useCallback(
    (item: Omit<PlanType, 'quantity'>, quantity = 1) => {
      dispatch({
        type: CartActionType.REMOVE_ITEM,
        payload: { ...item, quantity },
      });
    },
    [],
  );
  const clearCart = useCallback(() => {
    dispatch({ type: CartActionType.CLEAR_CART });
  }, []);
  const cartItemsTotal = useMemo(
    () => cartItemsTotalPrice(state.items).toFixed(2),
    [state],
  );
  const itemsCount = state.items?.reduce((acc, item) => acc + item.quantity, 0);
  return {
    state,
    itemsCount,
    addItemHandler,
    removeItemHandler,
    cartItemsTotal,
    clearCart,
  };
};
