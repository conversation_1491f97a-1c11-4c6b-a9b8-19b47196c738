import { useKeycloak } from '@react-keycloak/web';
import { useEffect, useState } from 'react';
import { io as ClientIO, Socket } from 'socket.io-client';
import { SOCKET_URL } from '../../utils/apiUrls';
import { isDevEnvironment } from '@/features/UserOnboarding/utils/helper';

export default function useSocketContextStateAndActions() {
  const { keycloak } = useKeycloak();
  const [socket, setSocket] = useState<Socket<any, any> | null>(null);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    if (!keycloak?.token || !isDevEnvironment()) {
      return;
    }
    const socketInstance: Socket<any, any> = ClientIO(SOCKET_URL!, {
      query: {
        accessToken: keycloak?.token || import.meta.env.VITE_DEV_ACCESS_TOKEN,
      },
      addTrailingSlash: false,
      timeout: 120000,
    });
    if (!socketInstance) {
      return;
    }

    socketInstance.on('connect', () => {
      setIsConnected(true);
    });

    socketInstance.on('disconnect', () => {
      setIsConnected(false);
    });

    window.addEventListener('focus', () => {
      socketInstance.emit('CONNECTED', () => {
        setIsConnected(true);
      });
    });

    window.addEventListener('blur', () => {
      socketInstance.emit('DISCONNECTED', () => {
        setIsConnected(false);
      });
    });

    setSocket(socketInstance);

    return () => {
      socketInstance.disconnect();
      window.removeEventListener('focus', () =>
        socketInstance.emit('CONNECTED', () => {
          setIsConnected(true);
        }),
      );
      window.removeEventListener('blur', () =>
        socketInstance.emit('DISCONNECTED', () => {
          setIsConnected(false);
        }),
      );
    };
  }, [setIsConnected]);

  return {
    socket,
    isConnected,
  };
}
