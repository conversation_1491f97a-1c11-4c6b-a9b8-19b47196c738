import { createContext, useContext } from 'react'

import useSocketContextStateAndActions from './useSocketContextStateAndActions'

type SocketContextType = ReturnType<typeof useSocketContextStateAndActions>

export const SocketContext = createContext({} as SocketContextType)

export const useSocketContext = () => {
  const context = useContext(SocketContext)
  if (!context) {
    throw new Error(`Out of context`)
  }
  return context
}

export const SocketProvider = ({ children }: { children: React.ReactNode }) => {
  return (
    <SocketContext.Provider value={useSocketContextStateAndActions()}>
      {children}
    </SocketContext.Provider>
  )
}
