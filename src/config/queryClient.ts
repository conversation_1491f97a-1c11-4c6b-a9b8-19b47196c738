import {
  getNetworkConnectionType,
  isSlowNetwork,
} from '@/utils/helpers/network-utils';
import { QueryClient } from '@tanstack/react-query';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      cacheTime: 15 * 60 * 1000,
      retry: 3,
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
      networkMode: 'always',
    },
    mutations: {
      retry: failureCount => {
        const { effectiveType } = getNetworkConnectionType();
        const slowNetwork = isSlowNetwork(effectiveType);
        return slowNetwork ? failureCount < 1 : failureCount < 3;
      },
    },
  },
});
