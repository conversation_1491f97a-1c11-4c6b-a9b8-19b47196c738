import { loadStripe } from '@stripe/stripe-js';

import { STRIPE_PUBLISHABLE_KEY } from '../utils/stripeUrls';

export const stripePromise = loadStripe(STRIPE_PUBLISHABLE_KEY);
export const appearance = {
  theme: 'stripe' as const,
  variables: {
    colorPrimary: '#ff3e00',
    colorBackground: '#fbfafa',
    colorText: 'black',
    fontFamily: `Spartan, sans-serif`,
    borderRadius: '6px',
    border: '1px solid #d9d9d9',
    spacingGridRow: '24px',
  },
  rules: {
    '.Termstext': {
      display: 'none',
    },
    '.Input': {
      border: '1px solid #d9d9d9',
      borderRadius: '6px',
    },
    '.Input:focus': {
      outline: 'none',
      border: '1px solid #ff3e00',
      background: '#fbfafa',
      boxShadow: 'none',
    },
    '.Input:hover': {
      outline: 'none',
      border: '1px solid #ff3e00',
      background: '#fbfafa',
      boxShadow: 'none',
    },
    '.Input--invalid': {
      boxShadow: '0 1px 1px 0 rgba(0, 0, 0, 0.07), 0 0 0 2px rgb(153, 27, 27)',
    },
  },
};

export const paymentElementOptions = {
  layout: 'tabs',
  terms: {
    card: 'never',
  },
} as const;
