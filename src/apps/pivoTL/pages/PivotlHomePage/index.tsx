import { Link } from 'react-router-dom';

const HomePage = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-white">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="mx-auto max-w-7xl px-4 py-24 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="mb-6 text-4xl font-semibold text-gray-900 md:text-6xl">
              The Agentic AI
              <br />
              Transformation Hub
            </h1>
            <p className="mx-auto mb-8 max-w-3xl text-xl text-gray-600">
              Orchestrate, evolve, and deploy AI agents across your enterprise.
              PivoTL is the transformation layer that powers agentic workflows,
              augments human roles, and accelerates value creation.
            </p>
            <div className="flex flex-col justify-center gap-4 sm:flex-row">
              <Link
                to="/pivotal/marketplace"
                className="rounded-lg bg-orange-600 px-8 py-3 font-semibold text-white transition-colors hover:bg-orange-700"
              >
                Chat with Pivo
              </Link>
              <Link
                to="/pivotal/create-agent"
                className="rounded-lg border border-gray-900 px-8 py-3 font-semibold text-gray-900 transition-colors hover:border-primary hover:bg-orange-50 hover:text-primary"
              >
                Regsiter for Free
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* AI Agents Section */}
      <section className="bg-white py-20">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="mb-16 text-center">
            <h2 className="mb-4 text-3xl font-bold capitalize text-gray-900">
              Meet the AI Agent teams
            </h2>
            <p className="mx-auto max-w-2xl text-lg text-gray-600">
              Each PivoTL Agent Team is a suite of purpose-trained AI agents
              designed to own and resolve key enterprise workflows.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            {/* AI agents cards */}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="mb-20 bg-gray-900 py-20 text-white">
        <div className="mx-auto max-w-7xl px-4 text-center sm:px-6 lg:px-8">
          <h2 className="mb-4 text-5xl font-bold">
            Ready to Activate Your First AI Agent?
          </h2>
          <p className="mx-auto mb-8 max-w-2xl text-lg text-gray-300">
            Start with one. Expand on your terms. Watch transformation compound.
          </p>
          <div className="flex flex-col justify-center gap-4 sm:flex-row">
            <Link
              to="/pivotal/agents"
              className="inline-block rounded-lg bg-orange-600 px-8 py-3 font-semibold text-white transition-colors hover:bg-orange-700"
            >
              Browse Agents
            </Link>
            <Link
              to="/pivotal/demo"
              className="rounded-lg border border-white px-8 py-3 font-semibold text-white transition-colors hover:border-primary hover:bg-orange-50 hover:text-primary"
            >
              Schedule a Demo
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
