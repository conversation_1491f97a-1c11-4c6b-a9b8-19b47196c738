import { lazy } from 'react';
import { withSuspense } from '@/components/hocs/suspense/withSuspense';
import { Navigate } from 'react-router-dom';

// Lazy load pages for better performance
const PivotlHomePage = withSuspense(
  lazy(() => import('../../pages/PivotlHomePage')),
);

export const PivotlShellRoutes = [
  {
    index: true, // /pivotl
    element: <PivotlHomePage />,
  },
  {
    path: '*',
    element: <Navigate to="/pivotl" />,
  },
];
