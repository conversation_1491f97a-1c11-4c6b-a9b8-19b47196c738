// {
//   "files": ["src"],
//   "references": [
//     { "path": "./tsconfig.app.json" },
//     { "path": "./tsconfig.node.json" }
//   ]
// }

{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@shared/*": ["./src/shared/*"],
      "@upivotal/*": ["./src/apps/upivotal/*"],
      "@pivotl/*": ["src/apps/pivoTL/*"]
    },
    "typeRoots": ["./src/types", "./node_modules/@types"],
    "target": "es2021",
    "useDefineForClassFields": true,
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
    "lib": ["es2021", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,

    /* extra */
    "allowJs": true,
    "forceConsistentCasingInFileNames": true,
    "esModuleInterop": true,

    "incremental": true,

    /**/
    "composite": true,
    "allowSyntheticDefaultImports": true
  },
  "include": [
    "src",
    "**/*.ts",
    "**/*.tsx",
    "src/test/setup-test-environment.ts"
  ]
}
